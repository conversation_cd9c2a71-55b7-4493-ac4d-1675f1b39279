import { EventEmitter } from 'events';
import { KickBot } from '../bot/KickBot';
export interface Giveaway {
    id: number;
    title: string;
    description?: string;
    prize: string;
    duration: number;
    maxEntries: number;
    entryCost: number;
    status: 'active' | 'ended' | 'cancelled';
    winner?: string;
    createdBy: string;
    createdAt: Date;
    endedAt?: Date;
    entries: GiveawayEntry[];
}
export interface GiveawayEntry {
    username: string;
    entries: number;
    timestamp: Date;
}
export declare class GiveawaySystem extends EventEmitter {
    private bot;
    private activeGiveaways;
    private giveawayTimers;
    constructor(bot: KickBot);
    private setupEventHandlers;
    private loadActiveGiveaways;
    private loadGiveawayWithEntries;
    createGiveaway(title: string, prize: string, duration: number, createdBy: string, options?: {
        description?: string;
        maxEntries?: number;
        entryCost?: number;
    }): Promise<{
        success: boolean;
        giveaway?: Giveaway;
        message: string;
    }>;
    enterGiveaway(username: string, giveawayId?: number): Promise<{
        success: boolean;
        message: string;
    }>;
    endGiveaway(giveawayId: number, forcedBy?: string): Promise<{
        success: boolean;
        winner?: string;
        message: string;
    }>;
    cancelGiveaway(giveawayId: number, cancelledBy: string): Promise<{
        success: boolean;
        message: string;
    }>;
    getActiveGiveaways(): Giveaway[];
    getGiveaway(giveawayId: number): Giveaway | undefined;
    private scheduleGiveawayEnd;
    private clearGiveawayTimer;
    getStats(): Promise<any>;
    cleanup(): void;
}
//# sourceMappingURL=GiveawaySystem.d.ts.map