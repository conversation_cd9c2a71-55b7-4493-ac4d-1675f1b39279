export interface KickUser {
    id: number;
    username: string;
    slug: string;
    is_staff: boolean;
    is_channel_owner: boolean;
    is_moderator: boolean;
    is_vip: boolean;
    is_subscriber: boolean;
    is_follower: boolean;
    badges?: Badge[];
}
export interface Badge {
    type: string;
    text: string;
    count?: number;
}
export interface ChatMessage {
    id: string;
    chatroom_id: number;
    content: string;
    type: string;
    created_at: string;
    sender: KickUser;
    metadata?: any;
}
export interface Command {
    name: string;
    description: string;
    usage: string;
    aliases: string[];
    cooldown: number;
    permission: PermissionLevel;
    enabled: boolean;
    response?: string;
    action?: string;
    created_by: string;
    created_at: string;
    updated_at: string;
}
export declare enum PermissionLevel {
    EVERYONE = 0,
    FOLLOWER = 1,
    SUBSCRIBER = 2,
    VIP = 3,
    MODERATOR = 4,
    OWNER = 5,
    BOT_ADMIN = 6
}
export interface BotConfig {
    username: string;
    password?: string;
    token?: string;
    channel: string;
    prefix: string;
    features: {
        moderation: boolean;
        commands: boolean;
        songRequests: boolean;
        games: boolean;
        analytics: boolean;
    };
    moderation: {
        maxMessageLength: number;
        spamThreshold: number;
        capsThreshold: number;
        linkProtection: boolean;
        bannedWords: string[];
        autoTimeout: boolean;
        timeoutDuration: number;
    };
}
export interface SongRequest {
    id: string;
    title: string;
    artist: string;
    duration: number;
    url: string;
    requested_by: string;
    requested_at: string;
    platform: 'youtube' | 'spotify' | 'soundcloud';
}
export interface GameResult {
    user: string;
    game: string;
    result: any;
    timestamp: string;
}
export interface UserStats {
    username: string;
    messages_sent: number;
    commands_used: number;
    time_in_chat: number;
    points: number;
    last_seen: string;
    is_active: boolean;
}
export interface BotEvent {
    type: 'message' | 'join' | 'leave' | 'follow' | 'subscribe' | 'ban' | 'timeout';
    data: any;
    timestamp: string;
}
export interface WebSocketMessage {
    event: string;
    data: any;
    channel?: string;
}
//# sourceMappingURL=index.d.ts.map