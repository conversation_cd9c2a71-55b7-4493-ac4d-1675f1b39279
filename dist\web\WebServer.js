"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebServer = void 0;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const path_1 = __importDefault(require("path"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const bcrypt_1 = __importDefault(require("bcrypt"));
const config_1 = require("../config");
const logger_1 = require("../utils/logger");
const Database_1 = require("../database/Database");
class WebServer {
    constructor(bot) {
        this.bot = bot;
        this.app = (0, express_1.default)();
        this.setupMiddleware();
        this.setupRoutes();
    }
    setupMiddleware() {
        this.app.use((0, helmet_1.default)({
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
                    scriptSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
                    imgSrc: ["'self'", "data:", "https:"],
                },
            },
        }));
        this.app.use((0, cors_1.default)({
            origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
            credentials: true,
        }));
        this.app.use(express_1.default.json({ limit: '10mb' }));
        this.app.use(express_1.default.urlencoded({ extended: true }));
        this.app.use('/static', express_1.default.static(path_1.default.join(__dirname, '../web/public')));
        this.app.use((req, res, next) => {
            logger_1.logger.debug(`${req.method} ${req.path}`, {
                ip: req.ip,
                userAgent: req.get('User-Agent')
            });
            next();
        });
    }
    setupRoutes() {
        this.app.get('/api/health', (req, res) => {
            res.json({
                status: 'ok',
                timestamp: new Date().toISOString(),
                botRunning: this.bot.running
            });
        });
        this.app.post('/api/auth/login', this.handleLogin.bind(this));
        this.app.post('/api/auth/logout', this.handleLogout.bind(this));
        this.app.get('/api/auth/me', this.authenticateToken.bind(this), this.handleMe.bind(this));
        this.app.use('/api', this.authenticateToken.bind(this));
        this.app.get('/api/bot/status', this.getBotStatus.bind(this));
        this.app.get('/api/bot/stats', this.getBotStats.bind(this));
        this.app.post('/api/bot/restart', this.restartBot.bind(this));
        this.app.get('/api/commands', this.getCommands.bind(this));
        this.app.post('/api/commands', this.createCommand.bind(this));
        this.app.put('/api/commands/:name', this.updateCommand.bind(this));
        this.app.delete('/api/commands/:name', this.deleteCommand.bind(this));
        this.app.get('/api/moderation/stats', this.getModerationStats.bind(this));
        this.app.get('/api/moderation/logs', this.getModerationLogs.bind(this));
        this.app.post('/api/moderation/banned-words', this.addBannedWord.bind(this));
        this.app.delete('/api/moderation/banned-words/:word', this.removeBannedWord.bind(this));
        this.app.get('/api/currency/stats', this.getCurrencyStats.bind(this));
        this.app.get('/api/currency/leaderboard', this.getCurrencyLeaderboard.bind(this));
        this.app.post('/api/currency/give', this.givePoints.bind(this));
        this.app.get('/api/giveaways', this.getGiveaways.bind(this));
        this.app.post('/api/giveaways', this.createGiveaway.bind(this));
        this.app.post('/api/giveaways/:id/end', this.endGiveaway.bind(this));
        this.app.delete('/api/giveaways/:id', this.cancelGiveaway.bind(this));
        this.app.get('/api/users', this.getUsers.bind(this));
        this.app.get('/api/users/:username', this.getUser.bind(this));
        this.app.get('/api/chat/logs', this.getChatLogs.bind(this));
        this.app.get('/', (req, res) => {
            res.sendFile(path_1.default.join(__dirname, '../web/public/index.html'));
        });
        this.app.get('*', (req, res) => {
            if (req.path.startsWith('/api/')) {
                res.status(404).json({ error: 'API endpoint not found' });
            }
            else {
                res.sendFile(path_1.default.join(__dirname, '../web/public/index.html'));
            }
        });
        this.app.use((err, req, res, next) => {
            logger_1.logger.error('Web server error:', err);
            res.status(500).json({ error: 'Internal server error' });
        });
    }
    authenticateToken(req, res, next) {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1];
        if (!token) {
            res.status(401).json({ error: 'Access token required' });
            return;
        }
        jsonwebtoken_1.default.verify(token, config_1.webConfig.jwtSecret, (err, user) => {
            if (err) {
                res.status(403).json({ error: 'Invalid token' });
                return;
            }
            req.user = user;
            next();
        });
    }
    async handleLogin(req, res) {
        try {
            const { username, password } = req.body;
            if (!username || !password) {
                res.status(400).json({ error: 'Username and password required' });
                return;
            }
            if (username === config_1.webConfig.adminUsername) {
                const isValid = await bcrypt_1.default.compare(password, await bcrypt_1.default.hash(config_1.webConfig.adminPassword, 10));
                if (isValid || password === config_1.webConfig.adminPassword) {
                    const token = jsonwebtoken_1.default.sign({ username, isAdmin: true }, config_1.webConfig.jwtSecret, { expiresIn: '24h' });
                    res.json({
                        token,
                        user: { username, isAdmin: true },
                        expiresIn: '24h'
                    });
                    return;
                }
            }
            res.status(401).json({ error: 'Invalid credentials' });
        }
        catch (error) {
            logger_1.logger.error('Login error:', error);
            res.status(500).json({ error: 'Login failed' });
        }
    }
    handleLogout(req, res) {
        res.json({ message: 'Logged out successfully' });
    }
    handleMe(req, res) {
        res.json({ user: req.user });
    }
    getBotStatus(req, res) {
        const status = {
            running: this.bot.running,
            connected: this.bot.botInstance.connected,
            uptime: this.bot.botInstance.getStats().uptimeFormatted,
            channel: this.bot.botInstance.currentChannel?.slug,
        };
        res.json(status);
    }
    async getBotStats(req, res) {
        try {
            const stats = await this.bot.getStats();
            res.json(stats);
        }
        catch (error) {
            logger_1.logger.error('Failed to get bot stats:', error);
            res.status(500).json({ error: 'Failed to get statistics' });
        }
    }
    async restartBot(req, res) {
        try {
            logger_1.logger.info(`Bot restart requested by ${req.user?.username}`);
            await this.bot.stop();
            await this.bot.start();
            res.json({ message: 'Bot restarted successfully' });
        }
        catch (error) {
            logger_1.logger.error('Failed to restart bot:', error);
            res.status(500).json({ error: 'Failed to restart bot' });
        }
    }
    async start() {
        return new Promise((resolve, reject) => {
            try {
                this.server = this.app.listen(config_1.webConfig.port, () => {
                    logger_1.logger.info(`🌐 Web dashboard started on http://localhost:${config_1.webConfig.port}`);
                    resolve();
                });
                this.server.on('error', (error) => {
                    logger_1.logger.error('Web server error:', error);
                    reject(error);
                });
            }
            catch (error) {
                reject(error);
            }
        });
    }
    async getCommands(req, res) {
        try {
            const commands = await Database_1.database.getAllCommands();
            res.json(commands);
        }
        catch (error) {
            logger_1.logger.error('Failed to get commands:', error);
            res.status(500).json({ error: 'Failed to get commands' });
        }
    }
    async createCommand(req, res) {
        try {
            const { name, response, description, cooldown, permission } = req.body;
            if (!name || !response) {
                res.status(400).json({ error: 'Name and response are required' });
                return;
            }
            const success = await this.bot.features.customCommands?.createCommand(name, response, {
                description,
                cooldown,
                permission,
                createdBy: req.user?.username || 'web-admin',
            });
            if (success) {
                res.json({ message: 'Command created successfully' });
            }
            else {
                res.status(400).json({ error: 'Failed to create command' });
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to create command:', error);
            res.status(500).json({ error: 'Failed to create command' });
        }
    }
    async updateCommand(req, res) {
        try {
            const { name } = req.params;
            const updates = req.body;
            const success = await this.bot.features.customCommands?.updateCommand(name, updates);
            if (success) {
                res.json({ message: 'Command updated successfully' });
            }
            else {
                res.status(404).json({ error: 'Command not found' });
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to update command:', error);
            res.status(500).json({ error: 'Failed to update command' });
        }
    }
    async deleteCommand(req, res) {
        try {
            const { name } = req.params;
            const success = await this.bot.features.customCommands?.deleteCommand(name);
            if (success) {
                res.json({ message: 'Command deleted successfully' });
            }
            else {
                res.status(404).json({ error: 'Command not found' });
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to delete command:', error);
            res.status(500).json({ error: 'Failed to delete command' });
        }
    }
    getModerationStats(req, res) {
        try {
            const stats = this.bot.features.moderation?.getModerationStats();
            res.json(stats || {});
        }
        catch (error) {
            logger_1.logger.error('Failed to get moderation stats:', error);
            res.status(500).json({ error: 'Failed to get moderation stats' });
        }
    }
    async getModerationLogs(req, res) {
        try {
            const limit = parseInt(req.query.limit) || 50;
            const logs = await Database_1.database.db.all(`
        SELECT * FROM moderation_logs
        ORDER BY timestamp DESC
        LIMIT ?
      `, [limit]);
            res.json(logs);
        }
        catch (error) {
            logger_1.logger.error('Failed to get moderation logs:', error);
            res.status(500).json({ error: 'Failed to get moderation logs' });
        }
    }
    addBannedWord(req, res) {
        try {
            const { word } = req.body;
            if (!word) {
                res.status(400).json({ error: 'Word is required' });
                return;
            }
            this.bot.features.moderation?.addBannedWord(word);
            res.json({ message: 'Banned word added successfully' });
        }
        catch (error) {
            logger_1.logger.error('Failed to add banned word:', error);
            res.status(500).json({ error: 'Failed to add banned word' });
        }
    }
    removeBannedWord(req, res) {
        try {
            const { word } = req.params;
            const success = this.bot.features.moderation?.removeBannedWord(word);
            if (success) {
                res.json({ message: 'Banned word removed successfully' });
            }
            else {
                res.status(404).json({ error: 'Banned word not found' });
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to remove banned word:', error);
            res.status(500).json({ error: 'Failed to remove banned word' });
        }
    }
    async getCurrencyStats(req, res) {
        try {
            const stats = await this.bot.features.currency?.getStats();
            res.json(stats || {});
        }
        catch (error) {
            logger_1.logger.error('Failed to get currency stats:', error);
            res.status(500).json({ error: 'Failed to get currency stats' });
        }
    }
    async getCurrencyLeaderboard(req, res) {
        try {
            const limit = parseInt(req.query.limit) || 10;
            const leaderboard = await this.bot.features.currency?.getLeaderboard(limit);
            res.json(leaderboard || []);
        }
        catch (error) {
            logger_1.logger.error('Failed to get currency leaderboard:', error);
            res.status(500).json({ error: 'Failed to get currency leaderboard' });
        }
    }
    async givePoints(req, res) {
        try {
            const { username, amount, reason } = req.body;
            if (!username || !amount) {
                res.status(400).json({ error: 'Username and amount are required' });
                return;
            }
            const success = await this.bot.features.currency?.addPoints(username, amount, reason || `Admin gift by ${req.user?.username}`);
            if (success) {
                res.json({ message: 'Points given successfully' });
            }
            else {
                res.status(400).json({ error: 'Failed to give points' });
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to give points:', error);
            res.status(500).json({ error: 'Failed to give points' });
        }
    }
    async getGiveaways(req, res) {
        try {
            const activeGiveaways = this.bot.features.giveaways?.getActiveGiveaways() || [];
            const stats = await this.bot.features.giveaways?.getStats();
            res.json({
                active: activeGiveaways,
                stats: stats || {}
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to get giveaways:', error);
            res.status(500).json({ error: 'Failed to get giveaways' });
        }
    }
    async createGiveaway(req, res) {
        try {
            const { title, prize, duration, description, maxEntries, entryCost } = req.body;
            if (!title || !prize || !duration) {
                res.status(400).json({ error: 'Title, prize, and duration are required' });
                return;
            }
            const result = await this.bot.features.giveaways?.createGiveaway(title, prize, duration, req.user?.username || 'web-admin', { description, maxEntries, entryCost });
            if (result?.success) {
                res.json({ message: 'Giveaway created successfully', giveaway: result.giveaway });
            }
            else {
                res.status(400).json({ error: result?.message || 'Failed to create giveaway' });
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to create giveaway:', error);
            res.status(500).json({ error: 'Failed to create giveaway' });
        }
    }
    async endGiveaway(req, res) {
        try {
            const giveawayId = parseInt(req.params.id);
            const result = await this.bot.features.giveaways?.endGiveaway(giveawayId, req.user?.username);
            if (result?.success) {
                res.json({ message: result.message, winner: result.winner });
            }
            else {
                res.status(400).json({ error: result?.message || 'Failed to end giveaway' });
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to end giveaway:', error);
            res.status(500).json({ error: 'Failed to end giveaway' });
        }
    }
    async cancelGiveaway(req, res) {
        try {
            const giveawayId = parseInt(req.params.id);
            const result = await this.bot.features.giveaways?.cancelGiveaway(giveawayId, req.user?.username || 'web-admin');
            if (result?.success) {
                res.json({ message: result.message });
            }
            else {
                res.status(400).json({ error: result?.message || 'Failed to cancel giveaway' });
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to cancel giveaway:', error);
            res.status(500).json({ error: 'Failed to cancel giveaway' });
        }
    }
    async getUsers(req, res) {
        try {
            const limit = parseInt(req.query.limit) || 50;
            const users = await Database_1.database.db.all(`
        SELECT username, display_name, points, messages_sent, commands_used,
               last_seen, is_follower, is_subscriber, is_vip, is_moderator
        FROM users
        ORDER BY last_seen DESC
        LIMIT ?
      `, [limit]);
            res.json(users);
        }
        catch (error) {
            logger_1.logger.error('Failed to get users:', error);
            res.status(500).json({ error: 'Failed to get users' });
        }
    }
    async getUser(req, res) {
        try {
            const { username } = req.params;
            const user = await Database_1.database.getUser(username);
            if (user) {
                res.json(user);
            }
            else {
                res.status(404).json({ error: 'User not found' });
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to get user:', error);
            res.status(500).json({ error: 'Failed to get user' });
        }
    }
    async getChatLogs(req, res) {
        try {
            const limit = parseInt(req.query.limit) || 100;
            const logs = await Database_1.database.db.all(`
        SELECT username, message, timestamp, message_type
        FROM chat_logs
        ORDER BY timestamp DESC
        LIMIT ?
      `, [limit]);
            res.json(logs);
        }
        catch (error) {
            logger_1.logger.error('Failed to get chat logs:', error);
            res.status(500).json({ error: 'Failed to get chat logs' });
        }
    }
    async stop() {
        return new Promise((resolve) => {
            if (this.server) {
                this.server.close(() => {
                    logger_1.logger.info('Web server stopped');
                    resolve();
                });
            }
            else {
                resolve();
            }
        });
    }
}
exports.WebServer = WebServer;
//# sourceMappingURL=WebServer.js.map