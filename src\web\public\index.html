<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KickBot Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            margin: 2px 0;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .status-online { color: #28a745; }
        .status-offline { color: #dc3545; }
        .chat-log {
            max-height: 400px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
        }
        .chat-message {
            margin-bottom: 8px;
            padding: 5px 10px;
            border-radius: 6px;
            background-color: white;
            border-left: 3px solid #667eea;
        }
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
        }
        .hidden { display: none !important; }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div id="loginScreen" class="login-container d-flex align-items-center justify-content-center">
        <div class="login-card p-5 shadow-lg" style="width: 400px;">
            <div class="text-center mb-4">
                <h2 class="fw-bold text-primary">🤖 KickBot Dashboard</h2>
                <p class="text-muted">Sign in to manage your bot</p>
            </div>
            <form id="loginForm">
                <div class="mb-3">
                    <label for="username" class="form-label">Username</label>
                    <input type="text" class="form-control" id="username" required>
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label">Password</label>
                    <input type="password" class="form-control" id="password" required>
                </div>
                <button type="submit" class="btn btn-primary w-100">Sign In</button>
            </form>
            <div id="loginError" class="alert alert-danger mt-3 hidden"></div>
        </div>
    </div>

    <!-- Main Dashboard -->
    <div id="dashboard" class="hidden">
        <div class="container-fluid">
            <div class="row">
                <!-- Sidebar -->
                <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                    <div class="position-sticky pt-3">
                        <div class="text-center mb-4">
                            <h4 class="text-white">🤖 KickBot</h4>
                            <small class="text-white-50">Dashboard</small>
                        </div>
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link active" href="#" data-section="overview">
                                    <i class="bi bi-speedometer2"></i> Overview
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" data-section="commands">
                                    <i class="bi bi-terminal"></i> Commands
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" data-section="moderation">
                                    <i class="bi bi-shield-check"></i> Moderation
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" data-section="currency">
                                    <i class="bi bi-coin"></i> Currency
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" data-section="giveaways">
                                    <i class="bi bi-gift"></i> Giveaways
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" data-section="users">
                                    <i class="bi bi-people"></i> Users
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" data-section="chat">
                                    <i class="bi bi-chat-dots"></i> Chat Logs
                                </a>
                            </li>
                        </ul>
                        <hr class="text-white-50">
                        <div class="dropdown">
                            <a href="#" class="d-flex align-items-center text-white text-decoration-none dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle me-2"></i>
                                <span id="currentUser">Admin</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-dark text-small shadow">
                                <li><a class="dropdown-item" href="#" onclick="logout()">Sign out</a></li>
                            </ul>
                        </div>
                    </div>
                </nav>

                <!-- Main content -->
                <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2" id="pageTitle">Dashboard Overview</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <div class="btn-group me-2">
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshData()">
                                    <i class="bi bi-arrow-clockwise"></i> Refresh
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="restartBot()">
                                    <i class="bi bi-arrow-repeat"></i> Restart Bot
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Content sections will be loaded here -->
                    <div id="content">
                        <!-- Overview Section -->
                        <div id="overview-section" class="content-section">
                            <div class="row mb-4">
                                <div class="col-md-3 mb-3">
                                    <div class="card stat-card">
                                        <div class="card-body text-center">
                                            <i class="bi bi-robot fs-1"></i>
                                            <h5 class="card-title mt-2">Bot Status</h5>
                                            <p class="card-text" id="botStatus">Loading...</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="card stat-card">
                                        <div class="card-body text-center">
                                            <i class="bi bi-clock fs-1"></i>
                                            <h5 class="card-title mt-2">Uptime</h5>
                                            <p class="card-text" id="botUptime">Loading...</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="card stat-card">
                                        <div class="card-body text-center">
                                            <i class="bi bi-chat-square-text fs-1"></i>
                                            <h5 class="card-title mt-2">Messages</h5>
                                            <p class="card-text" id="messageCount">Loading...</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="card stat-card">
                                        <div class="card-body text-center">
                                            <i class="bi bi-terminal fs-1"></i>
                                            <h5 class="card-title mt-2">Commands</h5>
                                            <p class="card-text" id="commandCount">Loading...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="card-title mb-0">Recent Activity</h5>
                                        </div>
                                        <div class="card-body">
                                            <div id="recentActivity">Loading...</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="card-title mb-0">Quick Actions</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="d-grid gap-2">
                                                <button class="btn btn-primary" onclick="showSection('commands')">
                                                    <i class="bi bi-plus-circle"></i> Add Command
                                                </button>
                                                <button class="btn btn-success" onclick="showSection('giveaways')">
                                                    <i class="bi bi-gift"></i> Create Giveaway
                                                </button>
                                                <button class="btn btn-warning" onclick="showSection('moderation')">
                                                    <i class="bi bi-shield-check"></i> Moderation Settings
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Other sections will be loaded dynamically -->
                        <div id="commands-section" class="content-section hidden">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">Custom Commands</h5>
                                    <button class="btn btn-primary btn-sm" onclick="showAddCommandModal()">
                                        <i class="bi bi-plus"></i> Add Command
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div id="commandsList">Loading commands...</div>
                                </div>
                            </div>
                        </div>

                        <div id="moderation-section" class="content-section hidden">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="mb-0">Moderation Statistics</h5>
                                        </div>
                                        <div class="card-body">
                                            <div id="moderationStats">Loading...</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="mb-0">Banned Words</h5>
                                        </div>
                                        <div class="card-body">
                                            <div id="bannedWords">Loading...</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="currency-section" class="content-section hidden">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="mb-0">Points Leaderboard</h5>
                                        </div>
                                        <div class="card-body">
                                            <div id="leaderboard">Loading...</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="mb-0">Currency Stats</h5>
                                        </div>
                                        <div class="card-body">
                                            <div id="currencyStats">Loading...</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="giveaways-section" class="content-section hidden">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">Giveaways</h5>
                                    <button class="btn btn-primary btn-sm" onclick="showCreateGiveawayModal()">
                                        <i class="bi bi-plus"></i> Create Giveaway
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div id="giveawaysList">Loading giveaways...</div>
                                </div>
                            </div>
                        </div>

                        <div id="users-section" class="content-section hidden">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Users</h5>
                                </div>
                                <div class="card-body">
                                    <div id="usersList">Loading users...</div>
                                </div>
                            </div>
                        </div>

                        <div id="chat-section" class="content-section hidden">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Chat Logs</h5>
                                </div>
                                <div class="card-body">
                                    <div class="chat-log" id="chatLogs">Loading chat logs...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/dashboard.js"></script>
</body>
</html>
