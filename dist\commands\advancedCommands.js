"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerAdvancedCommands = registerAdvancedCommands;
const types_1 = require("../types");
const logger_1 = require("../utils/logger");
function registerAdvancedCommands(commandHandler, currencySystem, giveawaySystem) {
    if (currencySystem) {
        registerCurrencyCommands(commandHandler, currencySystem);
    }
    if (giveawaySystem) {
        registerGiveawayCommands(commandHandler, giveawaySystem);
    }
    logger_1.logger.info('Registered advanced feature commands');
}
function registerCurrencyCommands(commandHandler, currencySystem) {
    commandHandler.registerCommand({
        name: 'points',
        description: 'Check your points balance',
        usage: '!points [username]',
        aliases: ['balance', 'coins'],
        cooldown: 5,
        permission: types_1.PermissionLevel.EVERYONE,
        enabled: true,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    }, async (context) => await pointsCommand(context, currencySystem));
    commandHandler.registerCommand({
        name: 'give',
        description: 'Give points to a user',
        usage: '!give <username> <amount>',
        aliases: ['givepoints'],
        cooldown: 0,
        permission: types_1.PermissionLevel.MODERATOR,
        enabled: true,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    }, async (context) => await givePointsCommand(context, currencySystem));
    commandHandler.registerCommand({
        name: 'transfer',
        description: 'Transfer points to another user',
        usage: '!transfer <username> <amount>',
        aliases: ['send'],
        cooldown: 30,
        permission: types_1.PermissionLevel.EVERYONE,
        enabled: true,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    }, async (context) => await transferPointsCommand(context, currencySystem));
    commandHandler.registerCommand({
        name: 'leaderboard',
        description: 'Show points leaderboard',
        usage: '!leaderboard',
        aliases: ['top', 'rich'],
        cooldown: 30,
        permission: types_1.PermissionLevel.EVERYONE,
        enabled: true,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    }, async (context) => await leaderboardCommand(context, currencySystem));
    commandHandler.registerCommand({
        name: 'daily',
        description: 'Claim your daily bonus',
        usage: '!daily',
        aliases: ['bonus'],
        cooldown: 60,
        permission: types_1.PermissionLevel.EVERYONE,
        enabled: true,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    }, async (context) => await dailyBonusCommand(context, currencySystem));
}
function registerGiveawayCommands(commandHandler, giveawaySystem) {
    commandHandler.registerCommand({
        name: 'giveaway',
        description: 'Create a new giveaway',
        usage: '!giveaway <duration> <prize> [description]',
        aliases: ['startgiveaway'],
        cooldown: 0,
        permission: types_1.PermissionLevel.MODERATOR,
        enabled: true,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    }, async (context) => await createGiveawayCommand(context, giveawaySystem));
    commandHandler.registerCommand({
        name: 'enter',
        description: 'Enter the current giveaway',
        usage: '!enter',
        aliases: ['join'],
        cooldown: 5,
        permission: types_1.PermissionLevel.EVERYONE,
        enabled: true,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    }, async (context) => await enterGiveawayCommand(context, giveawaySystem));
    commandHandler.registerCommand({
        name: 'endgiveaway',
        description: 'End the current giveaway',
        usage: '!endgiveaway [id]',
        aliases: ['stopgiveaway'],
        cooldown: 0,
        permission: types_1.PermissionLevel.MODERATOR,
        enabled: true,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    }, async (context) => await endGiveawayCommand(context, giveawaySystem));
    commandHandler.registerCommand({
        name: 'cancelgiveaway',
        description: 'Cancel the current giveaway',
        usage: '!cancelgiveaway [id]',
        aliases: [],
        cooldown: 0,
        permission: types_1.PermissionLevel.MODERATOR,
        enabled: true,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    }, async (context) => await cancelGiveawayCommand(context, giveawaySystem));
    commandHandler.registerCommand({
        name: 'giveawayinfo',
        description: 'Show current giveaway information',
        usage: '!giveawayinfo',
        aliases: ['ginfo'],
        cooldown: 10,
        permission: types_1.PermissionLevel.EVERYONE,
        enabled: true,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    }, async (context) => await giveawayInfoCommand(context, giveawaySystem));
}
async function pointsCommand(context, currencySystem) {
    const { bot, message, args, user } = context;
    const targetUser = args.length > 0 ? args[0] : user.username;
    const points = await currencySystem.getUserPoints(targetUser);
    if (targetUser.toLowerCase() === user.username.toLowerCase()) {
        await bot.replyToMessage(message, `You have ${points} points.`);
    }
    else {
        await bot.replyToMessage(message, `${targetUser} has ${points} points.`);
    }
}
async function givePointsCommand(context, currencySystem) {
    const { bot, message, args } = context;
    if (args.length < 2) {
        await bot.replyToMessage(message, 'Usage: !give <username> <amount>');
        return;
    }
    const targetUser = args[0];
    const amount = parseInt(args[1]);
    if (isNaN(amount) || amount <= 0) {
        await bot.replyToMessage(message, 'Invalid amount. Must be a positive number.');
        return;
    }
    const success = await currencySystem.addPoints(targetUser, amount, 'Admin gift');
    if (success) {
        await bot.replyToMessage(message, `✅ Gave ${amount} points to ${targetUser}.`);
    }
    else {
        await bot.replyToMessage(message, `❌ Failed to give points to ${targetUser}.`);
    }
}
async function transferPointsCommand(context, currencySystem) {
    const { bot, message, args, user } = context;
    if (args.length < 2) {
        await bot.replyToMessage(message, 'Usage: !transfer <username> <amount>');
        return;
    }
    const targetUser = args[0];
    const amount = parseInt(args[1]);
    if (isNaN(amount) || amount <= 0) {
        await bot.replyToMessage(message, 'Invalid amount. Must be a positive number.');
        return;
    }
    if (targetUser.toLowerCase() === user.username.toLowerCase()) {
        await bot.replyToMessage(message, 'You cannot transfer points to yourself.');
        return;
    }
    const success = await currencySystem.transferPoints(user.username, targetUser, amount);
    if (success) {
        await bot.replyToMessage(message, `✅ Transferred ${amount} points to ${targetUser}.`);
    }
    else {
        await bot.replyToMessage(message, `❌ Transfer failed. Check your balance and try again.`);
    }
}
async function leaderboardCommand(context, currencySystem) {
    const { bot, message } = context;
    const leaderboard = await currencySystem.getLeaderboard(5);
    if (leaderboard.length === 0) {
        await bot.replyToMessage(message, 'No users found on the leaderboard.');
        return;
    }
    const leaderboardText = leaderboard
        .map(entry => `${entry.rank}. ${entry.username}: ${entry.points} points`)
        .join(' | ');
    await bot.replyToMessage(message, `🏆 Top 5 Leaderboard: ${leaderboardText}`);
}
async function dailyBonusCommand(context, currencySystem) {
    const { bot, message, user } = context;
    const result = await currencySystem.claimDailyBonus(user.username);
    await bot.replyToMessage(message, result.message);
}
async function createGiveawayCommand(context, giveawaySystem) {
    const { bot, message, args, user } = context;
    if (args.length < 2) {
        await bot.replyToMessage(message, 'Usage: !giveaway <duration_minutes> <prize> [description]');
        return;
    }
    const duration = parseInt(args[0]);
    const prize = args[1];
    const description = args.slice(2).join(' ');
    if (isNaN(duration) || duration <= 0 || duration > 1440) {
        await bot.replyToMessage(message, 'Invalid duration. Must be between 1-1440 minutes.');
        return;
    }
    const result = await giveawaySystem.createGiveaway(`Giveaway by ${user.username}`, prize, duration, user.username, { description });
    await bot.replyToMessage(message, result.message);
}
async function enterGiveawayCommand(context, giveawaySystem) {
    const { bot, message, user } = context;
    const result = await giveawaySystem.enterGiveaway(user.username);
    await bot.replyToMessage(message, result.message);
}
async function endGiveawayCommand(context, giveawaySystem) {
    const { bot, message, args, user } = context;
    const activeGiveaways = giveawaySystem.getActiveGiveaways();
    if (activeGiveaways.length === 0) {
        await bot.replyToMessage(message, 'No active giveaways to end.');
        return;
    }
    const giveawayId = args.length > 0 ? parseInt(args[0]) : activeGiveaways[0].id;
    const result = await giveawaySystem.endGiveaway(giveawayId, user.username);
    await bot.replyToMessage(message, result.message);
}
async function cancelGiveawayCommand(context, giveawaySystem) {
    const { bot, message, args, user } = context;
    const activeGiveaways = giveawaySystem.getActiveGiveaways();
    if (activeGiveaways.length === 0) {
        await bot.replyToMessage(message, 'No active giveaways to cancel.');
        return;
    }
    const giveawayId = args.length > 0 ? parseInt(args[0]) : activeGiveaways[0].id;
    const result = await giveawaySystem.cancelGiveaway(giveawayId, user.username);
    await bot.replyToMessage(message, result.message);
}
async function giveawayInfoCommand(context, giveawaySystem) {
    const { bot, message } = context;
    const activeGiveaways = giveawaySystem.getActiveGiveaways();
    if (activeGiveaways.length === 0) {
        await bot.replyToMessage(message, 'No active giveaways.');
        return;
    }
    const giveaway = activeGiveaways[0];
    const timeLeft = Math.max(0, Math.ceil((giveaway.createdAt.getTime() + giveaway.duration * 60000 - Date.now()) / 60000));
    const info = `🎉 "${giveaway.title}" | Prize: ${giveaway.prize} | Entries: ${giveaway.entries.length}${giveaway.maxEntries > 0 ? `/${giveaway.maxEntries}` : ''} | Time left: ${timeLeft}m | Use !enter to join!`;
    await bot.replyToMessage(message, info);
}
//# sourceMappingURL=advancedCommands.js.map