"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.database = exports.Database = void 0;
const sqlite3_1 = __importDefault(require("sqlite3"));
const util_1 = require("util");
const logger_1 = require("../utils/logger");
const config_1 = require("../config");
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
class Database {
    constructor() {
        this.dbPath = config_1.databaseConfig.path;
        this.ensureDirectoryExists();
        this.db = new sqlite3_1.default.Database(this.dbPath);
        this.db.run = (0, util_1.promisify)(this.db.run.bind(this.db));
        this.db.get = (0, util_1.promisify)(this.db.get.bind(this.db));
        this.db.all = (0, util_1.promisify)(this.db.all.bind(this.db));
    }
    ensureDirectoryExists() {
        const dir = path_1.default.dirname(this.dbPath);
        if (!fs_1.default.existsSync(dir)) {
            fs_1.default.mkdirSync(dir, { recursive: true });
        }
    }
    async initialize() {
        try {
            await this.createTables();
            logger_1.logger.info('Database initialized successfully');
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize database:', error);
            throw error;
        }
    }
    async createTables() {
        await this.db.run(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        display_name TEXT NOT NULL,
        points INTEGER DEFAULT 0,
        messages_sent INTEGER DEFAULT 0,
        commands_used INTEGER DEFAULT 0,
        time_in_chat INTEGER DEFAULT 0,
        last_seen TEXT,
        first_seen TEXT,
        is_follower BOOLEAN DEFAULT FALSE,
        is_subscriber BOOLEAN DEFAULT FALSE,
        is_vip BOOLEAN DEFAULT FALSE,
        is_moderator BOOLEAN DEFAULT FALSE,
        violations INTEGER DEFAULT 0,
        timeouts INTEGER DEFAULT 0,
        bans INTEGER DEFAULT 0,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);
        await this.db.run(`
      CREATE TABLE IF NOT EXISTS commands (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        response TEXT NOT NULL,
        description TEXT,
        aliases TEXT DEFAULT '',
        cooldown INTEGER DEFAULT 5,
        permission INTEGER DEFAULT 0,
        enabled BOOLEAN DEFAULT TRUE,
        use_count INTEGER DEFAULT 0,
        created_by TEXT NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);
        await this.db.run(`
      CREATE TABLE IF NOT EXISTS chat_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT NOT NULL,
        message TEXT NOT NULL,
        timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
        message_type TEXT DEFAULT 'message',
        channel TEXT NOT NULL
      )
    `);
        await this.db.run(`
      CREATE TABLE IF NOT EXISTS moderation_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT NOT NULL,
        action TEXT NOT NULL,
        reason TEXT,
        duration INTEGER,
        moderator TEXT NOT NULL,
        timestamp TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);
        await this.db.run(`
      CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);
        await this.db.run(`
      CREATE TABLE IF NOT EXISTS giveaways (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        description TEXT,
        prize TEXT NOT NULL,
        duration INTEGER NOT NULL,
        max_entries INTEGER DEFAULT -1,
        entry_cost INTEGER DEFAULT 0,
        status TEXT DEFAULT 'active',
        winner TEXT,
        created_by TEXT NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        ended_at TEXT
      )
    `);
        await this.db.run(`
      CREATE TABLE IF NOT EXISTS giveaway_entries (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        giveaway_id INTEGER NOT NULL,
        username TEXT NOT NULL,
        entries INTEGER DEFAULT 1,
        timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (giveaway_id) REFERENCES giveaways (id),
        UNIQUE(giveaway_id, username)
      )
    `);
        await this.db.run(`
      CREATE TABLE IF NOT EXISTS song_requests (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        artist TEXT,
        url TEXT NOT NULL,
        platform TEXT NOT NULL,
        duration INTEGER,
        requested_by TEXT NOT NULL,
        status TEXT DEFAULT 'pending',
        played_at TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);
        logger_1.logger.info('Database tables created successfully');
    }
    async getUser(username) {
        try {
            const user = await this.db.get('SELECT * FROM users WHERE username = ? COLLATE NOCASE', [username]);
            return user || null;
        }
        catch (error) {
            logger_1.logger.error(`Failed to get user ${username}:`, error);
            return null;
        }
    }
    async createUser(userData) {
        try {
            await this.db.run(`
        INSERT INTO users (username, display_name, first_seen, last_seen)
        VALUES (?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `, [userData.username, userData.display_name || userData.username]);
            logger_1.logger.debug(`Created user: ${userData.username}`);
            return true;
        }
        catch (error) {
            logger_1.logger.error(`Failed to create user ${userData.username}:`, error);
            return false;
        }
    }
    async updateUser(username, updates) {
        try {
            const setClause = Object.keys(updates)
                .filter(key => key !== 'username')
                .map(key => `${key} = ?`)
                .join(', ');
            if (!setClause)
                return false;
            const values = Object.keys(updates)
                .filter(key => key !== 'username')
                .map(key => updates[key]);
            values.push(username);
            await this.db.run(`
        UPDATE users SET ${setClause}, updated_at = CURRENT_TIMESTAMP
        WHERE username = ? COLLATE NOCASE
      `, values);
            return true;
        }
        catch (error) {
            logger_1.logger.error(`Failed to update user ${username}:`, error);
            return false;
        }
    }
    async getCommand(name) {
        try {
            const command = await this.db.get('SELECT * FROM commands WHERE name = ? COLLATE NOCASE', [name]);
            return command || null;
        }
        catch (error) {
            logger_1.logger.error(`Failed to get command ${name}:`, error);
            return null;
        }
    }
    async getAllCommands() {
        try {
            const commands = await this.db.all('SELECT * FROM commands ORDER BY name');
            return commands || [];
        }
        catch (error) {
            logger_1.logger.error('Failed to get all commands:', error);
            return [];
        }
    }
    async createCommand(commandData) {
        try {
            await this.db.run(`
        INSERT INTO commands (name, response, description, aliases, cooldown, permission, enabled, created_by)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
                commandData.name,
                commandData.response,
                commandData.description || '',
                commandData.aliases || '',
                commandData.cooldown || 5,
                commandData.permission || 0,
                commandData.enabled !== false,
                commandData.created_by
            ]);
            logger_1.logger.debug(`Created command: ${commandData.name}`);
            return true;
        }
        catch (error) {
            logger_1.logger.error(`Failed to create command ${commandData.name}:`, error);
            return false;
        }
    }
    async updateCommand(name, updates) {
        try {
            const setClause = Object.keys(updates)
                .filter(key => key !== 'name' && key !== 'id')
                .map(key => `${key} = ?`)
                .join(', ');
            if (!setClause)
                return false;
            const values = Object.keys(updates)
                .filter(key => key !== 'name' && key !== 'id')
                .map(key => updates[key]);
            values.push(name);
            await this.db.run(`
        UPDATE commands SET ${setClause}, updated_at = CURRENT_TIMESTAMP
        WHERE name = ? COLLATE NOCASE
      `, values);
            return true;
        }
        catch (error) {
            logger_1.logger.error(`Failed to update command ${name}:`, error);
            return false;
        }
    }
    async deleteCommand(name) {
        try {
            await this.db.run('DELETE FROM commands WHERE name = ? COLLATE NOCASE', [name]);
            logger_1.logger.debug(`Deleted command: ${name}`);
            return true;
        }
        catch (error) {
            logger_1.logger.error(`Failed to delete command ${name}:`, error);
            return false;
        }
    }
    async logMessage(username, message, channel, messageType = 'message') {
        try {
            await this.db.run(`
        INSERT INTO chat_logs (username, message, channel, message_type)
        VALUES (?, ?, ?, ?)
      `, [username, message, channel, messageType]);
        }
        catch (error) {
            logger_1.logger.error('Failed to log message:', error);
        }
    }
    async logModerationAction(username, action, reason, duration, moderator) {
        try {
            await this.db.run(`
        INSERT INTO moderation_logs (username, action, reason, duration, moderator)
        VALUES (?, ?, ?, ?, ?)
      `, [username, action, reason, duration, moderator]);
        }
        catch (error) {
            logger_1.logger.error('Failed to log moderation action:', error);
        }
    }
    async getSetting(key) {
        try {
            const result = await this.db.get('SELECT value FROM settings WHERE key = ?', [key]);
            return result ? result.value : null;
        }
        catch (error) {
            logger_1.logger.error(`Failed to get setting ${key}:`, error);
            return null;
        }
    }
    async setSetting(key, value) {
        try {
            await this.db.run(`
        INSERT OR REPLACE INTO settings (key, value, updated_at)
        VALUES (?, ?, CURRENT_TIMESTAMP)
      `, [key, value]);
            return true;
        }
        catch (error) {
            logger_1.logger.error(`Failed to set setting ${key}:`, error);
            return false;
        }
    }
    async getStats() {
        try {
            const userCount = await this.db.get('SELECT COUNT(*) as count FROM users');
            const commandCount = await this.db.get('SELECT COUNT(*) as count FROM commands');
            const messageCount = await this.db.get('SELECT COUNT(*) as count FROM chat_logs');
            const moderationCount = await this.db.get('SELECT COUNT(*) as count FROM moderation_logs');
            return {
                users: userCount.count,
                commands: commandCount.count,
                messages: messageCount.count,
                moderationActions: moderationCount.count,
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get stats:', error);
            return { users: 0, commands: 0, messages: 0, moderationActions: 0 };
        }
    }
    async close() {
        return new Promise((resolve, reject) => {
            this.db.close((err) => {
                if (err) {
                    logger_1.logger.error('Error closing database:', err);
                    reject(err);
                }
                else {
                    logger_1.logger.info('Database connection closed');
                    resolve();
                }
            });
        });
    }
}
exports.Database = Database;
exports.database = new Database();
//# sourceMappingURL=Database.js.map