"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChannelManager = void 0;
const events_1 = require("events");
const kickApi_1 = require("../api/kickApi");
const logger_1 = require("../utils/logger");
class ChannelManager extends events_1.EventEmitter {
    constructor() {
        super();
        this.channelCache = new Map();
    }
    async joinChannel(channelSlug) {
        try {
            logger_1.logger.info(`Joining channel: ${channelSlug}`);
            const channel = await kickApi_1.kickAPI.getChannel(channelSlug);
            if (!channel) {
                logger_1.logger.error(`Channel not found: ${channelSlug}`);
                return false;
            }
            const settings = await kickApi_1.kickAPI.getChatroomSettings(channel.chatroom.id);
            const channelInfo = {
                id: channel.id,
                slug: channel.slug,
                name: channel.user.username,
                isLive: false,
                viewerCount: 0,
                followerCount: 0,
                subscriberCount: 0,
                chatroomId: channel.chatroom.id,
                settings,
                lastUpdated: new Date(),
            };
            this.currentChannel = channelInfo;
            this.channelCache.set(channelSlug, channelInfo);
            this.startChannelUpdates();
            logger_1.logger.info(`Successfully joined channel: ${channelSlug} (ID: ${channel.id})`);
            this.emit('channel_joined', channelInfo);
            return true;
        }
        catch (error) {
            logger_1.logger.error(`Failed to join channel ${channelSlug}:`, error);
            return false;
        }
    }
    leaveChannel() {
        if (this.currentChannel) {
            logger_1.logger.info(`Leaving channel: ${this.currentChannel.slug}`);
            this.stopChannelUpdates();
            const channelInfo = this.currentChannel;
            this.currentChannel = undefined;
            this.emit('channel_left', channelInfo);
        }
    }
    getCurrentChannel() {
        return this.currentChannel;
    }
    async updateChannelInfo() {
        if (!this.currentChannel) {
            return;
        }
        try {
            const channel = await kickApi_1.kickAPI.getChannel(this.currentChannel.slug);
            if (!channel) {
                logger_1.logger.warn(`Failed to update channel info: channel not found`);
                return;
            }
            this.currentChannel.lastUpdated = new Date();
            this.channelCache.set(this.currentChannel.slug, this.currentChannel);
            this.emit('channel_updated', this.currentChannel);
        }
        catch (error) {
            logger_1.logger.error('Failed to update channel info:', error);
        }
    }
    startChannelUpdates() {
        this.stopChannelUpdates();
        this.updateInterval = setInterval(() => {
            this.updateChannelInfo();
        }, 5 * 60 * 1000);
    }
    stopChannelUpdates() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = undefined;
        }
    }
    async updateChatroomSettings(settings) {
        if (!this.currentChannel) {
            logger_1.logger.error('Cannot update chatroom settings: not in a channel');
            return false;
        }
        try {
            const success = await kickApi_1.kickAPI.updateChatroomSettings(this.currentChannel.chatroomId, settings);
            if (success && this.currentChannel.settings) {
                Object.assign(this.currentChannel.settings, settings);
                this.emit('chatroom_settings_updated', this.currentChannel.settings);
                logger_1.logger.info('Chatroom settings updated successfully');
            }
            return success;
        }
        catch (error) {
            logger_1.logger.error('Failed to update chatroom settings:', error);
            return false;
        }
    }
    async enableSlowMode(interval = 30) {
        return this.updateChatroomSettings({
            slow_mode: true,
            message_interval: interval,
        });
    }
    async disableSlowMode() {
        return this.updateChatroomSettings({
            slow_mode: false,
            message_interval: 0,
        });
    }
    async enableFollowersOnly(minDuration = 0) {
        return this.updateChatroomSettings({
            followers_mode: true,
            following_min_duration: minDuration,
        });
    }
    async disableFollowersOnly() {
        return this.updateChatroomSettings({
            followers_mode: false,
            following_min_duration: 0,
        });
    }
    async enableSubscribersOnly() {
        return this.updateChatroomSettings({
            subscribers_mode: true,
        });
    }
    async disableSubscribersOnly() {
        return this.updateChatroomSettings({
            subscribers_mode: false,
        });
    }
    async enableEmotesMode() {
        return this.updateChatroomSettings({
            emotes_mode: true,
        });
    }
    async disableEmotesMode() {
        return this.updateChatroomSettings({
            emotes_mode: false,
        });
    }
    getCachedChannel(channelSlug) {
        return this.channelCache.get(channelSlug);
    }
    clearCache() {
        this.channelCache.clear();
        logger_1.logger.info('Channel cache cleared');
    }
    getCacheStats() {
        return {
            cachedChannels: this.channelCache.size,
            currentChannel: this.currentChannel?.slug || null,
            isUpdating: !!this.updateInterval,
        };
    }
    get inChannel() {
        return !!this.currentChannel;
    }
    get currentChatroomId() {
        return this.currentChannel?.chatroomId;
    }
    get currentChannelSlug() {
        return this.currentChannel?.slug;
    }
    get currentChatroomSettings() {
        return this.currentChannel?.settings;
    }
    get isSlowModeEnabled() {
        return this.currentChannel?.settings?.slow_mode || false;
    }
    get isFollowersOnlyEnabled() {
        return this.currentChannel?.settings?.followers_mode || false;
    }
    get isSubscribersOnlyEnabled() {
        return this.currentChannel?.settings?.subscribers_mode || false;
    }
    get isEmotesModeEnabled() {
        return this.currentChannel?.settings?.emotes_mode || false;
    }
    cleanup() {
        this.stopChannelUpdates();
        this.clearCache();
        this.currentChannel = undefined;
    }
}
exports.ChannelManager = ChannelManager;
//# sourceMappingURL=ChannelManager.js.map