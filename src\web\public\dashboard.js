// KickBot Dashboard JavaScript

let authToken = localStorage.getItem('authToken');
let currentSection = 'overview';

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    if (authToken) {
        showDashboard();
        loadOverviewData();
    } else {
        showLogin();
    }

    // Setup navigation
    setupNavigation();
    
    // Setup login form
    setupLoginForm();
});

function showLogin() {
    document.getElementById('loginScreen').classList.remove('hidden');
    document.getElementById('dashboard').classList.add('hidden');
}

function showDashboard() {
    document.getElementById('loginScreen').classList.add('hidden');
    document.getElementById('dashboard').classList.remove('hidden');
}

function setupLoginForm() {
    document.getElementById('loginForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        
        try {
            const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username, password }),
            });
            
            const data = await response.json();
            
            if (response.ok) {
                authToken = data.token;
                localStorage.setItem('authToken', authToken);
                document.getElementById('currentUser').textContent = data.user.username;
                showDashboard();
                loadOverviewData();
            } else {
                showError('loginError', data.error || 'Login failed');
            }
        } catch (error) {
            showError('loginError', 'Connection error');
        }
    });
}

function setupNavigation() {
    document.querySelectorAll('[data-section]').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const section = this.getAttribute('data-section');
            showSection(section);
        });
    });
}

function showSection(section) {
    // Update active nav link
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    document.querySelector(`[data-section="${section}"]`).classList.add('active');
    
    // Hide all sections
    document.querySelectorAll('.content-section').forEach(sec => {
        sec.classList.add('hidden');
    });
    
    // Show selected section
    document.getElementById(`${section}-section`).classList.remove('hidden');
    
    // Update page title
    const titles = {
        overview: 'Dashboard Overview',
        commands: 'Custom Commands',
        moderation: 'Moderation',
        currency: 'Currency System',
        giveaways: 'Giveaways',
        users: 'Users',
        chat: 'Chat Logs'
    };
    document.getElementById('pageTitle').textContent = titles[section] || 'Dashboard';
    
    currentSection = section;
    
    // Load section data
    switch(section) {
        case 'overview':
            loadOverviewData();
            break;
        case 'commands':
            loadCommands();
            break;
        case 'moderation':
            loadModerationData();
            break;
        case 'currency':
            loadCurrencyData();
            break;
        case 'giveaways':
            loadGiveaways();
            break;
        case 'users':
            loadUsers();
            break;
        case 'chat':
            loadChatLogs();
            break;
    }
}

async function apiCall(endpoint, options = {}) {
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`,
        },
    };
    
    const response = await fetch(endpoint, { ...defaultOptions, ...options });
    
    if (response.status === 401) {
        logout();
        return null;
    }
    
    return response;
}

async function loadOverviewData() {
    try {
        const [statusResponse, statsResponse] = await Promise.all([
            apiCall('/api/bot/status'),
            apiCall('/api/bot/stats')
        ]);
        
        if (statusResponse && statsResponse) {
            const status = await statusResponse.json();
            const stats = await statsResponse.json();
            
            // Update status cards
            document.getElementById('botStatus').innerHTML = status.running 
                ? '<span class="status-online">● Online</span>' 
                : '<span class="status-offline">● Offline</span>';
            document.getElementById('botUptime').textContent = status.uptime || 'N/A';
            document.getElementById('messageCount').textContent = stats.bot?.messageCount || '0';
            document.getElementById('commandCount').textContent = stats.bot?.commandCount || '0';
            
            // Update recent activity
            updateRecentActivity(stats);
        }
    } catch (error) {
        console.error('Failed to load overview data:', error);
    }
}

function updateRecentActivity(stats) {
    const activity = document.getElementById('recentActivity');
    let html = '<div class="list-group list-group-flush">';
    
    if (stats.features?.moderation?.totalViolations > 0) {
        html += `<div class="list-group-item">🛡️ ${stats.features.moderation.totalViolations} moderation actions</div>`;
    }
    
    if (stats.features?.currency?.totalPointsInCirculation > 0) {
        html += `<div class="list-group-item">💰 ${stats.features.currency.totalPointsInCirculation} points in circulation</div>`;
    }
    
    if (stats.features?.giveaways?.activeGiveaways > 0) {
        html += `<div class="list-group-item">🎁 ${stats.features.giveaways.activeGiveaways} active giveaways</div>`;
    }
    
    if (stats.users?.totalUsers > 0) {
        html += `<div class="list-group-item">👥 ${stats.users.totalUsers} users tracked</div>`;
    }
    
    html += '</div>';
    activity.innerHTML = html;
}

async function loadCommands() {
    try {
        const response = await apiCall('/api/commands');
        if (response) {
            const commands = await response.json();
            displayCommands(commands);
        }
    } catch (error) {
        console.error('Failed to load commands:', error);
    }
}

function displayCommands(commands) {
    const container = document.getElementById('commandsList');
    
    if (commands.length === 0) {
        container.innerHTML = '<p class="text-muted">No custom commands found.</p>';
        return;
    }
    
    let html = '<div class="table-responsive"><table class="table table-striped">';
    html += '<thead><tr><th>Name</th><th>Response</th><th>Cooldown</th><th>Uses</th><th>Actions</th></tr></thead><tbody>';
    
    commands.forEach(cmd => {
        html += `
            <tr>
                <td><code>!${cmd.name}</code></td>
                <td>${cmd.response.substring(0, 50)}${cmd.response.length > 50 ? '...' : ''}</td>
                <td>${cmd.cooldown}s</td>
                <td>${cmd.use_count || 0}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editCommand('${cmd.name}')">Edit</button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteCommand('${cmd.name}')">Delete</button>
                </td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    container.innerHTML = html;
}

async function loadModerationData() {
    try {
        const response = await apiCall('/api/moderation/stats');
        if (response) {
            const stats = await response.json();
            displayModerationStats(stats);
        }
    } catch (error) {
        console.error('Failed to load moderation data:', error);
    }
}

function displayModerationStats(stats) {
    const container = document.getElementById('moderationStats');
    
    let html = `
        <div class="row">
            <div class="col-6">
                <div class="text-center">
                    <h4 class="text-primary">${stats.totalViolations || 0}</h4>
                    <small class="text-muted">Total Violations</small>
                </div>
            </div>
            <div class="col-6">
                <div class="text-center">
                    <h4 class="text-warning">${stats.totalTimeouts || 0}</h4>
                    <small class="text-muted">Timeouts</small>
                </div>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-6">
                <div class="text-center">
                    <h4 class="text-danger">${stats.totalBans || 0}</h4>
                    <small class="text-muted">Bans</small>
                </div>
            </div>
            <div class="col-6">
                <div class="text-center">
                    <h4 class="text-success">${stats.config?.enabled ? 'Enabled' : 'Disabled'}</h4>
                    <small class="text-muted">Auto-Mod Status</small>
                </div>
            </div>
        </div>
    `;
    
    container.innerHTML = html;
}

async function loadCurrencyData() {
    try {
        const [statsResponse, leaderboardResponse] = await Promise.all([
            apiCall('/api/currency/stats'),
            apiCall('/api/currency/leaderboard?limit=10')
        ]);
        
        if (statsResponse && leaderboardResponse) {
            const stats = await statsResponse.json();
            const leaderboard = await leaderboardResponse.json();
            
            displayCurrencyStats(stats);
            displayLeaderboard(leaderboard);
        }
    } catch (error) {
        console.error('Failed to load currency data:', error);
    }
}

function displayCurrencyStats(stats) {
    const container = document.getElementById('currencyStats');
    
    let html = `
        <div class="mb-3">
            <h5 class="text-primary">${stats.totalPointsInCirculation || 0}</h5>
            <small class="text-muted">Total Points</small>
        </div>
        <div class="mb-3">
            <h5 class="text-success">${stats.totalUsers || 0}</h5>
            <small class="text-muted">Users with Points</small>
        </div>
        <div class="mb-3">
            <h5 class="text-info">${stats.averagePoints || 0}</h5>
            <small class="text-muted">Average Points</small>
        </div>
    `;
    
    if (stats.topUser) {
        html += `
            <div class="mb-3">
                <h6 class="text-warning">${stats.topUser.username}</h6>
                <small class="text-muted">Top User (${stats.topUser.points} points)</small>
            </div>
        `;
    }
    
    container.innerHTML = html;
}

function displayLeaderboard(leaderboard) {
    const container = document.getElementById('leaderboard');
    
    if (leaderboard.length === 0) {
        container.innerHTML = '<p class="text-muted">No users with points found.</p>';
        return;
    }
    
    let html = '<div class="table-responsive"><table class="table table-striped">';
    html += '<thead><tr><th>Rank</th><th>User</th><th>Points</th></tr></thead><tbody>';
    
    leaderboard.forEach(user => {
        const medal = user.rank === 1 ? '🥇' : user.rank === 2 ? '🥈' : user.rank === 3 ? '🥉' : '';
        html += `
            <tr>
                <td>${medal} #${user.rank}</td>
                <td>${user.username}</td>
                <td>${user.points}</td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    container.innerHTML = html;
}

async function loadGiveaways() {
    try {
        const response = await apiCall('/api/giveaways');
        if (response) {
            const data = await response.json();
            displayGiveaways(data.active, data.stats);
        }
    } catch (error) {
        console.error('Failed to load giveaways:', error);
    }
}

function displayGiveaways(giveaways, stats) {
    const container = document.getElementById('giveawaysList');
    
    let html = '';
    
    if (giveaways.length === 0) {
        html = '<p class="text-muted">No active giveaways.</p>';
    } else {
        html = '<div class="row">';
        giveaways.forEach(giveaway => {
            const timeLeft = Math.max(0, Math.ceil((new Date(giveaway.createdAt).getTime() + giveaway.duration * 60000 - Date.now()) / 60000));
            html += `
                <div class="col-md-6 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">${giveaway.title}</h5>
                            <p class="card-text">Prize: ${giveaway.prize}</p>
                            <p class="card-text">Entries: ${giveaway.entries.length}</p>
                            <p class="card-text">Time left: ${timeLeft} minutes</p>
                            <button class="btn btn-sm btn-success" onclick="endGiveaway(${giveaway.id})">End Now</button>
                            <button class="btn btn-sm btn-danger" onclick="cancelGiveaway(${giveaway.id})">Cancel</button>
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div>';
    }
    
    container.innerHTML = html;
}

async function loadUsers() {
    try {
        const response = await apiCall('/api/users?limit=50');
        if (response) {
            const users = await response.json();
            displayUsers(users);
        }
    } catch (error) {
        console.error('Failed to load users:', error);
    }
}

function displayUsers(users) {
    const container = document.getElementById('usersList');
    
    if (users.length === 0) {
        container.innerHTML = '<p class="text-muted">No users found.</p>';
        return;
    }
    
    let html = '<div class="table-responsive"><table class="table table-striped">';
    html += '<thead><tr><th>Username</th><th>Points</th><th>Messages</th><th>Commands</th><th>Last Seen</th><th>Roles</th></tr></thead><tbody>';
    
    users.forEach(user => {
        const roles = [];
        if (user.is_moderator) roles.push('Mod');
        if (user.is_vip) roles.push('VIP');
        if (user.is_subscriber) roles.push('Sub');
        if (user.is_follower) roles.push('Follower');
        
        html += `
            <tr>
                <td>${user.username}</td>
                <td>${user.points || 0}</td>
                <td>${user.messages_sent || 0}</td>
                <td>${user.commands_used || 0}</td>
                <td>${user.last_seen ? new Date(user.last_seen).toLocaleDateString() : 'Never'}</td>
                <td>${roles.join(', ') || 'User'}</td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    container.innerHTML = html;
}

async function loadChatLogs() {
    try {
        const response = await apiCall('/api/chat/logs?limit=50');
        if (response) {
            const logs = await response.json();
            displayChatLogs(logs);
        }
    } catch (error) {
        console.error('Failed to load chat logs:', error);
    }
}

function displayChatLogs(logs) {
    const container = document.getElementById('chatLogs');
    
    if (logs.length === 0) {
        container.innerHTML = '<p class="text-muted">No chat logs found.</p>';
        return;
    }
    
    let html = '';
    logs.reverse().forEach(log => {
        const time = new Date(log.timestamp).toLocaleTimeString();
        html += `
            <div class="chat-message">
                <strong>${log.username}</strong> 
                <small class="text-muted">${time}</small><br>
                ${log.message}
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// Utility functions
function showError(elementId, message) {
    const element = document.getElementById(elementId);
    element.textContent = message;
    element.classList.remove('hidden');
    setTimeout(() => element.classList.add('hidden'), 5000);
}

function refreshData() {
    switch(currentSection) {
        case 'overview':
            loadOverviewData();
            break;
        case 'commands':
            loadCommands();
            break;
        case 'moderation':
            loadModerationData();
            break;
        case 'currency':
            loadCurrencyData();
            break;
        case 'giveaways':
            loadGiveaways();
            break;
        case 'users':
            loadUsers();
            break;
        case 'chat':
            loadChatLogs();
            break;
    }
}

async function restartBot() {
    if (confirm('Are you sure you want to restart the bot?')) {
        try {
            const response = await apiCall('/api/bot/restart', { method: 'POST' });
            if (response) {
                alert('Bot restart initiated');
                setTimeout(refreshData, 3000);
            }
        } catch (error) {
            alert('Failed to restart bot');
        }
    }
}

function logout() {
    localStorage.removeItem('authToken');
    authToken = null;
    showLogin();
}

// Auto-refresh every 30 seconds
setInterval(() => {
    if (authToken && currentSection === 'overview') {
        loadOverviewData();
    }
}, 30000);
