# KickBot Setup Guide

This guide will help you set up and run your KickBot for Kick streaming platform.

## Prerequisites

- Node.js 18+ installed
- npm or yarn package manager
- A Kick account for your bot
- Basic knowledge of command line/terminal

## Quick Start

### 1. Install Dependencies

```bash
npm install
```

### 2. Configure Environment

Copy the example environment file and configure it:

```bash
cp .env.example .env
```

Edit `.env` with your bot credentials:

```env
# Bot Configuration
BOT_USERNAME=your_bot_username
BOT_PASSWORD=your_bot_password
DEFAULT_CHANNEL=your_channel_name

# Features (enable/disable as needed)
ENABLE_MODERATION=true
ENABLE_COMMANDS=true
ENABLE_SONG_REQUESTS=false
ENABLE_GAMES=true
ENABLE_ANALYTICS=true

# Moderation Settings
MAX_MESSAGE_LENGTH=500
SPAM_THRESHOLD=5
CAPS_THRESHOLD=70
LINK_PROTECTION=true
```

### 3. Build and Run

```bash
# Build the project
npm run build

# Start the bot
npm start
```

For development:
```bash
npm run dev
```

## Bot Account Setup

### Creating a Bot Account

1. Create a new Kick account for your bot
2. Choose a username that clearly identifies it as a bot (e.g., "YourChannelBot")
3. **Important**: Disable 2-factor authentication on the bot account
4. Add the bot as a moderator in your channel for full functionality

### Bot Permissions

For full functionality, your bot should be:
- Added as a moderator in your channel
- Have chat permissions enabled
- Not restricted by any channel-specific rules

## Available Commands

### Default Commands (Everyone)
- `!help` - Show available commands
- `!uptime` - Show bot uptime and stats
- `!roll [max]` - Roll a dice (default 1-100)
- `!8ball <question>` - Ask the magic 8-ball

### Moderation Commands (Moderators Only)
- `!timeout <user> [duration] [reason]` - Timeout a user
- `!ban <user> [reason]` - Ban a user
- `!unban <user>` - Unban a user
- `!addcom <name> <response>` - Add custom command
- `!editcom <name> <new_response>` - Edit custom command
- `!delcom <name>` - Delete custom command
- `!addword <word>` - Add banned word
- `!removeword <word>` - Remove banned word
- `!moderation <on|off>` - Toggle auto-moderation
- `!modstats` - Show moderation statistics

### Chat Management Commands (Moderators Only)
- `!slow <on|off> [interval]` - Enable/disable slow mode
- `!followers <on|off>` - Enable/disable followers only
- `!subs <on|off>` - Enable/disable subscribers only

## Custom Commands

### Creating Custom Commands

Use `!addcom` to create custom commands:

```
!addcom hello Hello @{user}! Welcome to the stream!
!addcom discord Join our Discord: https://discord.gg/example
!addcom social Follow us on Twitter: @YourTwitter
```

### Available Variables

- `{user}` - Username of command user
- `{channel}` - Current channel name
- `{uptime}` - Bot uptime
- `{count}` - Command usage count
- `{random}` - Random number 1-100
- `{args}` - All command arguments
- `{arg1}`, `{arg2}`, `{arg3}` - Specific arguments
- `{time}` - Current time
- `{date}` - Current date

### Example Custom Commands

```
!addcom lurk Thanks for lurking, {user}! Enjoy the stream!
!addcom shoutout Go check out {arg1} at kick.com/{arg1} - they're awesome!
!addcom random {user} rolled {random}!
!addcom uptime Stream has been live for {uptime}
```

## Auto-Moderation Features

### Spam Protection
- Detects users sending too many messages in a short time
- Configurable message limit and time window
- Automatic timeout/ban for violations

### Caps Lock Filter
- Detects messages with excessive uppercase letters
- Configurable percentage threshold
- Automatic moderation actions

### Banned Words Filter
- Maintains a list of banned words/phrases
- Automatic detection and punishment
- Easy management with `!addword` and `!removeword`

### Link Protection
- Blocks unauthorized links in chat
- Configurable allowed domains
- Protects against spam and malicious links

### Repetitive Message Detection
- Detects users repeating the same message
- Configurable repeat threshold
- Automatic timeout for violations

## Configuration Options

### Moderation Settings

Edit your `.env` file to customize moderation:

```env
# Spam Protection
SPAM_THRESHOLD=5          # Max messages per time window
SPAM_TIME_WINDOW=30       # Time window in seconds

# Caps Filter
CAPS_THRESHOLD=70         # Percentage of caps to trigger
CAPS_MIN_LENGTH=10        # Minimum message length to check

# Auto Timeout
AUTO_TIMEOUT=true         # Enable automatic timeouts
TIMEOUT_DURATION=300      # Default timeout duration (seconds)

# Banned Words (comma-separated)
BANNED_WORDS=word1,word2,phrase3
```

### Feature Toggles

Enable or disable features:

```env
ENABLE_MODERATION=true    # Auto-moderation features
ENABLE_COMMANDS=true      # Custom commands system
ENABLE_GAMES=true         # Chat games (!roll, !8ball)
ENABLE_ANALYTICS=true     # User tracking and stats
```

## Troubleshooting

### Common Issues

1. **Bot won't connect**
   - Check username/password in `.env`
   - Ensure 2FA is disabled on bot account
   - Verify channel name is correct

2. **Commands not working**
   - Ensure bot is moderator for mod commands
   - Check if commands are enabled in config
   - Verify command syntax

3. **Moderation not working**
   - Check if `ENABLE_MODERATION=true`
   - Ensure bot has moderator permissions
   - Verify moderation settings

### Logs

Check the logs for detailed information:
- Console output shows real-time activity
- Log files are saved to `./logs/kickbot.log`
- Use `LOG_LEVEL=debug` for detailed debugging

### Getting Help

1. Check the logs for error messages
2. Verify your configuration in `.env`
3. Ensure bot account has proper permissions
4. Test with simple commands first

## Advanced Usage

### Running as a Service

For production deployment, consider using PM2:

```bash
npm install -g pm2
pm2 start dist/index.js --name kickbot
pm2 startup
pm2 save
```

### Multiple Channels

To run the bot in multiple channels, you'll need to:
1. Create separate bot accounts
2. Run multiple instances with different configurations
3. Use different environment files for each instance

### Custom Development

The bot is built with TypeScript and is highly extensible:
- Add new commands in `src/commands/`
- Extend moderation features in `src/moderation/`
- Add new API integrations in `src/api/`

## Security Notes

- Never share your bot credentials
- Use environment variables for sensitive data
- Regularly update dependencies
- Monitor bot activity and logs
- Use strong passwords for bot accounts

## Support

For issues and questions:
- Check the troubleshooting section
- Review the logs for error details
- Ensure proper configuration and permissions
