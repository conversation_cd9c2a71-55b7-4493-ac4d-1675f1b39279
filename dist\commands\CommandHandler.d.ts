import { EventEmitter } from 'events';
import { Command, PermissionLevel, ChatMessage } from '../types';
import { KickBot } from '../bot/KickBot';
export interface CommandContext {
    bot: KickBot;
    message: ChatMessage;
    args: string[];
    user: any;
    command: Command;
}
export type CommandFunction = (context: CommandContext) => Promise<void>;
export declare class CommandHandler extends EventEmitter {
    private bot;
    private commands;
    private commandFunctions;
    private cooldowns;
    constructor(bot: KickBot);
    private setupBotEvents;
    registerCommand(command: Command, handler: CommandFunction): void;
    unregisterCommand(commandName: string): boolean;
    private handleCommand;
    private isOnCooldown;
    private getRemainingCooldown;
    private setCooldown;
    getCommands(): Command[];
    getCommand(name: string): Command | undefined;
    getCommandsByPermission(permissionLevel: PermissionLevel): Command[];
    updateCommand(name: string, updates: Partial<Command>): boolean;
    clearCooldowns(commandName: string): void;
    clearUserCooldowns(username: string): void;
    getCommandStats(): any;
}
//# sourceMappingURL=CommandHandler.d.ts.map