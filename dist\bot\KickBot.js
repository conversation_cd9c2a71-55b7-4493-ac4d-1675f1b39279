"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.KickBot = void 0;
const events_1 = require("events");
const kickApi_1 = require("../api/kickApi");
const SimpleKickWebSocket_1 = require("../websocket/SimpleKickWebSocket");
const logger_1 = require("../utils/logger");
const types_1 = require("../types");
class KickBot extends events_1.EventEmitter {
    constructor(botConfig) {
        super();
        this.botConfig = botConfig;
        this.isRunning = false;
        this.messageCount = 0;
        this.commandCount = 0;
        this.webSocket = new SimpleKickWebSocket_1.SimpleKickWebSocket();
        this.startTime = new Date();
        this.setupEventHandlers();
    }
    setupEventHandlers() {
        this.webSocket.on('connected', () => {
            logger_1.logger.info('Bot connected to Kick WebSocket');
            this.emit('connected');
        });
        this.webSocket.on('subscribed', () => {
            logger_1.logger.info('Bot subscribed to channel chat');
            this.emit('subscribed');
        });
        this.webSocket.on('message', (message) => {
            this.handleMessage(message);
        });
        this.webSocket.on('new_follower', (data) => {
            logger_1.logger.info(`New follower: ${data.username}`);
            this.emit('new_follower', data);
        });
        this.webSocket.on('user_banned', (data) => {
            this.emit('user_banned', data);
        });
        this.webSocket.on('user_unbanned', (data) => {
            this.emit('user_unbanned', data);
        });
        this.webSocket.on('streamer_live', (data) => {
            this.emit('streamer_live', data);
        });
        this.webSocket.on('streamer_offline', (data) => {
            this.emit('streamer_offline', data);
        });
        this.webSocket.on('error', (error) => {
            logger_1.logger.error('WebSocket error:', error);
            this.emit('error', error);
        });
    }
    async start() {
        try {
            logger_1.logger.info('Starting KickBot...');
            logger_1.logger.info('Using simplified connection mode...');
            logger_1.logger.info('Authentication bypassed - using development mode');
            logger_1.logger.info(`Connecting to channel: ${this.botConfig.channel}`);
            const mockChatroomId = 123456;
            logger_1.logger.info(`Using mock chatroom ID: ${mockChatroomId}`);
            await this.webSocket.connect(mockChatroomId);
            this.isRunning = true;
            this.startTime = new Date();
            logger_1.logger.info('KickBot started successfully!');
            this.emit('started');
        }
        catch (error) {
            logger_1.logger.error('Failed to start bot:', error);
            this.emit('error', error);
            throw error;
        }
    }
    async stop() {
        logger_1.logger.info('Stopping KickBot...');
        this.isRunning = false;
        this.webSocket.disconnect();
        logger_1.logger.info('KickBot stopped');
        this.emit('stopped');
    }
    handleMessage(message) {
        this.messageCount++;
        this.emit('message', message);
        if (message.content.startsWith(this.botConfig.prefix)) {
            this.handleCommand(message);
        }
        const botEvent = {
            type: 'message',
            data: message,
            timestamp: new Date().toISOString(),
        };
        this.emit('bot_event', botEvent);
    }
    handleCommand(message) {
        const content = message.content.slice(this.botConfig.prefix.length);
        const args = content.split(' ');
        const commandName = args[0]?.toLowerCase();
        if (!commandName)
            return;
        this.commandCount++;
        logger_1.logger.commandUsed(message.sender.username, commandName, args.slice(1), `chatroom-${message.chatroom_id}`);
        this.emit('command', {
            name: commandName,
            args: args.slice(1),
            message,
            user: message.sender,
        });
    }
    async sendMessage(content) {
        if (!this.channel) {
            logger_1.logger.error('Cannot send message: not connected to channel');
            return false;
        }
        try {
            const success = await kickApi_1.kickAPI.sendMessage(this.channel.chatroom.id, content);
            if (success) {
                logger_1.logger.info(`Sent message: ${content}`);
            }
            return success;
        }
        catch (error) {
            logger_1.logger.error('Failed to send message:', error);
            return false;
        }
    }
    async replyToMessage(originalMessage, content) {
        const replyContent = `@${originalMessage.sender.username} ${content}`;
        return this.sendMessage(replyContent);
    }
    async banUser(username, permanent = false) {
        if (!this.channel) {
            logger_1.logger.error('Cannot ban user: not connected to channel');
            return false;
        }
        try {
            const success = await kickApi_1.kickAPI.banUser(this.channel.chatroom.id, username, permanent);
            if (success) {
                logger_1.logger.moderationAction('ban', username, this.botConfig.username);
            }
            return success;
        }
        catch (error) {
            logger_1.logger.error(`Failed to ban user ${username}:`, error);
            return false;
        }
    }
    async timeoutUser(username, duration) {
        if (!this.channel) {
            logger_1.logger.error('Cannot timeout user: not connected to channel');
            return false;
        }
        try {
            const success = await kickApi_1.kickAPI.timeoutUser(this.channel.chatroom.id, username, duration);
            if (success) {
                logger_1.logger.moderationAction('timeout', username, this.botConfig.username, `${duration} minutes`);
            }
            return success;
        }
        catch (error) {
            logger_1.logger.error(`Failed to timeout user ${username}:`, error);
            return false;
        }
    }
    async unbanUser(username) {
        if (!this.channel) {
            logger_1.logger.error('Cannot unban user: not connected to channel');
            return false;
        }
        try {
            const success = await kickApi_1.kickAPI.unbanUser(this.channel.chatroom.id, username);
            if (success) {
                logger_1.logger.moderationAction('unban', username, this.botConfig.username);
            }
            return success;
        }
        catch (error) {
            logger_1.logger.error(`Failed to unban user ${username}:`, error);
            return false;
        }
    }
    getUserPermissionLevel(user) {
        if (user.is_channel_owner)
            return types_1.PermissionLevel.OWNER;
        if (user.is_moderator)
            return types_1.PermissionLevel.MODERATOR;
        if (user.is_vip)
            return types_1.PermissionLevel.VIP;
        if (user.is_subscriber)
            return types_1.PermissionLevel.SUBSCRIBER;
        if (user.is_follower)
            return types_1.PermissionLevel.FOLLOWER;
        return types_1.PermissionLevel.EVERYONE;
    }
    hasPermission(user, requiredLevel) {
        const userLevel = this.getUserPermissionLevel(user);
        return userLevel >= requiredLevel;
    }
    getStats() {
        const uptime = Date.now() - this.startTime.getTime();
        return {
            isRunning: this.isRunning,
            uptime,
            uptimeFormatted: this.formatUptime(uptime),
            startTime: this.startTime,
            messageCount: this.messageCount,
            commandCount: this.commandCount,
            channel: this.channel?.slug,
            connected: this.webSocket.connected,
        };
    }
    formatUptime(uptime) {
        const seconds = Math.floor(uptime / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        if (days > 0) {
            return `${days}d ${hours % 24}h ${minutes % 60}m ${seconds % 60}s`;
        }
        else if (hours > 0) {
            return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
        }
        else if (minutes > 0) {
            return `${minutes}m ${seconds % 60}s`;
        }
        else {
            return `${seconds}s`;
        }
    }
    get currentChannel() {
        return this.channel;
    }
    get running() {
        return this.isRunning;
    }
    get connected() {
        return this.webSocket.connected;
    }
}
exports.KickBot = KickBot;
//# sourceMappingURL=KickBot.js.map