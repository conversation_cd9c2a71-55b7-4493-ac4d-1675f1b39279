{"name": "kickbot", "version": "1.0.0", "description": "A comprehensive Kick streaming bot with moderation, commands, and interactive features", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "start:full": "node examples/complete-bot.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "dev:full": "ts-node-dev --respawn --transpile-only examples/complete-bot.js", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "setup": "npm install && npm run build && cp .env.example .env"}, "keywords": ["kick", "streaming", "bot", "chat", "moderation", "twitch-alternative"], "author": "Your Name", "license": "MIT", "dependencies": {"axios": "^1.10.0", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.21.2", "helmet": "^7.2.0", "jsonwebtoken": "^9.0.2", "node-cron": "^3.0.3", "sqlite3": "^5.1.7", "uuid": "^9.0.1", "ws": "^8.18.3"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.10.0", "@types/uuid": "^9.0.7", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0"}}