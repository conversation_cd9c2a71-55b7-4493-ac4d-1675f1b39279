{"name": "kickbot", "version": "1.0.0", "description": "A comprehensive Kick streaming bot with moderation, commands, and interactive features", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "start:full": "node examples/complete-bot.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "dev:full": "ts-node-dev --respawn --transpile-only examples/complete-bot.js", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "setup": "npm install && npm run build && cp .env.example .env"}, "keywords": ["kick", "streaming", "bot", "chat", "moderation", "twitch-alternative"], "author": "Your Name", "license": "MIT", "dependencies": {"ws": "^8.14.2", "express": "^4.18.2", "sqlite3": "^5.1.6", "node-cron": "^3.0.3", "axios": "^1.6.2", "dotenv": "^16.3.1", "cors": "^2.8.5", "helmet": "^7.1.0", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "uuid": "^9.0.1"}, "devDependencies": {"@types/node": "^20.10.0", "@types/express": "^4.17.21", "@types/ws": "^8.5.10", "@types/cors": "^2.8.17", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.5", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.8", "typescript": "^5.3.2", "ts-node-dev": "^2.0.0", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "jest": "^29.7.0", "ts-jest": "^29.1.1"}, "engines": {"node": ">=18.0.0"}}