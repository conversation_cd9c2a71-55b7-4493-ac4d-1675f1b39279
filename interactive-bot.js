// Interactive KickBot - Test commands directly in terminal
const readline = require('readline');
const express = require('express');

console.log('🚀 Starting Interactive KickBot...');

// Bot configuration
const config = {
  channel: 'lllm7md',
  botName: 'KickBot',
  webPort: 3000,
  commands: new Map(),
  users: new Map(),
  messageCount: 0,
  startTime: new Date(),
};

// Add commands
config.commands.set('help', {
  response: 'Available commands: !help, !uptime, !hello, !roll, !8ball, !addcom, !points',
  cooldown: 5,
  lastUsed: 0,
});

config.commands.set('uptime', {
  response: () => {
    const uptime = Date.now() - config.startTime.getTime();
    const minutes = Math.floor(uptime / 60000);
    const hours = Math.floor(minutes / 60);
    return `Bot has been running for ${hours}h ${minutes % 60}m`;
  },
  cooldown: 10,
  lastUsed: 0,
});

config.commands.set('hello', {
  response: (args, username) => `Hello @${username}! Welcome to the stream! 🤖`,
  cooldown: 5,
  lastUsed: 0,
});

config.commands.set('roll', {
  response: (args, username) => {
    const max = args[0] ? parseInt(args[0]) : 100;
    const roll = Math.floor(Math.random() * (isNaN(max) ? 100 : max)) + 1;
    return `🎲 @${username} rolled: ${roll} (1-${isNaN(max) ? 100 : max})`;
  },
  cooldown: 3,
  lastUsed: 0,
});

config.commands.set('8ball', {
  response: (args, username) => {
    const responses = [
      'Yes, definitely!',
      'No way!',
      'Maybe...',
      'Ask again later',
      'Absolutely!',
      'I doubt it',
      'Signs point to yes',
      'Very doubtful',
      'Without a doubt',
      'My sources say no'
    ];
    const question = args.join(' ');
    return `🎱 @${username} asked: "${question}" - ${responses[Math.floor(Math.random() * responses.length)]}`;
  },
  cooldown: 5,
  lastUsed: 0,
});

config.commands.set('addcom', {
  response: (args, username) => {
    if (args.length < 2) {
      return `Usage: !addcom <name> <response>`;
    }
    const commandName = args[0].toLowerCase();
    const response = args.slice(1).join(' ');
    
    config.commands.set(commandName, {
      response: response,
      cooldown: 5,
      lastUsed: 0,
      createdBy: username,
    });
    
    return `✅ Command !${commandName} created by @${username}`;
  },
  cooldown: 0,
  lastUsed: 0,
});

config.commands.set('points', {
  response: (args, username) => {
    if (!config.users.has(username)) {
      config.users.set(username, {
        username,
        messageCount: 0,
        points: 100,
        firstSeen: new Date(),
        lastSeen: new Date(),
      });
    }
    
    const user = config.users.get(username);
    return `💎 @${username} has ${user.points} points!`;
  },
  cooldown: 10,
  lastUsed: 0,
});

// Message handler
function handleMessage(content, username = 'TestUser') {
  try {
    config.messageCount++;
    
    console.log(`\n💬 [${username}]: ${content}`);
    
    // Check for commands
    if (content.startsWith('!')) {
      const parts = content.slice(1).split(' ');
      const commandName = parts[0].toLowerCase();
      const args = parts.slice(1);
      
      const command = config.commands.get(commandName);
      if (command) {
        const now = Date.now();
        if (now - command.lastUsed > command.cooldown * 1000) {
          command.lastUsed = now;
          
          let response;
          if (typeof command.response === 'function') {
            response = command.response(args, username);
          } else {
            response = command.response;
          }
          
          console.log(`🤖 [BOT]: ${response}`);
        } else {
          const timeLeft = Math.ceil((command.cooldown * 1000 - (now - command.lastUsed)) / 1000);
          console.log(`⏰ [BOT]: Command on cooldown for ${timeLeft} seconds`);
        }
      } else {
        console.log(`❌ [BOT]: Unknown command: !${commandName}`);
      }
    }
    
    // Track user
    if (!config.users.has(username)) {
      config.users.set(username, {
        username,
        messageCount: 0,
        points: 100,
        firstSeen: new Date(),
        lastSeen: new Date(),
      });
    }
    
    const user = config.users.get(username);
    user.messageCount++;
    user.lastSeen = new Date();
    user.points += 1; // Earn 1 point per message
    
  } catch (error) {
    console.error('Error handling message:', error);
  }
}

// Web dashboard
function startWebDashboard() {
  const app = express();
  
  app.get('/api/status', (req, res) => {
    res.json({
      status: 'running',
      channel: config.channel,
      uptime: Date.now() - config.startTime.getTime(),
      messageCount: config.messageCount,
      userCount: config.users.size,
      commands: Array.from(config.commands.keys()),
    });
  });
  
  app.get('/', (req, res) => {
    const usersList = Array.from(config.users.values())
      .sort((a, b) => b.points - a.points)
      .slice(0, 10)
      .map(user => `<tr><td>${user.username}</td><td>${user.points}</td><td>${user.messageCount}</td></tr>`)
      .join('');
    
    res.send(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Interactive KickBot Dashboard</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
          .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
          .status { background: #e8f5e8; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
          .commands { background: #f0f8ff; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
          .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
          .stat-card { background: #fff; border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center; }
          .stat-number { font-size: 24px; font-weight: bold; color: #333; }
          .stat-label { color: #666; margin-top: 5px; }
          table { width: 100%; border-collapse: collapse; margin-top: 10px; }
          th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
          th { background-color: #f2f2f2; }
          h1 { color: #333; text-align: center; }
          h2 { color: #555; border-bottom: 2px solid #eee; padding-bottom: 10px; }
          .refresh-btn { background: #007cba; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; }
          .refresh-btn:hover { background: #005a87; }
          .instructions { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        </style>
        <script>
          setInterval(() => location.reload(), 5000); // Auto-refresh every 5 seconds
        </script>
      </head>
      <body>
        <div class="container">
          <h1>🤖 Interactive KickBot Dashboard</h1>
          
          <div class="instructions">
            <h2>📝 How to Test Commands</h2>
            <p><strong>In your terminal/command prompt:</strong></p>
            <p>• Type any message or command (e.g., <code>!roll</code>, <code>!hello</code>, <code>!8ball Will I win?</code>)</p>
            <p>• Press Enter to send</p>
            <p>• Watch the bot respond in real-time!</p>
          </div>
          
          <div class="status">
            <h2>📊 Bot Status</h2>
            <p><strong>Status:</strong> <span style="color: green;">● Running (Interactive Mode)</span></p>
            <p><strong>Channel:</strong> ${config.channel}</p>
            <p><strong>Started:</strong> ${config.startTime.toLocaleString()}</p>
            <button class="refresh-btn" onclick="location.reload()">🔄 Refresh</button>
          </div>
          
          <div class="stats">
            <div class="stat-card">
              <div class="stat-number">${config.messageCount}</div>
              <div class="stat-label">Messages Processed</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">${config.users.size}</div>
              <div class="stat-label">Users Tracked</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">${config.commands.size}</div>
              <div class="stat-label">Commands Available</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">${Math.floor((Date.now() - config.startTime.getTime()) / 60000)}</div>
              <div class="stat-label">Minutes Uptime</div>
            </div>
          </div>
          
          <div class="commands">
            <h2>🎮 Available Commands</h2>
            <p><strong>Commands:</strong> ${Array.from(config.commands.keys()).map(cmd => `!${cmd}`).join(', ')}</p>
            <p><em>Type these commands in your terminal to test them!</em></p>
          </div>
          
          <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
            <h2>🏆 Top Users (Points Leaderboard)</h2>
            <table>
              <thead>
                <tr><th>Username</th><th>Points</th><th>Messages</th></tr>
              </thead>
              <tbody>
                ${usersList || '<tr><td colspan="3">No users yet - start typing commands!</td></tr>'}
              </tbody>
            </table>
          </div>
        </div>
      </body>
      </html>
    `);
  });
  
  app.listen(config.webPort, () => {
    console.log(`🌐 Web Dashboard: http://localhost:${config.webPort}`);
  });
}

// Interactive terminal input
function startInteractiveMode() {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
    prompt: '\n💬 Type a message or command: '
  });

  console.log('\n🎮 Interactive Mode Started!');
  console.log('📝 Available commands: !help, !uptime, !hello, !roll, !8ball, !addcom, !points');
  console.log('💡 Try typing: !roll, !hello, !8ball Will I win?, !addcom test Hello World');
  console.log('🌐 Dashboard: http://localhost:3000');
  
  rl.prompt();

  rl.on('line', (input) => {
    const trimmed = input.trim();
    if (trimmed) {
      if (trimmed.toLowerCase() === 'quit' || trimmed.toLowerCase() === 'exit') {
        console.log('\n🛑 Shutting down bot...');
        rl.close();
        process.exit(0);
      } else {
        handleMessage(trimmed, 'You');
      }
    }
    rl.prompt();
  });

  rl.on('close', () => {
    console.log('\n✅ Bot stopped successfully');
    process.exit(0);
  });
}

// Start everything
console.log('📋 Configuration:');
console.log(`   • Channel: ${config.channel}`);
console.log(`   • Mode: Interactive Testing`);
console.log(`   • Web Port: ${config.webPort}`);

startWebDashboard();
startInteractiveMode();
