"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModerationManager = void 0;
const events_1 = require("events");
const logger_1 = require("../utils/logger");
class ModerationManager extends events_1.EventEmitter {
    constructor(bot, config) {
        super();
        this.userModerationData = new Map();
        this.messageHistory = [];
        this.bot = bot;
        this.config = config;
        this.setupBotEvents();
    }
    setupBotEvents() {
        this.bot.on('message', (message) => {
            if (this.config.enabled) {
                this.processMessage(message);
            }
        });
    }
    async processMessage(message) {
        const user = message.sender;
        const content = message.content;
        if (this.isPrivilegedUser(user)) {
            return;
        }
        this.updateUserModerationData(user.username, content);
        this.messageHistory.push({
            username: user.username,
            content,
            timestamp: new Date(),
        });
        if (this.messageHistory.length > 100) {
            this.messageHistory.shift();
        }
        const violations = [];
        if (this.config.spamProtection.enabled) {
            const spamViolation = this.checkSpam(user.username);
            if (spamViolation)
                violations.push(spamViolation);
        }
        if (this.config.capsFilter.enabled) {
            const capsViolation = this.checkCapsLock(content);
            if (capsViolation)
                violations.push(capsViolation);
        }
        if (this.config.bannedWords.enabled) {
            const bannedWordViolation = this.checkBannedWords(content);
            if (bannedWordViolation)
                violations.push(bannedWordViolation);
        }
        if (this.config.linkProtection.enabled) {
            const linkViolation = this.checkLinks(content);
            if (linkViolation)
                violations.push(linkViolation);
        }
        if (this.config.repetitiveMessages.enabled) {
            const repetitiveViolation = this.checkRepetitiveMessages(user.username, content);
            if (repetitiveViolation)
                violations.push(repetitiveViolation);
        }
        if (violations.length > 0) {
            await this.handleViolations(user, message, violations);
        }
    }
    isPrivilegedUser(user) {
        return user.is_channel_owner || user.is_moderator || user.is_staff;
    }
    updateUserModerationData(username, content) {
        const normalizedUsername = username.toLowerCase();
        const now = new Date();
        if (!this.userModerationData.has(normalizedUsername)) {
            this.userModerationData.set(normalizedUsername, {
                username,
                violations: [],
                messageHistory: [],
                lastMessageTime: now,
                messageCount: 0,
                timeoutCount: 0,
                banCount: 0,
            });
        }
        const userData = this.userModerationData.get(normalizedUsername);
        userData.messageHistory.push({ content, timestamp: now });
        userData.lastMessageTime = now;
        userData.messageCount++;
        if (userData.messageHistory.length > 20) {
            userData.messageHistory.shift();
        }
    }
    checkSpam(username) {
        const normalizedUsername = username.toLowerCase();
        const userData = this.userModerationData.get(normalizedUsername);
        if (!userData)
            return null;
        const timeWindow = this.config.spamProtection.timeWindow * 1000;
        const cutoffTime = new Date(Date.now() - timeWindow);
        const recentMessages = userData.messageHistory.filter(msg => msg.timestamp > cutoffTime);
        if (recentMessages.length > this.config.spamProtection.maxMessages) {
            return `Spam: ${recentMessages.length} messages in ${this.config.spamProtection.timeWindow} seconds`;
        }
        return null;
    }
    checkCapsLock(content) {
        if (content.length < this.config.capsFilter.minLength) {
            return null;
        }
        const uppercaseCount = (content.match(/[A-Z]/g) || []).length;
        const letterCount = (content.match(/[A-Za-z]/g) || []).length;
        if (letterCount === 0)
            return null;
        const capsPercentage = (uppercaseCount / letterCount) * 100;
        if (capsPercentage > this.config.capsFilter.threshold) {
            return `Excessive caps: ${Math.round(capsPercentage)}% uppercase`;
        }
        return null;
    }
    checkBannedWords(content) {
        const lowerContent = content.toLowerCase();
        for (const bannedWord of this.config.bannedWords.words) {
            if (lowerContent.includes(bannedWord.toLowerCase())) {
                return `Banned word detected: ${bannedWord}`;
            }
        }
        return null;
    }
    checkLinks(content) {
        const urlRegex = /(https?:\/\/[^\s]+)/gi;
        const urls = content.match(urlRegex);
        if (!urls)
            return null;
        for (const url of urls) {
            try {
                const domain = new URL(url).hostname.toLowerCase();
                const isAllowed = this.config.linkProtection.allowedDomains.some(allowedDomain => domain.includes(allowedDomain.toLowerCase()));
                if (!isAllowed) {
                    return `Unauthorized link: ${domain}`;
                }
            }
            catch (error) {
                return `Invalid URL detected`;
            }
        }
        return null;
    }
    checkRepetitiveMessages(username, content) {
        const normalizedUsername = username.toLowerCase();
        const userData = this.userModerationData.get(normalizedUsername);
        if (!userData)
            return null;
        const timeWindow = this.config.repetitiveMessages.timeWindow * 1000;
        const cutoffTime = new Date(Date.now() - timeWindow);
        const recentMessages = userData.messageHistory.filter(msg => msg.timestamp > cutoffTime);
        const duplicateCount = recentMessages.filter(msg => msg.content.toLowerCase() === content.toLowerCase()).length;
        if (duplicateCount >= this.config.repetitiveMessages.maxRepeats) {
            return `Repetitive message: sent ${duplicateCount} times`;
        }
        return null;
    }
    async handleViolations(user, message, violations) {
        const normalizedUsername = user.username.toLowerCase();
        const userData = this.userModerationData.get(normalizedUsername);
        for (const violation of violations) {
            const violationData = {
                username: user.username,
                type: violation.split(':')[0] || 'Unknown',
                message: message.content,
                timestamp: new Date(),
                punishment: 'pending',
            };
            userData.violations.push(violationData);
            logger_1.logger.moderationAction('violation', user.username, 'auto-mod', violation);
        }
        const punishment = this.determinePunishment(violations, userData);
        if (punishment) {
            await this.executePunishment(user, punishment, violations);
        }
        this.emit('violations_detected', {
            user,
            message,
            violations,
            punishment,
        });
    }
    determinePunishment(violations, userData) {
        const hasBannedWord = violations.some(v => v.startsWith('Banned word'));
        const hasSpam = violations.some(v => v.startsWith('Spam'));
        if (hasBannedWord) {
            return {
                type: this.config.bannedWords.punishment,
                duration: this.config.bannedWords.duration,
            };
        }
        if (hasSpam) {
            return {
                type: this.config.spamProtection.punishment,
                duration: this.config.spamProtection.duration,
            };
        }
        if (this.config.autoTimeout.escalation && userData.violations.length > 3) {
            const escalatedDuration = Math.min(60, 5 * userData.violations.length);
            return {
                type: 'timeout',
                duration: escalatedDuration,
            };
        }
        return {
            type: 'timeout',
            duration: 5,
        };
    }
    async executePunishment(user, punishment, violations) {
        const normalizedUsername = user.username.toLowerCase();
        const userData = this.userModerationData.get(normalizedUsername);
        switch (punishment.type) {
            case 'timeout':
                if (punishment.duration) {
                    const success = await this.bot.timeoutUser(user.username, punishment.duration);
                    if (success) {
                        userData.timeoutCount++;
                        logger_1.logger.moderationAction('timeout', user.username, 'auto-mod', `${punishment.duration}m - ${violations.join(', ')}`);
                    }
                }
                break;
            case 'ban':
                const success = await this.bot.banUser(user.username, true);
                if (success) {
                    userData.banCount++;
                    logger_1.logger.moderationAction('ban', user.username, 'auto-mod', violations.join(', '));
                }
                break;
            case 'delete':
                logger_1.logger.moderationAction('delete', user.username, 'auto-mod', violations.join(', '));
                break;
            case 'warn':
                await this.bot.sendMessage(`@${user.username} Warning: ${violations.join(', ')}`);
                logger_1.logger.moderationAction('warn', user.username, 'auto-mod', violations.join(', '));
                break;
        }
        const recentViolations = userData.violations.slice(-violations.length);
        recentViolations.forEach(violation => {
            violation.punishment = punishment.type;
            violation.duration = punishment.duration;
        });
        this.emit('punishment_executed', {
            user,
            punishment,
            violations,
        });
    }
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        logger_1.logger.info('Moderation configuration updated');
        this.emit('config_updated', this.config);
    }
    addBannedWord(word) {
        if (!this.config.bannedWords.words.includes(word.toLowerCase())) {
            this.config.bannedWords.words.push(word.toLowerCase());
            logger_1.logger.info(`Added banned word: ${word}`);
            this.emit('banned_word_added', word);
        }
    }
    removeBannedWord(word) {
        const index = this.config.bannedWords.words.indexOf(word.toLowerCase());
        if (index > -1) {
            this.config.bannedWords.words.splice(index, 1);
            logger_1.logger.info(`Removed banned word: ${word}`);
            this.emit('banned_word_removed', word);
            return true;
        }
        return false;
    }
    getUserModerationData(username) {
        return this.userModerationData.get(username.toLowerCase());
    }
    getModerationStats() {
        const totalUsers = this.userModerationData.size;
        let totalViolations = 0;
        let totalTimeouts = 0;
        let totalBans = 0;
        for (const userData of this.userModerationData.values()) {
            totalViolations += userData.violations.length;
            totalTimeouts += userData.timeoutCount;
            totalBans += userData.banCount;
        }
        return {
            totalUsers,
            totalViolations,
            totalTimeouts,
            totalBans,
            config: this.config,
            recentMessages: this.messageHistory.length,
        };
    }
    clearUserData(username) {
        const normalizedUsername = username.toLowerCase();
        if (this.userModerationData.has(normalizedUsername)) {
            this.userModerationData.delete(normalizedUsername);
            logger_1.logger.info(`Cleared moderation data for user: ${username}`);
            return true;
        }
        return false;
    }
    exportModerationData() {
        return {
            config: this.config,
            userData: Array.from(this.userModerationData.entries()),
            messageHistory: this.messageHistory.slice(-50),
        };
    }
}
exports.ModerationManager = ModerationManager;
//# sourceMappingURL=ModerationManager.js.map