// Example: Basic KickBot setup
// This file shows how to set up a basic KickBot instance

const { KickBot, CommandHandler, registerDefaultCommands } = require('../dist/index');
const { CustomCommandManager } = require('../dist/commands/CustomCommandManager');
const { ModerationManager } = require('../dist/moderation/ModerationManager');
const { registerModerationCommands } = require('../dist/commands/moderationCommands');

// Bot configuration
const botConfig = {
  username: 'YourBotUsername',
  password: 'YourBotPassword', // or use token instead
  channel: 'YourChannelName',
  prefix: '!',
  features: {
    moderation: true,
    commands: true,
    songRequests: false,
    games: true,
    analytics: true,
  },
  moderation: {
    maxMessageLength: 500,
    spamThreshold: 5,
    capsThreshold: 70,
    linkProtection: true,
    bannedWords: ['spam', 'scam'],
    autoTimeout: true,
    timeoutDuration: 300, // 5 minutes
  },
};

// Moderation configuration
const moderationConfig = {
  enabled: true,
  spamProtection: {
    enabled: true,
    maxMessages: 5,
    timeWindow: 30, // seconds
    punishment: 'timeout',
    duration: 5, // minutes
  },
  capsFilter: {
    enabled: true,
    threshold: 70, // percentage
    minLength: 10,
    punishment: 'timeout',
    duration: 2, // minutes
  },
  bannedWords: {
    enabled: true,
    words: ['spam', 'scam', 'hack'],
    punishment: 'timeout',
    duration: 10, // minutes
  },
  linkProtection: {
    enabled: true,
    allowedDomains: ['kick.com', 'youtube.com', 'twitch.tv'],
    punishment: 'delete',
    duration: 5, // minutes
  },
  repetitiveMessages: {
    enabled: true,
    maxRepeats: 3,
    timeWindow: 60, // seconds
    punishment: 'timeout',
    duration: 5, // minutes
  },
  autoTimeout: {
    enabled: true,
    escalation: true,
  },
};

async function startBot() {
  try {
    console.log('Starting KickBot...');

    // Create bot instance
    const bot = new KickBot(botConfig);

    // Create command handler
    const commandHandler = new CommandHandler(bot);

    // Create custom command manager
    const customCommandManager = new CustomCommandManager(commandHandler);

    // Create moderation manager
    const moderationManager = new ModerationManager(bot, moderationConfig);

    // Register default commands
    registerDefaultCommands(commandHandler, customCommandManager);

    // Register moderation commands
    registerModerationCommands(commandHandler, moderationManager);

    // Add some example custom commands
    await customCommandManager.createCommand('discord', 'Join our Discord server: https://discord.gg/example', {
      description: 'Show Discord link',
      cooldown: 30,
      createdBy: 'system',
    });

    await customCommandManager.createCommand('social', 'Follow us on Twitter: @YourTwitter | YouTube: YourChannel', {
      description: 'Show social media links',
      cooldown: 60,
      createdBy: 'system',
    });

    await customCommandManager.createCommand('lurk', 'Thanks for lurking, {user}! Enjoy the stream! 👀', {
      description: 'Lurk message',
      cooldown: 10,
      createdBy: 'system',
    });

    // Setup event handlers
    bot.on('started', () => {
      console.log('✅ Bot started successfully!');
      console.log(`📺 Connected to channel: ${botConfig.channel}`);
      console.log('🤖 Bot is now active and monitoring chat');
    });

    bot.on('message', (message) => {
      // Log all messages (optional)
      console.log(`[${message.sender.username}]: ${message.content}`);
    });

    bot.on('new_follower', (data) => {
      console.log(`🎉 New follower: ${data.username}`);
      bot.sendMessage(`Welcome to the stream, @${data.username}! Thanks for following! 🎉`);
    });

    bot.on('error', (error) => {
      console.error('❌ Bot error:', error);
    });

    // Custom command events
    customCommandManager.on('command_created', (command) => {
      console.log(`✅ Custom command created: !${command.name}`);
    });

    customCommandManager.on('command_executed', (data) => {
      console.log(`🎯 Command used: !${data.command.name} by ${data.user}`);
    });

    // Moderation events
    moderationManager.on('violations_detected', (data) => {
      console.log(`⚠️ Violations detected for ${data.user.username}: ${data.violations.join(', ')}`);
    });

    moderationManager.on('punishment_executed', (data) => {
      console.log(`🔨 Punishment executed: ${data.punishment.type} for ${data.user.username}`);
    });

    // Start the bot
    await bot.start();

    // Graceful shutdown
    process.on('SIGINT', async () => {
      console.log('\n🛑 Shutting down bot...');
      await bot.stop();
      process.exit(0);
    });

  } catch (error) {
    console.error('❌ Failed to start bot:', error);
    process.exit(1);
  }
}

// Start the bot
startBot();

// Export for use in other files
module.exports = {
  botConfig,
  moderationConfig,
  startBot,
};
