import { EventEmitter } from 'events';
import { kickAPI, KickChannel } from '../api/kickApi';
import { KickWebSocket } from '../api/kickWebSocket';
import { config } from '../config';
import { logger } from '../utils/logger';
import { ChatMessage, BotConfig, PermissionLevel, BotEvent } from '../types';

export class KickBot extends EventEmitter {
  private webSocket: KickWebSocket;
  private channel?: KickChannel;
  private isRunning = false;
  private startTime: Date;
  private messageCount = 0;
  private commandCount = 0;

  constructor(private botConfig: BotConfig) {
    super();
    this.webSocket = new KickWebSocket();
    this.startTime = new Date();
    this.setupEventHandlers();
  }

  /**
   * Setup event handlers for WebSocket events
   */
  private setupEventHandlers(): void {
    this.webSocket.on('connected', () => {
      logger.info('Bot connected to Kick WebSocket');
      this.emit('connected');
    });

    this.webSocket.on('subscribed', () => {
      logger.info('Bot subscribed to channel chat');
      this.emit('subscribed');
    });

    this.webSocket.on('message', (message: ChatMessage) => {
      this.handleMessage(message);
    });

    this.webSocket.on('new_follower', (data: any) => {
      logger.info(`New follower: ${data.username}`);
      this.emit('new_follower', data);
    });

    this.webSocket.on('user_banned', (data: any) => {
      this.emit('user_banned', data);
    });

    this.webSocket.on('user_unbanned', (data: any) => {
      this.emit('user_unbanned', data);
    });

    this.webSocket.on('streamer_live', (data: any) => {
      this.emit('streamer_live', data);
    });

    this.webSocket.on('streamer_offline', (data: any) => {
      this.emit('streamer_offline', data);
    });

    this.webSocket.on('error', (error: Error) => {
      logger.error('WebSocket error:', error);
      this.emit('error', error);
    });
  }

  /**
   * Start the bot
   */
  async start(): Promise<void> {
    try {
      logger.info('Starting KickBot...');

      // Authenticate with Kick API
      if (this.botConfig.password) {
        const authenticated = await kickAPI.authenticate(this.botConfig.username, this.botConfig.password);
        if (!authenticated) {
          throw new Error('Failed to authenticate with Kick API');
        }
      }

      // Get channel information
      this.channel = await kickAPI.getChannel(this.botConfig.channel);
      if (!this.channel) {
        throw new Error(`Failed to get channel information for: ${this.botConfig.channel}`);
      }

      logger.info(`Connected to channel: ${this.channel.slug} (ID: ${this.channel.id})`);

      // Connect to WebSocket
      await this.webSocket.connect(this.channel.id, this.channel.chatroom.id);

      this.isRunning = true;
      this.startTime = new Date();

      logger.info('KickBot started successfully!');
      this.emit('started');

    } catch (error) {
      logger.error('Failed to start bot:', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Stop the bot
   */
  async stop(): Promise<void> {
    logger.info('Stopping KickBot...');
    
    this.isRunning = false;
    this.webSocket.disconnect();
    
    logger.info('KickBot stopped');
    this.emit('stopped');
  }

  /**
   * Handle incoming chat messages
   */
  private handleMessage(message: ChatMessage): void {
    this.messageCount++;

    // Emit raw message event
    this.emit('message', message);

    // Check if message is a command
    if (message.content.startsWith(this.botConfig.prefix)) {
      this.handleCommand(message);
    }

    // Create bot event
    const botEvent: BotEvent = {
      type: 'message',
      data: message,
      timestamp: new Date().toISOString(),
    };

    this.emit('bot_event', botEvent);
  }

  /**
   * Handle command messages
   */
  private handleCommand(message: ChatMessage): void {
    const content = message.content.slice(this.botConfig.prefix.length);
    const args = content.split(' ');
    const commandName = args[0]?.toLowerCase();

    if (!commandName) return;

    this.commandCount++;

    logger.commandUsed(
      message.sender.username,
      commandName,
      args.slice(1),
      `chatroom-${message.chatroom_id}`
    );

    // Emit command event
    this.emit('command', {
      name: commandName,
      args: args.slice(1),
      message,
      user: message.sender,
    });
  }

  /**
   * Send a message to the chat
   */
  async sendMessage(content: string): Promise<boolean> {
    if (!this.channel) {
      logger.error('Cannot send message: not connected to channel');
      return false;
    }

    try {
      const success = await kickAPI.sendMessage(this.channel.chatroom.id, content);
      if (success) {
        logger.info(`Sent message: ${content}`);
      }
      return success;
    } catch (error) {
      logger.error('Failed to send message:', error);
      return false;
    }
  }

  /**
   * Reply to a specific message
   */
  async replyToMessage(originalMessage: ChatMessage, content: string): Promise<boolean> {
    const replyContent = `@${originalMessage.sender.username} ${content}`;
    return this.sendMessage(replyContent);
  }

  /**
   * Ban a user
   */
  async banUser(username: string, permanent: boolean = false): Promise<boolean> {
    if (!this.channel) {
      logger.error('Cannot ban user: not connected to channel');
      return false;
    }

    try {
      const success = await kickAPI.banUser(this.channel.chatroom.id, username, permanent);
      if (success) {
        logger.moderationAction('ban', username, this.botConfig.username);
      }
      return success;
    } catch (error) {
      logger.error(`Failed to ban user ${username}:`, error);
      return false;
    }
  }

  /**
   * Timeout a user
   */
  async timeoutUser(username: string, duration: number): Promise<boolean> {
    if (!this.channel) {
      logger.error('Cannot timeout user: not connected to channel');
      return false;
    }

    try {
      const success = await kickAPI.timeoutUser(this.channel.chatroom.id, username, duration);
      if (success) {
        logger.moderationAction('timeout', username, this.botConfig.username, `${duration} minutes`);
      }
      return success;
    } catch (error) {
      logger.error(`Failed to timeout user ${username}:`, error);
      return false;
    }
  }

  /**
   * Unban a user
   */
  async unbanUser(username: string): Promise<boolean> {
    if (!this.channel) {
      logger.error('Cannot unban user: not connected to channel');
      return false;
    }

    try {
      const success = await kickAPI.unbanUser(this.channel.chatroom.id, username);
      if (success) {
        logger.moderationAction('unban', username, this.botConfig.username);
      }
      return success;
    } catch (error) {
      logger.error(`Failed to unban user ${username}:`, error);
      return false;
    }
  }

  /**
   * Get user permission level
   */
  getUserPermissionLevel(user: any): PermissionLevel {
    if (user.is_channel_owner) return PermissionLevel.OWNER;
    if (user.is_moderator) return PermissionLevel.MODERATOR;
    if (user.is_vip) return PermissionLevel.VIP;
    if (user.is_subscriber) return PermissionLevel.SUBSCRIBER;
    if (user.is_follower) return PermissionLevel.FOLLOWER;
    return PermissionLevel.EVERYONE;
  }

  /**
   * Check if user has required permission level
   */
  hasPermission(user: any, requiredLevel: PermissionLevel): boolean {
    const userLevel = this.getUserPermissionLevel(user);
    return userLevel >= requiredLevel;
  }

  /**
   * Get bot statistics
   */
  getStats() {
    const uptime = Date.now() - this.startTime.getTime();
    
    return {
      isRunning: this.isRunning,
      uptime,
      uptimeFormatted: this.formatUptime(uptime),
      startTime: this.startTime,
      messageCount: this.messageCount,
      commandCount: this.commandCount,
      channel: this.channel?.slug,
      connected: this.webSocket.connected,
    };
  }

  /**
   * Format uptime in human readable format
   */
  private formatUptime(uptime: number): string {
    const seconds = Math.floor(uptime / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}d ${hours % 24}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * Get current channel information
   */
  get currentChannel(): KickChannel | undefined {
    return this.channel;
  }

  /**
   * Check if bot is running
   */
  get running(): boolean {
    return this.isRunning;
  }

  /**
   * Check if bot is connected to WebSocket
   */
  get connected(): boolean {
    return this.webSocket.connected;
  }
}
