import dotenv from 'dotenv';
import { BotConfig, PermissionLevel } from '../types';

dotenv.config();

export const config: BotConfig = {
  username: process.env.BOT_USERNAME || '',
  password: process.env.BOT_PASSWORD,
  token: process.env.BOT_TOKEN,
  channel: process.env.DEFAULT_CHANNEL || '',
  prefix: process.env.COMMAND_PREFIX || '!',
  features: {
    moderation: process.env.ENABLE_MODERATION === 'true',
    commands: process.env.ENABLE_COMMANDS === 'true',
    songRequests: process.env.ENABLE_SONG_REQUESTS === 'true',
    games: process.env.ENABLE_GAMES === 'true',
    analytics: process.env.ENABLE_ANALYTICS === 'true',
  },
  moderation: {
    maxMessageLength: parseInt(process.env.MAX_MESSAGE_LENGTH || '500'),
    spamThreshold: parseInt(process.env.SPAM_THRESHOLD || '5'),
    capsThreshold: parseInt(process.env.CAPS_THRESHOLD || '70'),
    linkProtection: process.env.LINK_PROTECTION === 'true',
    bannedWords: (process.env.BANNED_WORDS || '').split(',').filter(word => word.trim()),
    autoTimeout: process.env.AUTO_TIMEOUT === 'true',
    timeoutDuration: parseInt(process.env.TIMEOUT_DURATION || '300'), // 5 minutes
  },
};

export const webConfig = {
  port: parseInt(process.env.WEB_PORT || '3000'),
  jwtSecret: process.env.JWT_SECRET || 'your-secret-key',
  adminUsername: process.env.ADMIN_USERNAME || 'admin',
  adminPassword: process.env.ADMIN_PASSWORD || 'admin123',
};

export const kickConfig = {
  apiBaseUrl: process.env.KICK_API_BASE_URL || 'https://kick.com/api/v2',
  websocketUrl: process.env.KICK_WEBSOCKET_URL || 'wss://ws-us2.pusher.app/app/32cbd69e4b950bf97679',
  pusherKey: '32cbd69e4b950bf97679',
  pusherCluster: 'us2',
};

export const databaseConfig = {
  path: process.env.DATABASE_PATH || './data/kickbot.db',
};

export const logConfig = {
  level: process.env.LOG_LEVEL || 'info',
  file: process.env.LOG_FILE || './logs/kickbot.log',
};

// Default commands that come with the bot
export const defaultCommands = [
  {
    name: 'help',
    description: 'Shows available commands',
    usage: '!help [command]',
    aliases: ['commands', 'h'],
    cooldown: 5,
    permission: PermissionLevel.EVERYONE,
    enabled: true,
  },
  {
    name: 'uptime',
    description: 'Shows bot uptime',
    usage: '!uptime',
    aliases: ['up'],
    cooldown: 10,
    permission: PermissionLevel.EVERYONE,
    enabled: true,
  },
  {
    name: 'roll',
    description: 'Roll a dice',
    usage: '!roll [max]',
    aliases: ['dice', 'random'],
    cooldown: 3,
    permission: PermissionLevel.EVERYONE,
    enabled: true,
  },
  {
    name: '8ball',
    description: 'Ask the magic 8-ball a question',
    usage: '!8ball <question>',
    aliases: ['eightball', 'magic8ball'],
    cooldown: 5,
    permission: PermissionLevel.EVERYONE,
    enabled: true,
  },
  {
    name: 'timeout',
    description: 'Timeout a user',
    usage: '!timeout <username> [duration] [reason]',
    aliases: ['to'],
    cooldown: 0,
    permission: PermissionLevel.MODERATOR,
    enabled: true,
  },
  {
    name: 'ban',
    description: 'Ban a user',
    usage: '!ban <username> [reason]',
    aliases: [],
    cooldown: 0,
    permission: PermissionLevel.MODERATOR,
    enabled: true,
  },
  {
    name: 'unban',
    description: 'Unban a user',
    usage: '!unban <username>',
    aliases: [],
    cooldown: 0,
    permission: PermissionLevel.MODERATOR,
    enabled: true,
  },
];
