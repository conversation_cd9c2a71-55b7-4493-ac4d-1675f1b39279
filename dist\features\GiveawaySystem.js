"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GiveawaySystem = void 0;
const events_1 = require("events");
const Database_1 = require("../database/Database");
const logger_1 = require("../utils/logger");
class GiveawaySystem extends events_1.EventEmitter {
    constructor(bot) {
        super();
        this.activeGiveaways = new Map();
        this.giveawayTimers = new Map();
        this.bot = bot;
        this.setupEventHandlers();
    }
    setupEventHandlers() {
        this.bot.on('started', () => {
            this.loadActiveGiveaways();
        });
    }
    async loadActiveGiveaways() {
        try {
            const giveaways = await Database_1.database.db.all(`
        SELECT * FROM giveaways 
        WHERE status = 'active'
      `);
            for (const giveawayData of giveaways) {
                const giveaway = await this.loadGiveawayWithEntries(giveawayData.id);
                if (giveaway) {
                    this.activeGiveaways.set(giveaway.id, giveaway);
                    this.scheduleGiveawayEnd(giveaway);
                }
            }
            logger_1.logger.info(`Loaded ${giveaways.length} active giveaways`);
        }
        catch (error) {
            logger_1.logger.error('Failed to load active giveaways:', error);
        }
    }
    async loadGiveawayWithEntries(giveawayId) {
        try {
            const giveawayData = await Database_1.database.db.get('SELECT * FROM giveaways WHERE id = ?', [giveawayId]);
            if (!giveawayData)
                return null;
            const entries = await Database_1.database.db.all(`
        SELECT username, entries, timestamp 
        FROM giveaway_entries 
        WHERE giveaway_id = ?
      `, [giveawayId]);
            return {
                id: giveawayData.id,
                title: giveawayData.title,
                description: giveawayData.description,
                prize: giveawayData.prize,
                duration: giveawayData.duration,
                maxEntries: giveawayData.max_entries,
                entryCost: giveawayData.entry_cost,
                status: giveawayData.status,
                winner: giveawayData.winner,
                createdBy: giveawayData.created_by,
                createdAt: new Date(giveawayData.created_at),
                endedAt: giveawayData.ended_at ? new Date(giveawayData.ended_at) : undefined,
                entries: entries.map((entry) => ({
                    username: entry.username,
                    entries: entry.entries,
                    timestamp: new Date(entry.timestamp),
                })),
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to load giveaway ${giveawayId}:`, error);
            return null;
        }
    }
    async createGiveaway(title, prize, duration, createdBy, options = {}) {
        try {
            const result = await Database_1.database.db.run(`
        INSERT INTO giveaways (title, description, prize, duration, max_entries, entry_cost, created_by)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
                title,
                options.description || null,
                prize,
                duration,
                options.maxEntries || -1,
                options.entryCost || 0,
                createdBy
            ]);
            const giveawayId = result.lastID;
            const giveaway = {
                id: giveawayId,
                title,
                description: options.description,
                prize,
                duration,
                maxEntries: options.maxEntries || -1,
                entryCost: options.entryCost || 0,
                status: 'active',
                createdBy,
                createdAt: new Date(),
                entries: [],
            };
            this.activeGiveaways.set(giveawayId, giveaway);
            this.scheduleGiveawayEnd(giveaway);
            const announcement = `🎉 NEW GIVEAWAY: ${title} | Prize: ${prize} | Duration: ${duration}m${options.entryCost ? ` | Cost: ${options.entryCost} points` : ''} | Use !enter to join!`;
            await this.bot.sendMessage(announcement);
            this.emit('giveaway_created', giveaway);
            logger_1.logger.info(`Created giveaway: ${title} (ID: ${giveawayId})`);
            return {
                success: true,
                giveaway,
                message: `Giveaway "${title}" created successfully!`
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to create giveaway:', error);
            return {
                success: false,
                message: 'Failed to create giveaway'
            };
        }
    }
    async enterGiveaway(username, giveawayId) {
        try {
            if (!giveawayId) {
                const activeGiveawayIds = Array.from(this.activeGiveaways.keys());
                if (activeGiveawayIds.length === 0) {
                    return { success: false, message: 'No active giveaways' };
                }
                giveawayId = Math.max(...activeGiveawayIds);
            }
            const giveaway = this.activeGiveaways.get(giveawayId);
            if (!giveaway || giveaway.status !== 'active') {
                return { success: false, message: 'Giveaway not found or not active' };
            }
            const existingEntry = giveaway.entries.find(entry => entry.username.toLowerCase() === username.toLowerCase());
            if (existingEntry) {
                return { success: false, message: 'You are already entered in this giveaway' };
            }
            if (giveaway.maxEntries > 0 && giveaway.entries.length >= giveaway.maxEntries) {
                return { success: false, message: 'Giveaway is full' };
            }
            if (giveaway.entryCost > 0) {
                const user = await Database_1.database.getUser(username);
                if (!user || user.points < giveaway.entryCost) {
                    return { success: false, message: `Insufficient points. Need ${giveaway.entryCost} points to enter` };
                }
                await Database_1.database.updateUser(username, { points: user.points - giveaway.entryCost });
            }
            const entry = {
                username,
                entries: 1,
                timestamp: new Date(),
            };
            giveaway.entries.push(entry);
            await Database_1.database.db.run(`
        INSERT INTO giveaway_entries (giveaway_id, username, entries)
        VALUES (?, ?, ?)
      `, [giveawayId, username, 1]);
            this.emit('giveaway_entry', { giveaway, entry });
            logger_1.logger.debug(`${username} entered giveaway: ${giveaway.title}`);
            return {
                success: true,
                message: `Successfully entered giveaway "${giveaway.title}"! (${giveaway.entries.length} total entries)`
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to enter giveaway for ${username}:`, error);
            return { success: false, message: 'Failed to enter giveaway' };
        }
    }
    async endGiveaway(giveawayId, forcedBy) {
        try {
            const giveaway = this.activeGiveaways.get(giveawayId);
            if (!giveaway) {
                return { success: false, message: 'Giveaway not found' };
            }
            if (giveaway.entries.length === 0) {
                giveaway.status = 'ended';
                await Database_1.database.db.run(`
          UPDATE giveaways 
          SET status = 'ended', ended_at = CURRENT_TIMESTAMP 
          WHERE id = ?
        `, [giveawayId]);
                this.activeGiveaways.delete(giveawayId);
                this.clearGiveawayTimer(giveawayId);
                const message = `Giveaway "${giveaway.title}" ended with no entries.`;
                await this.bot.sendMessage(message);
                return { success: true, message };
            }
            const totalEntries = giveaway.entries.reduce((sum, entry) => sum + entry.entries, 0);
            const randomNum = Math.floor(Math.random() * totalEntries) + 1;
            let currentSum = 0;
            let winner = '';
            for (const entry of giveaway.entries) {
                currentSum += entry.entries;
                if (randomNum <= currentSum) {
                    winner = entry.username;
                    break;
                }
            }
            giveaway.status = 'ended';
            giveaway.winner = winner;
            giveaway.endedAt = new Date();
            await Database_1.database.db.run(`
        UPDATE giveaways 
        SET status = 'ended', winner = ?, ended_at = CURRENT_TIMESTAMP 
        WHERE id = ?
      `, [winner, giveawayId]);
            this.activeGiveaways.delete(giveawayId);
            this.clearGiveawayTimer(giveawayId);
            const announcement = `🎊 GIVEAWAY WINNER: @${winner} won "${giveaway.prize}" from "${giveaway.title}"! Congratulations! 🎊`;
            await this.bot.sendMessage(announcement);
            this.emit('giveaway_ended', { giveaway, winner });
            logger_1.logger.info(`Giveaway "${giveaway.title}" ended. Winner: ${winner}`);
            return {
                success: true,
                winner,
                message: `Giveaway ended. Winner: ${winner}`
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to end giveaway ${giveawayId}:`, error);
            return { success: false, message: 'Failed to end giveaway' };
        }
    }
    async cancelGiveaway(giveawayId, cancelledBy) {
        try {
            const giveaway = this.activeGiveaways.get(giveawayId);
            if (!giveaway) {
                return { success: false, message: 'Giveaway not found' };
            }
            if (giveaway.entryCost > 0) {
                for (const entry of giveaway.entries) {
                    const user = await Database_1.database.getUser(entry.username);
                    if (user) {
                        await Database_1.database.updateUser(entry.username, {
                            points: user.points + (giveaway.entryCost * entry.entries)
                        });
                    }
                }
            }
            giveaway.status = 'cancelled';
            await Database_1.database.db.run(`
        UPDATE giveaways 
        SET status = 'cancelled', ended_at = CURRENT_TIMESTAMP 
        WHERE id = ?
      `, [giveawayId]);
            this.activeGiveaways.delete(giveawayId);
            this.clearGiveawayTimer(giveawayId);
            const message = `Giveaway "${giveaway.title}" has been cancelled${giveaway.entryCost > 0 ? '. Entry costs have been refunded' : ''}.`;
            await this.bot.sendMessage(message);
            this.emit('giveaway_cancelled', giveaway);
            logger_1.logger.info(`Giveaway "${giveaway.title}" cancelled by ${cancelledBy}`);
            return { success: true, message };
        }
        catch (error) {
            logger_1.logger.error(`Failed to cancel giveaway ${giveawayId}:`, error);
            return { success: false, message: 'Failed to cancel giveaway' };
        }
    }
    getActiveGiveaways() {
        return Array.from(this.activeGiveaways.values());
    }
    getGiveaway(giveawayId) {
        return this.activeGiveaways.get(giveawayId);
    }
    scheduleGiveawayEnd(giveaway) {
        const endTime = giveaway.createdAt.getTime() + (giveaway.duration * 60 * 1000);
        const timeUntilEnd = endTime - Date.now();
        if (timeUntilEnd > 0) {
            const timer = setTimeout(() => {
                this.endGiveaway(giveaway.id);
            }, timeUntilEnd);
            this.giveawayTimers.set(giveaway.id, timer);
        }
        else {
            this.endGiveaway(giveaway.id);
        }
    }
    clearGiveawayTimer(giveawayId) {
        const timer = this.giveawayTimers.get(giveawayId);
        if (timer) {
            clearTimeout(timer);
            this.giveawayTimers.delete(giveawayId);
        }
    }
    async getStats() {
        try {
            const totalGiveaways = await Database_1.database.db.get('SELECT COUNT(*) as count FROM giveaways');
            const activeGiveaways = await Database_1.database.db.get('SELECT COUNT(*) as count FROM giveaways WHERE status = "active"');
            const totalEntries = await Database_1.database.db.get('SELECT COUNT(*) as count FROM giveaway_entries');
            const recentWinners = await Database_1.database.db.all(`
        SELECT winner, title, prize, ended_at 
        FROM giveaways 
        WHERE winner IS NOT NULL 
        ORDER BY ended_at DESC 
        LIMIT 5
      `);
            return {
                totalGiveaways: totalGiveaways.count,
                activeGiveaways: activeGiveaways.count,
                totalEntries: totalEntries.count,
                recentWinners: recentWinners.map((g) => ({
                    winner: g.winner,
                    title: g.title,
                    prize: g.prize,
                    endedAt: g.ended_at,
                })),
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get giveaway stats:', error);
            return {
                totalGiveaways: 0,
                activeGiveaways: 0,
                totalEntries: 0,
                recentWinners: [],
            };
        }
    }
    cleanup() {
        for (const timer of this.giveawayTimers.values()) {
            clearTimeout(timer);
        }
        this.giveawayTimers.clear();
    }
}
exports.GiveawaySystem = GiveawaySystem;
//# sourceMappingURL=GiveawaySystem.js.map