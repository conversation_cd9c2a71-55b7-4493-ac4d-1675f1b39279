"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerDefaultCommands = registerDefaultCommands;
const types_1 = require("../types");
const logger_1 = require("../utils/logger");
function registerDefaultCommands(commandHandler, customCommandManager) {
    commandHandler.registerCommand({
        name: 'help',
        description: 'Shows available commands',
        usage: '!help [command]',
        aliases: ['commands', 'h'],
        cooldown: 5,
        permission: types_1.PermissionLevel.EVERYONE,
        enabled: true,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    }, helpCommand);
    commandHandler.registerCommand({
        name: 'uptime',
        description: 'Shows bot uptime',
        usage: '!uptime',
        aliases: ['up'],
        cooldown: 10,
        permission: types_1.PermissionLevel.EVERYONE,
        enabled: true,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    }, uptimeCommand);
    commandHandler.registerCommand({
        name: 'roll',
        description: 'Roll a dice',
        usage: '!roll [max]',
        aliases: ['dice', 'random'],
        cooldown: 3,
        permission: types_1.PermissionLevel.EVERYONE,
        enabled: true,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    }, rollCommand);
    commandHandler.registerCommand({
        name: '8ball',
        description: 'Ask the magic 8-ball a question',
        usage: '!8ball <question>',
        aliases: ['eightball', 'magic8ball'],
        cooldown: 5,
        permission: types_1.PermissionLevel.EVERYONE,
        enabled: true,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    }, eightBallCommand);
    commandHandler.registerCommand({
        name: 'timeout',
        description: 'Timeout a user',
        usage: '!timeout <username> [duration] [reason]',
        aliases: ['to'],
        cooldown: 0,
        permission: types_1.PermissionLevel.MODERATOR,
        enabled: true,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    }, timeoutCommand);
    commandHandler.registerCommand({
        name: 'ban',
        description: 'Ban a user',
        usage: '!ban <username> [reason]',
        aliases: [],
        cooldown: 0,
        permission: types_1.PermissionLevel.MODERATOR,
        enabled: true,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    }, banCommand);
    commandHandler.registerCommand({
        name: 'unban',
        description: 'Unban a user',
        usage: '!unban <username>',
        aliases: [],
        cooldown: 0,
        permission: types_1.PermissionLevel.MODERATOR,
        enabled: true,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    }, unbanCommand);
    if (customCommandManager) {
        commandHandler.registerCommand({
            name: 'addcom',
            description: 'Add a custom command',
            usage: '!addcom <name> <response>',
            aliases: ['addcommand'],
            cooldown: 0,
            permission: types_1.PermissionLevel.MODERATOR,
            enabled: true,
            created_by: 'system',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
        }, async (context) => await addCommandHandler(context, customCommandManager));
        commandHandler.registerCommand({
            name: 'editcom',
            description: 'Edit a custom command',
            usage: '!editcom <name> <new_response>',
            aliases: ['editcommand'],
            cooldown: 0,
            permission: types_1.PermissionLevel.MODERATOR,
            enabled: true,
            created_by: 'system',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
        }, async (context) => await editCommandHandler(context, customCommandManager));
        commandHandler.registerCommand({
            name: 'delcom',
            description: 'Delete a custom command',
            usage: '!delcom <name>',
            aliases: ['deletecommand', 'removecommand'],
            cooldown: 0,
            permission: types_1.PermissionLevel.MODERATOR,
            enabled: true,
            created_by: 'system',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
        }, async (context) => await deleteCommandHandler(context, customCommandManager));
        commandHandler.registerCommand({
            name: 'commands',
            description: 'List all custom commands',
            usage: '!commands',
            aliases: ['listcommands'],
            cooldown: 10,
            permission: types_1.PermissionLevel.EVERYONE,
            enabled: true,
            created_by: 'system',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
        }, async (context) => await listCommandsHandler(context, customCommandManager));
    }
    logger_1.logger.info('Registered default commands');
}
async function helpCommand(context) {
    const { bot, message, args, user } = context;
    if (args.length > 0) {
        const commandName = args[0].toLowerCase();
        const command = context.bot.emit('get_command', commandName);
        if (command) {
            const helpText = `Command: ${command.name} | Usage: ${command.usage} | Description: ${command.description}`;
            await bot.replyToMessage(message, helpText);
        }
        else {
            await bot.replyToMessage(message, `Command "${commandName}" not found.`);
        }
    }
    else {
        const userPermission = bot.getUserPermissionLevel(user);
        const helpText = `Available commands: !help, !uptime, !roll, !8ball${userPermission >= types_1.PermissionLevel.MODERATOR ? ', !timeout, !ban, !unban' : ''}. Use !help <command> for details.`;
        await bot.replyToMessage(message, helpText);
    }
}
async function uptimeCommand(context) {
    const { bot, message } = context;
    const stats = bot.getStats();
    await bot.replyToMessage(message, `Bot uptime: ${stats.uptimeFormatted} | Messages: ${stats.messageCount} | Commands: ${stats.commandCount}`);
}
async function rollCommand(context) {
    const { bot, message, args, user } = context;
    let max = 100;
    if (args.length > 0) {
        const parsedMax = parseInt(args[0]);
        if (!isNaN(parsedMax) && parsedMax > 0 && parsedMax <= 1000000) {
            max = parsedMax;
        }
    }
    const result = Math.floor(Math.random() * max) + 1;
    await bot.replyToMessage(message, `🎲 ${user.username} rolled ${result} (1-${max})`);
}
async function eightBallCommand(context) {
    const { bot, message, args, user } = context;
    if (args.length === 0) {
        await bot.replyToMessage(message, 'Please ask a question! Usage: !8ball <question>');
        return;
    }
    const responses = [
        'It is certain',
        'It is decidedly so',
        'Without a doubt',
        'Yes definitely',
        'You may rely on it',
        'As I see it, yes',
        'Most likely',
        'Outlook good',
        'Yes',
        'Signs point to yes',
        'Reply hazy, try again',
        'Ask again later',
        'Better not tell you now',
        'Cannot predict now',
        'Concentrate and ask again',
        "Don't count on it",
        'My reply is no',
        'My sources say no',
        'Outlook not so good',
        'Very doubtful'
    ];
    const response = responses[Math.floor(Math.random() * responses.length)];
    await bot.replyToMessage(message, `🎱 ${response}`);
}
async function timeoutCommand(context) {
    const { bot, message, args } = context;
    if (args.length === 0) {
        await bot.replyToMessage(message, 'Usage: !timeout <username> [duration] [reason]');
        return;
    }
    const username = args[0];
    let duration = 300;
    let reason = 'No reason provided';
    if (args.length > 1) {
        const parsedDuration = parseInt(args[1]);
        if (!isNaN(parsedDuration) && parsedDuration > 0 && parsedDuration <= 1440) {
            duration = parsedDuration;
        }
    }
    if (args.length > 2) {
        reason = args.slice(2).join(' ');
    }
    const success = await bot.timeoutUser(username, duration);
    if (success) {
        await bot.replyToMessage(message, `⏰ ${username} has been timed out for ${duration} minutes. Reason: ${reason}`);
    }
    else {
        await bot.replyToMessage(message, `Failed to timeout ${username}.`);
    }
}
async function banCommand(context) {
    const { bot, message, args } = context;
    if (args.length === 0) {
        await bot.replyToMessage(message, 'Usage: !ban <username> [reason]');
        return;
    }
    const username = args[0];
    const reason = args.length > 1 ? args.slice(1).join(' ') : 'No reason provided';
    const success = await bot.banUser(username, true);
    if (success) {
        await bot.replyToMessage(message, `🔨 ${username} has been banned. Reason: ${reason}`);
    }
    else {
        await bot.replyToMessage(message, `Failed to ban ${username}.`);
    }
}
async function unbanCommand(context) {
    const { bot, message, args } = context;
    if (args.length === 0) {
        await bot.replyToMessage(message, 'Usage: !unban <username>');
        return;
    }
    const username = args[0];
    const success = await bot.unbanUser(username);
    if (success) {
        await bot.replyToMessage(message, `✅ ${username} has been unbanned.`);
    }
    else {
        await bot.replyToMessage(message, `Failed to unban ${username}.`);
    }
}
async function addCommandHandler(context, customCommandManager) {
    const { bot, message, args, user } = context;
    if (args.length < 2) {
        await bot.replyToMessage(message, 'Usage: !addcom <name> <response>');
        return;
    }
    const commandName = args[0].toLowerCase();
    const response = args.slice(1).join(' ');
    const success = await customCommandManager.createCommand(commandName, response, {
        createdBy: user.username,
    });
    if (success) {
        await bot.replyToMessage(message, `✅ Command !${commandName} has been created.`);
    }
    else {
        await bot.replyToMessage(message, `❌ Failed to create command !${commandName}. It may already exist.`);
    }
}
async function editCommandHandler(context, customCommandManager) {
    const { bot, message, args } = context;
    if (args.length < 2) {
        await bot.replyToMessage(message, 'Usage: !editcom <name> <new_response>');
        return;
    }
    const commandName = args[0].toLowerCase();
    const newResponse = args.slice(1).join(' ');
    const success = await customCommandManager.updateCommand(commandName, {
        response: newResponse,
    });
    if (success) {
        await bot.replyToMessage(message, `✅ Command !${commandName} has been updated.`);
    }
    else {
        await bot.replyToMessage(message, `❌ Failed to update command !${commandName}. It may not exist.`);
    }
}
async function deleteCommandHandler(context, customCommandManager) {
    const { bot, message, args } = context;
    if (args.length === 0) {
        await bot.replyToMessage(message, 'Usage: !delcom <name>');
        return;
    }
    const commandName = args[0].toLowerCase();
    const success = await customCommandManager.deleteCommand(commandName);
    if (success) {
        await bot.replyToMessage(message, `✅ Command !${commandName} has been deleted.`);
    }
    else {
        await bot.replyToMessage(message, `❌ Failed to delete command !${commandName}. It may not exist.`);
    }
}
async function listCommandsHandler(context, customCommandManager) {
    const { bot, message, user } = context;
    const userPermission = bot.getUserPermissionLevel(user);
    const customCommands = customCommandManager.getCustomCommandsByPermission(userPermission);
    if (customCommands.length === 0) {
        await bot.replyToMessage(message, 'No custom commands available.');
        return;
    }
    const commandNames = customCommands
        .filter(cmd => cmd.enabled)
        .map(cmd => `!${cmd.name}`)
        .slice(0, 10);
    const response = `Custom commands: ${commandNames.join(', ')}${customCommands.length > 10 ? ` and ${customCommands.length - 10} more...` : ''}`;
    await bot.replyToMessage(message, response);
}
//# sourceMappingURL=defaultCommands.js.map