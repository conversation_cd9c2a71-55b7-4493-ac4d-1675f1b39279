import axios, { AxiosInstance } from 'axios';
import { kickConfig } from '../config';
import { logger } from '../utils/logger';

export interface KickChannel {
  id: number;
  user_id: number;
  slug: string;
  is_banned: boolean;
  playback_url: string;
  name_updated_at: string | null;
  vod_enabled: boolean;
  subscription_enabled: boolean;
  can_host: boolean;
  user: {
    id: number;
    username: string;
    agreed_to_terms: boolean;
    email_verified_at: string | null;
    bio: string;
    country: string;
    state: string;
    city: string;
    instagram: string;
    twitter: string;
    youtube: string;
    discord: string;
    tiktok: string;
    facebook: string;
  };
  chatroom: {
    id: number;
    chatable_type: string;
    channel_id: number;
    created_at: string;
    updated_at: string;
    chat_mode_old: string;
    chat_mode: string;
    slow_mode: boolean;
    chatable_id: number;
    followers_mode: boolean;
    subscribers_mode: boolean;
    emotes_mode: boolean;
    message_interval: number;
    following_min_duration: number;
  };
}

export interface KickUser {
  id: number;
  username: string;
  slug: string;
  follower_badges: any[];
  subscriber_badges: any[];
  is_staff: boolean;
  is_channel_owner: boolean;
  is_moderator: boolean;
  is_vip: boolean;
  is_subscriber: boolean;
  is_follower: boolean;
  is_banned: boolean;
  profile_pic: string;
}

export interface ChatroomSettings {
  id: number;
  slow_mode: boolean;
  followers_mode: boolean;
  subscribers_mode: boolean;
  emotes_mode: boolean;
  message_interval: number;
  following_min_duration: number;
}

class KickAPI {
  private api: AxiosInstance;
  private authToken?: string;
  private userId?: number;

  constructor() {
    this.api = axios.create({
      baseURL: kickConfig.apiBaseUrl,
      timeout: 10000,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'User-Agent': 'KickBot/1.0.0',
      },
    });

    // Add request interceptor for logging
    this.api.interceptors.request.use(
      (config) => {
        logger.debug(`API Request: ${config.method?.toUpperCase()} ${config.url}`, {
          params: config.params,
          data: config.data,
        });
        return config;
      },
      (error) => {
        logger.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging
    this.api.interceptors.response.use(
      (response) => {
        logger.debug(`API Response: ${response.status} ${response.config.url}`, {
          data: response.data,
        });
        return response;
      },
      (error) => {
        logger.error('API Response Error:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          url: error.config?.url,
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Authenticate with Kick using email and password
   */
  async authenticate(email: string, password: string): Promise<boolean> {
    try {
      // First, get CSRF token
      const csrfResponse = await this.api.get('/sanctum/csrf-cookie');
      
      // Extract CSRF token from cookies
      const cookies = csrfResponse.headers['set-cookie'];
      let csrfToken = '';
      
      if (cookies) {
        for (const cookie of cookies) {
          if (cookie.includes('XSRF-TOKEN=')) {
            csrfToken = cookie.split('XSRF-TOKEN=')[1]?.split(';')[0] || '';
            break;
          }
        }
      }

      // Login request
      const loginResponse = await this.api.post('/login', {
        email,
        password,
        remember: true,
      }, {
        headers: {
          'X-XSRF-TOKEN': decodeURIComponent(csrfToken),
          'Cookie': cookies?.join('; ') || '',
        },
      });

      if (loginResponse.status === 200) {
        // Extract auth token from response
        this.authToken = loginResponse.data.token || 'authenticated';
        this.userId = loginResponse.data.user?.id;
        
        // Set default authorization header
        this.api.defaults.headers.common['Authorization'] = `Bearer ${this.authToken}`;
        
        logger.info('Successfully authenticated with Kick API');
        return true;
      }

      return false;
    } catch (error: any) {
      logger.error('Authentication failed:', error.response?.data || error.message);
      return false;
    }
  }

  /**
   * Get channel information by slug
   */
  async getChannel(channelSlug: string): Promise<KickChannel | null> {
    try {
      const response = await this.api.get(`/channels/${channelSlug}`);
      return response.data;
    } catch (error: any) {
      logger.error(`Failed to get channel ${channelSlug}:`, error.response?.data || error.message);
      return null;
    }
  }

  /**
   * Get user information by username
   */
  async getUser(username: string): Promise<KickUser | null> {
    try {
      const response = await this.api.get(`/users/${username}`);
      return response.data;
    } catch (error: any) {
      logger.error(`Failed to get user ${username}:`, error.response?.data || error.message);
      return null;
    }
  }

  /**
   * Send a message to a chatroom
   */
  async sendMessage(chatroomId: number, message: string): Promise<boolean> {
    try {
      const response = await this.api.post(`/chatrooms/${chatroomId}/messages`, {
        content: message,
        type: 'message',
      });

      return response.status === 200;
    } catch (error: any) {
      logger.error('Failed to send message:', error.response?.data || error.message);
      return false;
    }
  }

  /**
   * Ban a user from a chatroom
   */
  async banUser(chatroomId: number, username: string, permanent: boolean = false): Promise<boolean> {
    try {
      const response = await this.api.post(`/chatrooms/${chatroomId}/bans`, {
        banned_username: username,
        permanent,
      });

      return response.status === 200;
    } catch (error: any) {
      logger.error(`Failed to ban user ${username}:`, error.response?.data || error.message);
      return false;
    }
  }

  /**
   * Timeout a user in a chatroom
   */
  async timeoutUser(chatroomId: number, username: string, duration: number): Promise<boolean> {
    try {
      const response = await this.api.post(`/chatrooms/${chatroomId}/timeouts`, {
        banned_username: username,
        duration, // in minutes
      });

      return response.status === 200;
    } catch (error: any) {
      logger.error(`Failed to timeout user ${username}:`, error.response?.data || error.message);
      return false;
    }
  }

  /**
   * Unban a user from a chatroom
   */
  async unbanUser(chatroomId: number, username: string): Promise<boolean> {
    try {
      const response = await this.api.delete(`/chatrooms/${chatroomId}/bans/${username}`);
      return response.status === 200;
    } catch (error: any) {
      logger.error(`Failed to unban user ${username}:`, error.response?.data || error.message);
      return false;
    }
  }

  /**
   * Get chatroom settings
   */
  async getChatroomSettings(chatroomId: number): Promise<ChatroomSettings | null> {
    try {
      const response = await this.api.get(`/chatrooms/${chatroomId}/settings`);
      return response.data;
    } catch (error: any) {
      logger.error(`Failed to get chatroom settings:`, error.response?.data || error.message);
      return null;
    }
  }

  /**
   * Update chatroom settings
   */
  async updateChatroomSettings(chatroomId: number, settings: Partial<ChatroomSettings>): Promise<boolean> {
    try {
      const response = await this.api.put(`/chatrooms/${chatroomId}/settings`, settings);
      return response.status === 200;
    } catch (error: any) {
      logger.error('Failed to update chatroom settings:', error.response?.data || error.message);
      return false;
    }
  }

  /**
   * Get current authenticated user info
   */
  get currentUserId(): number | undefined {
    return this.userId;
  }

  /**
   * Check if authenticated
   */
  get isAuthenticated(): boolean {
    return !!this.authToken;
  }
}

export const kickAPI = new KickAPI();
