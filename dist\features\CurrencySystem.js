"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CurrencySystem = void 0;
const events_1 = require("events");
const Database_1 = require("../database/Database");
const logger_1 = require("../utils/logger");
class CurrencySystem extends events_1.EventEmitter {
    constructor(bot, config) {
        super();
        this.messageRewardCooldowns = new Map();
        this.dailyBonusClaimed = new Map();
        this.bot = bot;
        this.config = config;
        this.setupEventHandlers();
        if (config.enabled) {
            this.startPassiveEarning();
        }
    }
    setupEventHandlers() {
        this.bot.on('message', (message) => {
            if (this.config.messageRewards.enabled) {
                this.handleMessageReward(message.sender.username);
            }
        });
        this.bot.on('new_follower', (data) => {
            if (this.config.followRewards.enabled) {
                this.addPoints(data.username, this.config.followRewards.amount, 'New follower bonus');
            }
        });
    }
    startPassiveEarning() {
        if (!this.config.passiveEarning.enabled)
            return;
        const intervalMs = this.config.passiveEarning.interval * 60 * 1000;
        this.passiveEarningInterval = setInterval(async () => {
            await this.distributePassiveEarnings();
        }, intervalMs);
        logger_1.logger.info(`Started passive earning: ${this.config.passiveEarning.amount} ${this.config.currencyName} every ${this.config.passiveEarning.interval} minutes`);
    }
    async distributePassiveEarnings() {
        try {
            const activeUsers = [];
            for (const username of activeUsers) {
                await this.addPoints(username, this.config.passiveEarning.amount, 'Passive earning');
            }
            if (activeUsers.length > 0) {
                logger_1.logger.debug(`Distributed ${this.config.passiveEarning.amount} ${this.config.currencyName} to ${activeUsers.length} active users`);
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to distribute passive earnings:', error);
        }
    }
    async handleMessageReward(username) {
        const now = Date.now();
        const lastReward = this.messageRewardCooldowns.get(username) || 0;
        const cooldownMs = this.config.messageRewards.cooldown * 1000;
        if (now - lastReward >= cooldownMs) {
            await this.addPoints(username, this.config.messageRewards.amount, 'Message reward');
            this.messageRewardCooldowns.set(username, now);
        }
    }
    async addPoints(username, amount, reason) {
        try {
            let user = await Database_1.database.getUser(username);
            if (!user) {
                await Database_1.database.createUser({ username, display_name: username });
                user = await Database_1.database.getUser(username);
            }
            if (!user) {
                logger_1.logger.error(`Failed to get/create user: ${username}`);
                return false;
            }
            const newPoints = user.points + amount;
            await Database_1.database.updateUser(username, { points: newPoints });
            const transaction = {
                id: this.generateTransactionId(),
                username,
                amount,
                type: amount > 0 ? 'earn' : 'spend',
                reason,
                timestamp: new Date(),
            };
            this.emit('points_added', { username, amount, newTotal: newPoints, reason });
            this.emit('transaction', transaction);
            logger_1.logger.debug(`Added ${amount} ${this.config.currencyName} to ${username} (${reason}). New total: ${newPoints}`);
            return true;
        }
        catch (error) {
            logger_1.logger.error(`Failed to add points to ${username}:`, error);
            return false;
        }
    }
    async removePoints(username, amount, reason) {
        try {
            const user = await Database_1.database.getUser(username);
            if (!user) {
                logger_1.logger.warn(`User not found: ${username}`);
                return false;
            }
            if (user.points < amount) {
                logger_1.logger.warn(`Insufficient points for ${username}: has ${user.points}, needs ${amount}`);
                return false;
            }
            const newPoints = user.points - amount;
            await Database_1.database.updateUser(username, { points: newPoints });
            const transaction = {
                id: this.generateTransactionId(),
                username,
                amount: -amount,
                type: 'spend',
                reason,
                timestamp: new Date(),
            };
            this.emit('points_removed', { username, amount, newTotal: newPoints, reason });
            this.emit('transaction', transaction);
            logger_1.logger.debug(`Removed ${amount} ${this.config.currencyName} from ${username} (${reason}). New total: ${newPoints}`);
            return true;
        }
        catch (error) {
            logger_1.logger.error(`Failed to remove points from ${username}:`, error);
            return false;
        }
    }
    async transferPoints(fromUsername, toUsername, amount) {
        try {
            const fromUser = await Database_1.database.getUser(fromUsername);
            if (!fromUser || fromUser.points < amount) {
                return false;
            }
            const removeSuccess = await this.removePoints(fromUsername, amount, `Transfer to ${toUsername}`);
            if (!removeSuccess)
                return false;
            const addSuccess = await this.addPoints(toUsername, amount, `Transfer from ${fromUsername}`);
            if (!addSuccess) {
                await this.addPoints(fromUsername, amount, 'Transfer rollback');
                return false;
            }
            this.emit('points_transferred', { from: fromUsername, to: toUsername, amount });
            return true;
        }
        catch (error) {
            logger_1.logger.error(`Failed to transfer points from ${fromUsername} to ${toUsername}:`, error);
            return false;
        }
    }
    async getUserPoints(username) {
        try {
            const user = await Database_1.database.getUser(username);
            return user ? user.points : 0;
        }
        catch (error) {
            logger_1.logger.error(`Failed to get points for ${username}:`, error);
            return 0;
        }
    }
    async getLeaderboard(limit = 10) {
        try {
            const users = await Database_1.database.db.all(`
        SELECT username, points 
        FROM users 
        WHERE points > 0 
        ORDER BY points DESC 
        LIMIT ?
      `, [limit]);
            return users.map((user, index) => ({
                username: user.username,
                points: user.points,
                rank: index + 1,
            }));
        }
        catch (error) {
            logger_1.logger.error('Failed to get leaderboard:', error);
            return [];
        }
    }
    async claimDailyBonus(username) {
        if (!this.config.dailyBonus.enabled) {
            return { success: false, message: 'Daily bonus is disabled' };
        }
        const today = new Date().toDateString();
        const lastClaimed = this.dailyBonusClaimed.get(username);
        if (lastClaimed === today) {
            return { success: false, message: 'Daily bonus already claimed today' };
        }
        const streak = 1;
        const bonusAmount = this.config.dailyBonus.amount * (1 + (streak - 1) * this.config.dailyBonus.streakMultiplier);
        const success = await this.addPoints(username, bonusAmount, `Daily bonus (streak: ${streak})`);
        if (success) {
            this.dailyBonusClaimed.set(username, today);
            return {
                success: true,
                amount: bonusAmount,
                streak,
                message: `Daily bonus claimed! +${bonusAmount} ${this.config.currencyName} (${streak} day streak)`
            };
        }
        return { success: false, message: 'Failed to claim daily bonus' };
    }
    generateTransactionId() {
        return `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        if (this.config.enabled && this.config.passiveEarning.enabled && !this.passiveEarningInterval) {
            this.startPassiveEarning();
        }
        else if (!this.config.enabled && this.passiveEarningInterval) {
            clearInterval(this.passiveEarningInterval);
            this.passiveEarningInterval = undefined;
        }
        this.emit('config_updated', this.config);
    }
    async getStats() {
        try {
            const totalUsers = await Database_1.database.db.get('SELECT COUNT(*) as count FROM users WHERE points > 0');
            const totalPoints = await Database_1.database.db.get('SELECT SUM(points) as total FROM users');
            const avgPoints = await Database_1.database.db.get('SELECT AVG(points) as avg FROM users WHERE points > 0');
            const topUser = await Database_1.database.db.get('SELECT username, points FROM users ORDER BY points DESC LIMIT 1');
            return {
                totalUsers: totalUsers.count,
                totalPointsInCirculation: totalPoints.total || 0,
                averagePoints: Math.round(avgPoints.avg || 0),
                topUser: topUser ? { username: topUser.username, points: topUser.points } : null,
                config: this.config,
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get currency stats:', error);
            return {
                totalUsers: 0,
                totalPointsInCirculation: 0,
                averagePoints: 0,
                topUser: null,
                config: this.config,
            };
        }
    }
    cleanup() {
        if (this.passiveEarningInterval) {
            clearInterval(this.passiveEarningInterval);
            this.passiveEarningInterval = undefined;
        }
    }
}
exports.CurrencySystem = CurrencySystem;
//# sourceMappingURL=CurrencySystem.js.map