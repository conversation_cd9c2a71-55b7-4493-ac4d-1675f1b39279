import { EventEmitter } from 'events';
import { KickChannel } from '../api/kickApi';
import { ChatMessage, BotConfig, PermissionLevel } from '../types';
export declare class KickBot extends EventEmitter {
    private botConfig;
    private webSocket;
    private channel?;
    private isRunning;
    private startTime;
    private messageCount;
    private commandCount;
    constructor(botConfig: BotConfig);
    private setupEventHandlers;
    start(): Promise<void>;
    stop(): Promise<void>;
    private handleMessage;
    private handleCommand;
    sendMessage(content: string): Promise<boolean>;
    replyToMessage(originalMessage: ChatMessage, content: string): Promise<boolean>;
    banUser(username: string, permanent?: boolean): Promise<boolean>;
    timeoutUser(username: string, duration: number): Promise<boolean>;
    unbanUser(username: string): Promise<boolean>;
    getUserPermissionLevel(user: any): PermissionLevel;
    hasPermission(user: any, requiredLevel: PermissionLevel): boolean;
    getStats(): {
        isRunning: boolean;
        uptime: number;
        uptimeFormatted: string;
        startTime: Date;
        messageCount: number;
        commandCount: number;
        channel: string | undefined;
        connected: boolean;
    };
    private formatUptime;
    get currentChannel(): KickChannel | undefined;
    get running(): boolean;
    get connected(): boolean;
}
//# sourceMappingURL=KickBot.d.ts.map