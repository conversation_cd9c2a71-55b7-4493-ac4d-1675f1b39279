import { EventEmitter } from 'events';
import { KickBot } from '../bot/KickBot';
export interface CurrencyConfig {
    enabled: boolean;
    currencyName: string;
    currencySymbol: string;
    passiveEarning: {
        enabled: boolean;
        amount: number;
        interval: number;
        requireActive: boolean;
    };
    messageRewards: {
        enabled: boolean;
        amount: number;
        cooldown: number;
    };
    followRewards: {
        enabled: boolean;
        amount: number;
    };
    subscribeRewards: {
        enabled: boolean;
        amount: number;
    };
    dailyBonus: {
        enabled: boolean;
        amount: number;
        streakMultiplier: number;
    };
}
export interface Transaction {
    id: string;
    username: string;
    amount: number;
    type: 'earn' | 'spend' | 'transfer' | 'bonus' | 'penalty';
    reason: string;
    timestamp: Date;
}
export declare class CurrencySystem extends EventEmitter {
    private config;
    private bot;
    private passiveEarningInterval?;
    private messageRewardCooldowns;
    private dailyBonusClaimed;
    constructor(bot: KickBot, config: CurrencyConfig);
    private setupEventHandlers;
    private startPassiveEarning;
    private distributePassiveEarnings;
    private handleMessageReward;
    addPoints(username: string, amount: number, reason: string): Promise<boolean>;
    removePoints(username: string, amount: number, reason: string): Promise<boolean>;
    transferPoints(fromUsername: string, toUsername: string, amount: number): Promise<boolean>;
    getUserPoints(username: string): Promise<number>;
    getLeaderboard(limit?: number): Promise<Array<{
        username: string;
        points: number;
        rank: number;
    }>>;
    claimDailyBonus(username: string): Promise<{
        success: boolean;
        amount?: number;
        streak?: number;
        message: string;
    }>;
    private generateTransactionId;
    updateConfig(newConfig: Partial<CurrencyConfig>): void;
    getStats(): Promise<any>;
    cleanup(): void;
}
//# sourceMappingURL=CurrencySystem.d.ts.map