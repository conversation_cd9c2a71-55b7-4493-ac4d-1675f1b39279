{"version": 3, "file": "SimpleKickWebSocket.js", "sourceRoot": "", "sources": ["../../src/websocket/SimpleKickWebSocket.ts"], "names": [], "mappings": ";;;;;;AAAA,4CAA2B;AAC3B,mCAAsC;AACtC,4CAAyC;AAuBzC,MAAa,mBAAoB,SAAQ,qBAAY;IAUnD;QACE,KAAK,EAAE,CAAC;QARF,sBAAiB,GAAG,CAAC,CAAC;QACtB,yBAAoB,GAAG,CAAC,CAAC;QACzB,mBAAc,GAAG,IAAI,CAAC;QACtB,iBAAY,GAAG,KAAK,CAAC;QACrB,gBAAW,GAAG,KAAK,CAAC;IAK5B,CAAC;IAKD,KAAK,CAAC,OAAO,CAAC,UAAkB;QAC9B,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAE7B,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,6CAA6C,UAAU,KAAK,CAAC,CAAC;YAG1E,MAAM,KAAK,GAAG,iGAAiG,CAAC;YAEhH,IAAI,CAAC,EAAE,GAAG,IAAI,YAAS,CAAC,KAAK,CAAC,CAAC;YAE/B,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;gBACtB,eAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;gBAC3C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;gBAC1B,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;gBAG3B,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAG3B,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAEzB,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,IAAoB,EAAE,EAAE;gBAC7C,IAAI,CAAC;oBACH,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;oBAC5C,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;gBAC9B,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;gBAC5D,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAY,EAAE,MAAc,EAAE,EAAE;gBACnD,eAAM,CAAC,IAAI,CAAC,qBAAqB,IAAI,MAAM,MAAM,EAAE,CAAC,CAAC;gBACrD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;gBACzB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;gBAC1B,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAExB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;gBAG5C,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBACvD,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC3B,CAAC;qBAAM,CAAC;oBACN,eAAM,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;oBAClD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC,CAAC;gBACrE,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;gBACnC,eAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;gBACxC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;gBACzB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;gBAC1B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACvD,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKO,mBAAmB;QACzB,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAO;QAEzC,MAAM,gBAAgB,GAAG;YACvB,KAAK,EAAE,kBAAkB;YACzB,IAAI,EAAE;gBACJ,IAAI,EAAE,EAAE;gBACR,OAAO,EAAE,aAAa,IAAI,CAAC,UAAU,KAAK;aAC3C;SACF,CAAC;QAEF,eAAM,CAAC,KAAK,CAAC,2BAA2B,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;QAC3D,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACjD,CAAC;IAKO,aAAa,CAAC,OAAY;QAChC,IAAI,CAAC;YACH,QAAQ,OAAO,CAAC,KAAK,EAAE,CAAC;gBACtB,KAAK,+BAA+B;oBAClC,eAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;oBAC7C,MAAM;gBAER,KAAK,+BAA+B;oBAClC,eAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;oBACnD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBACxB,MAAM;gBAER,KAAK,+BAA+B;oBAClC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;oBAChC,MAAM;gBAER,KAAK,8BAA8B;oBACjC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;oBACvC,MAAM;gBAER,KAAK,gCAAgC;oBACnC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;oBACzC,MAAM;gBAER,KAAK,+BAA+B;oBAClC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;oBACxC,MAAM;gBAER,KAAK,aAAa;oBAEhB,MAAM;gBAER;oBACE,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAKO,iBAAiB,CAAC,OAAY;QACpC,IAAI,CAAC;YACH,MAAM,WAAW,GAAsB;gBACrC,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;gBACnB,IAAI,EAAE,OAAO,CAAC,KAAK;gBACnB,IAAI,EAAE,OAAO,CAAC,IAAI;aACnB,CAAC;YAEF,eAAM,CAAC,KAAK,CAAC,qBAAqB,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;YACnG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QACpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAKO,iBAAiB;QACvB,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,GAAG,EAAE;YACnC,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBAChC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,aAAa,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;YACnE,CAAC;QACH,CAAC,EAAE,KAAK,CAAC,CAAC;IACZ,CAAC;IAKO,gBAAgB;QACtB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACjC,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;QAChC,CAAC;IACH,CAAC;IAKO,iBAAiB;QACvB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAE3D,eAAM,CAAC,IAAI,CAAC,mCAAmC,IAAI,CAAC,iBAAiB,OAAO,KAAK,IAAI,CAAC,CAAC;QAEvF,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBACpB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAChC,CAAC;QACH,CAAC,EAAE,KAAK,CAAC,CAAC;IACZ,CAAC;IAKD,KAAK,CAAC,WAAW,CAAC,OAAe;QAG/B,eAAM,CAAC,IAAI,CAAC,uBAAuB,OAAO,EAAE,CAAC,CAAC;QAC9C,OAAO,IAAI,CAAC;IACd,CAAC;IAKD,UAAU;QACR,eAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAE/C,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC;YACZ,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;YAChB,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC;QACtB,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;IAC7B,CAAC;IAKD,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAKD,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;CACF;AAtPD,kDAsPC"}