"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KickWebSocket = void 0;
const ws_1 = __importDefault(require("ws"));
const events_1 = require("events");
const config_1 = require("../config");
const logger_1 = require("../utils/logger");
class KickWebSocket extends events_1.EventEmitter {
    constructor() {
        super();
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 5000;
    }
    async connect(channelId, chatroomId) {
        this.channelId = channelId;
        this.chatroomId = chatroomId;
        return new Promise((resolve, reject) => {
            try {
                const wsUrl = `${config_1.kickConfig.websocketUrl}?protocol=7&client=js&version=8.4.0-rc2&flash=false`;
                logger_1.logger.info(`Connecting to Kick WebSocket: ${wsUrl}`);
                this.ws = new ws_1.default(wsUrl);
                this.connectionTimeout = setTimeout(() => {
                    if (!this.isConnected) {
                        logger_1.logger.error('WebSocket connection timeout');
                        this.ws?.terminate();
                        reject(new Error('Connection timeout'));
                    }
                }, 10000);
                this.ws.on('open', () => {
                    logger_1.logger.info('WebSocket connection opened');
                    this.isConnected = true;
                    this.reconnectAttempts = 0;
                    if (this.connectionTimeout) {
                        clearTimeout(this.connectionTimeout);
                    }
                    this.sendConnectionMessage();
                    this.subscribeToChannel();
                    this.startHeartbeat();
                    resolve();
                });
                this.ws.on('message', (data) => {
                    try {
                        const message = JSON.parse(data.toString());
                        this.handleMessage(message);
                    }
                    catch (error) {
                        logger_1.logger.error('Failed to parse WebSocket message:', error);
                    }
                });
                this.ws.on('close', (code, reason) => {
                    logger_1.logger.warn(`WebSocket connection closed: ${code} ${reason.toString()}`);
                    this.isConnected = false;
                    this.cleanup();
                    if (this.reconnectAttempts < this.maxReconnectAttempts) {
                        this.scheduleReconnect();
                    }
                    else {
                        logger_1.logger.error('Max reconnection attempts reached');
                        this.emit('error', new Error('Max reconnection attempts reached'));
                    }
                });
                this.ws.on('error', (error) => {
                    logger_1.logger.error('WebSocket error:', error);
                    this.isConnected = false;
                    this.emit('error', error);
                    if (this.connectionTimeout) {
                        clearTimeout(this.connectionTimeout);
                    }
                    reject(error);
                });
            }
            catch (error) {
                logger_1.logger.error('Failed to create WebSocket connection:', error);
                reject(error);
            }
        });
    }
    sendConnectionMessage() {
        const connectionMessage = {
            event: 'pusher:connection',
            data: {
                protocol: 7,
                client: 'js',
                version: '8.4.0-rc2',
            },
        };
        this.send(connectionMessage);
    }
    subscribeToChannel() {
        if (!this.chatroomId) {
            logger_1.logger.error('Cannot subscribe: chatroomId not set');
            return;
        }
        const subscribeMessage = {
            event: 'pusher:subscribe',
            data: {
                auth: '',
                channel: `chatrooms.${this.chatroomId}.v2`,
            },
        };
        this.send(subscribeMessage);
        logger_1.logger.info(`Subscribed to chatroom ${this.chatroomId}`);
    }
    handleMessage(message) {
        logger_1.logger.debug('Received WebSocket message:', message);
        switch (message.event) {
            case 'pusher:connection_established':
                logger_1.logger.info('Pusher connection established');
                this.emit('connected');
                break;
            case 'pusher:subscription_succeeded':
                logger_1.logger.info('Successfully subscribed to channel');
                this.emit('subscribed');
                break;
            case 'pusher:pong':
                logger_1.logger.debug('Received pong from server');
                break;
            case 'App\\Events\\ChatMessageEvent':
                this.handleChatMessage(message);
                break;
            case 'App\\Events\\UserBannedEvent':
                this.handleUserBanned(message);
                break;
            case 'App\\Events\\UserUnbannedEvent':
                this.handleUserUnbanned(message);
                break;
            case 'App\\Events\\FollowersUpdated':
                this.handleFollowersUpdated(message);
                break;
            case 'App\\Events\\StreamerIsLive':
                this.handleStreamerLive(message);
                break;
            case 'App\\Events\\StreamerIsOffline':
                this.handleStreamerOffline(message);
                break;
            default:
                logger_1.logger.debug(`Unhandled event: ${message.event}`);
                this.emit('unknown_event', message);
                break;
        }
    }
    handleChatMessage(message) {
        try {
            const data = JSON.parse(message.data);
            const chatMessage = {
                id: data.id,
                chatroom_id: data.chatroom_id,
                content: data.content,
                type: data.type,
                created_at: data.created_at,
                sender: data.sender,
                metadata: data.metadata,
            };
            logger_1.logger.chatMessage(chatMessage.sender.username, chatMessage.content, `chatroom-${chatMessage.chatroom_id}`);
            this.emit('message', chatMessage);
        }
        catch (error) {
            logger_1.logger.error('Failed to parse chat message:', error);
        }
    }
    handleUserBanned(message) {
        try {
            const data = JSON.parse(message.data);
            logger_1.logger.moderationAction('banned', data.user.username, data.banned_by?.username || 'system');
            this.emit('user_banned', data);
        }
        catch (error) {
            logger_1.logger.error('Failed to parse user banned event:', error);
        }
    }
    handleUserUnbanned(message) {
        try {
            const data = JSON.parse(message.data);
            logger_1.logger.moderationAction('unbanned', data.user.username, data.unbanned_by?.username || 'system');
            this.emit('user_unbanned', data);
        }
        catch (error) {
            logger_1.logger.error('Failed to parse user unbanned event:', error);
        }
    }
    handleFollowersUpdated(message) {
        try {
            const data = JSON.parse(message.data);
            logger_1.logger.info(`New follower: ${data.username}`);
            this.emit('new_follower', data);
        }
        catch (error) {
            logger_1.logger.error('Failed to parse followers updated event:', error);
        }
    }
    handleStreamerLive(message) {
        try {
            const data = JSON.parse(message.data);
            logger_1.logger.info('Streamer went live');
            this.emit('streamer_live', data);
        }
        catch (error) {
            logger_1.logger.error('Failed to parse streamer live event:', error);
        }
    }
    handleStreamerOffline(message) {
        try {
            const data = JSON.parse(message.data);
            logger_1.logger.info('Streamer went offline');
            this.emit('streamer_offline', data);
        }
        catch (error) {
            logger_1.logger.error('Failed to parse streamer offline event:', error);
        }
    }
    send(message) {
        if (this.ws && this.ws.readyState === ws_1.default.OPEN) {
            this.ws.send(JSON.stringify(message));
        }
        else {
            logger_1.logger.warn('Cannot send message: WebSocket not connected');
        }
    }
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected) {
                this.send({ event: 'pusher:ping', data: {} });
            }
        }, 30000);
    }
    scheduleReconnect() {
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * this.reconnectAttempts;
        logger_1.logger.info(`Scheduling reconnection attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);
        setTimeout(() => {
            if (this.channelId && this.chatroomId) {
                this.connect(this.channelId, this.chatroomId).catch((error) => {
                    logger_1.logger.error('Reconnection failed:', error);
                });
            }
        }, delay);
    }
    cleanup() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = undefined;
        }
        if (this.connectionTimeout) {
            clearTimeout(this.connectionTimeout);
            this.connectionTimeout = undefined;
        }
    }
    disconnect() {
        logger_1.logger.info('Disconnecting from WebSocket');
        this.isConnected = false;
        this.cleanup();
        if (this.ws) {
            this.ws.close();
            this.ws = undefined;
        }
    }
    get connected() {
        return this.isConnected;
    }
}
exports.KickWebSocket = KickWebSocket;
//# sourceMappingURL=kickWebSocket.js.map