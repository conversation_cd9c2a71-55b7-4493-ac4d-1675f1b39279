export interface DatabaseUser {
    id: number;
    username: string;
    display_name: string;
    points: number;
    messages_sent: number;
    commands_used: number;
    time_in_chat: number;
    last_seen: string;
    first_seen: string;
    is_follower: boolean;
    is_subscriber: boolean;
    is_vip: boolean;
    is_moderator: boolean;
    violations: number;
    timeouts: number;
    bans: number;
    created_at: string;
    updated_at: string;
}
export interface DatabaseCommand {
    id: number;
    name: string;
    response: string;
    description: string;
    aliases: string;
    cooldown: number;
    permission: number;
    enabled: boolean;
    use_count: number;
    created_by: string;
    created_at: string;
    updated_at: string;
}
export interface ChatLog {
    id: number;
    username: string;
    message: string;
    timestamp: string;
    message_type: string;
    channel: string;
}
export interface ModerationLog {
    id: number;
    username: string;
    action: string;
    reason: string;
    duration: number | null;
    moderator: string;
    timestamp: string;
}
export declare class Database {
    private db;
    private dbPath;
    constructor();
    private ensureDirectoryExists;
    initialize(): Promise<void>;
    private createTables;
    getUser(username: string): Promise<DatabaseUser | null>;
    createUser(userData: Partial<DatabaseUser>): Promise<boolean>;
    updateUser(username: string, updates: Partial<DatabaseUser>): Promise<boolean>;
    getCommand(name: string): Promise<DatabaseCommand | null>;
    getAllCommands(): Promise<DatabaseCommand[]>;
    createCommand(commandData: Partial<DatabaseCommand>): Promise<boolean>;
    updateCommand(name: string, updates: Partial<DatabaseCommand>): Promise<boolean>;
    deleteCommand(name: string): Promise<boolean>;
    logMessage(username: string, message: string, channel: string, messageType?: string): Promise<void>;
    logModerationAction(username: string, action: string, reason: string, duration: number | null, moderator: string): Promise<void>;
    getSetting(key: string): Promise<string | null>;
    setSetting(key: string, value: string): Promise<boolean>;
    getStats(): Promise<any>;
    close(): Promise<void>;
}
export declare const database: Database;
//# sourceMappingURL=Database.d.ts.map