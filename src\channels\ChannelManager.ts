import { EventEmitter } from 'events';
import { kickAPI, KickChannel, ChatroomSettings } from '../api/kickApi';
import { logger } from '../utils/logger';

export interface ChannelInfo {
  id: number;
  slug: string;
  name: string;
  isLive: boolean;
  viewerCount: number;
  followerCount: number;
  subscriberCount: number;
  chatroomId: number;
  settings: ChatroomSettings | null;
  lastUpdated: Date;
}

export class ChannelManager extends EventEmitter {
  private currentChannel?: ChannelInfo;
  private channelCache = new Map<string, ChannelInfo>();
  private updateInterval?: NodeJS.Timeout;

  constructor() {
    super();
  }

  /**
   * Join a channel
   */
  async joinChannel(channelSlug: string): Promise<boolean> {
    try {
      logger.info(`Joining channel: ${channelSlug}`);

      const channel = await kickAPI.getChannel(channelSlug);
      if (!channel) {
        logger.error(`Channel not found: ${channelSlug}`);
        return false;
      }

      // Get chatroom settings
      const settings = await kickAPI.getChatroomSettings(channel.chatroom.id);

      const channelInfo: ChannelInfo = {
        id: channel.id,
        slug: channel.slug,
        name: channel.user.username,
        isLive: false, // This would need to be determined from stream status
        viewerCount: 0, // This would need to be fetched from stream data
        followerCount: 0, // This would need to be fetched from user data
        subscriberCount: 0, // This would need to be fetched from user data
        chatroomId: channel.chatroom.id,
        settings,
        lastUpdated: new Date(),
      };

      this.currentChannel = channelInfo;
      this.channelCache.set(channelSlug, channelInfo);

      // Start periodic updates
      this.startChannelUpdates();

      logger.info(`Successfully joined channel: ${channelSlug} (ID: ${channel.id})`);
      this.emit('channel_joined', channelInfo);

      return true;
    } catch (error) {
      logger.error(`Failed to join channel ${channelSlug}:`, error);
      return false;
    }
  }

  /**
   * Leave the current channel
   */
  leaveChannel(): void {
    if (this.currentChannel) {
      logger.info(`Leaving channel: ${this.currentChannel.slug}`);
      
      this.stopChannelUpdates();
      
      const channelInfo = this.currentChannel;
      this.currentChannel = undefined;
      
      this.emit('channel_left', channelInfo);
    }
  }

  /**
   * Get current channel information
   */
  getCurrentChannel(): ChannelInfo | undefined {
    return this.currentChannel;
  }

  /**
   * Update channel information
   */
  async updateChannelInfo(): Promise<void> {
    if (!this.currentChannel) {
      return;
    }

    try {
      const channel = await kickAPI.getChannel(this.currentChannel.slug);
      if (!channel) {
        logger.warn(`Failed to update channel info: channel not found`);
        return;
      }

      // Update channel info
      this.currentChannel.lastUpdated = new Date();
      
      // Update cache
      this.channelCache.set(this.currentChannel.slug, this.currentChannel);

      this.emit('channel_updated', this.currentChannel);
    } catch (error) {
      logger.error('Failed to update channel info:', error);
    }
  }

  /**
   * Start periodic channel updates
   */
  private startChannelUpdates(): void {
    this.stopChannelUpdates();
    
    // Update channel info every 5 minutes
    this.updateInterval = setInterval(() => {
      this.updateChannelInfo();
    }, 5 * 60 * 1000);
  }

  /**
   * Stop periodic channel updates
   */
  private stopChannelUpdates(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = undefined;
    }
  }

  /**
   * Update chatroom settings
   */
  async updateChatroomSettings(settings: Partial<ChatroomSettings>): Promise<boolean> {
    if (!this.currentChannel) {
      logger.error('Cannot update chatroom settings: not in a channel');
      return false;
    }

    try {
      const success = await kickAPI.updateChatroomSettings(this.currentChannel.chatroomId, settings);
      
      if (success && this.currentChannel.settings) {
        // Update local settings
        Object.assign(this.currentChannel.settings, settings);
        this.emit('chatroom_settings_updated', this.currentChannel.settings);
        logger.info('Chatroom settings updated successfully');
      }

      return success;
    } catch (error) {
      logger.error('Failed to update chatroom settings:', error);
      return false;
    }
  }

  /**
   * Enable slow mode
   */
  async enableSlowMode(interval: number = 30): Promise<boolean> {
    return this.updateChatroomSettings({
      slow_mode: true,
      message_interval: interval,
    });
  }

  /**
   * Disable slow mode
   */
  async disableSlowMode(): Promise<boolean> {
    return this.updateChatroomSettings({
      slow_mode: false,
      message_interval: 0,
    });
  }

  /**
   * Enable followers only mode
   */
  async enableFollowersOnly(minDuration: number = 0): Promise<boolean> {
    return this.updateChatroomSettings({
      followers_mode: true,
      following_min_duration: minDuration,
    });
  }

  /**
   * Disable followers only mode
   */
  async disableFollowersOnly(): Promise<boolean> {
    return this.updateChatroomSettings({
      followers_mode: false,
      following_min_duration: 0,
    });
  }

  /**
   * Enable subscribers only mode
   */
  async enableSubscribersOnly(): Promise<boolean> {
    return this.updateChatroomSettings({
      subscribers_mode: true,
    });
  }

  /**
   * Disable subscribers only mode
   */
  async disableSubscribersOnly(): Promise<boolean> {
    return this.updateChatroomSettings({
      subscribers_mode: false,
    });
  }

  /**
   * Enable emotes mode
   */
  async enableEmotesMode(): Promise<boolean> {
    return this.updateChatroomSettings({
      emotes_mode: true,
    });
  }

  /**
   * Disable emotes mode
   */
  async disableEmotesMode(): Promise<boolean> {
    return this.updateChatroomSettings({
      emotes_mode: false,
    });
  }

  /**
   * Get channel from cache
   */
  getCachedChannel(channelSlug: string): ChannelInfo | undefined {
    return this.channelCache.get(channelSlug);
  }

  /**
   * Clear channel cache
   */
  clearCache(): void {
    this.channelCache.clear();
    logger.info('Channel cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      cachedChannels: this.channelCache.size,
      currentChannel: this.currentChannel?.slug || null,
      isUpdating: !!this.updateInterval,
    };
  }

  /**
   * Check if bot is in a channel
   */
  get inChannel(): boolean {
    return !!this.currentChannel;
  }

  /**
   * Get current chatroom ID
   */
  get currentChatroomId(): number | undefined {
    return this.currentChannel?.chatroomId;
  }

  /**
   * Get current channel slug
   */
  get currentChannelSlug(): string | undefined {
    return this.currentChannel?.slug;
  }

  /**
   * Get current chatroom settings
   */
  get currentChatroomSettings(): ChatroomSettings | null | undefined {
    return this.currentChannel?.settings;
  }

  /**
   * Check if slow mode is enabled
   */
  get isSlowModeEnabled(): boolean {
    return this.currentChannel?.settings?.slow_mode || false;
  }

  /**
   * Check if followers only mode is enabled
   */
  get isFollowersOnlyEnabled(): boolean {
    return this.currentChannel?.settings?.followers_mode || false;
  }

  /**
   * Check if subscribers only mode is enabled
   */
  get isSubscribersOnlyEnabled(): boolean {
    return this.currentChannel?.settings?.subscribers_mode || false;
  }

  /**
   * Check if emotes mode is enabled
   */
  get isEmotesModeEnabled(): boolean {
    return this.currentChannel?.settings?.emotes_mode || false;
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.stopChannelUpdates();
    this.clearCache();
    this.currentChannel = undefined;
  }
}
