"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerModerationCommands = registerModerationCommands;
const types_1 = require("../types");
const logger_1 = require("../utils/logger");
function registerModerationCommands(commandHandler, moderationManager) {
    commandHandler.registerCommand({
        name: 'addword',
        description: 'Add a banned word',
        usage: '!addword <word>',
        aliases: ['banword'],
        cooldown: 0,
        permission: types_1.PermissionLevel.MODERATOR,
        enabled: true,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    }, async (context) => await addBannedWordCommand(context, moderationManager));
    commandHandler.registerCommand({
        name: 'removeword',
        description: 'Remove a banned word',
        usage: '!removeword <word>',
        aliases: ['unbanword'],
        cooldown: 0,
        permission: types_1.PermissionLevel.MODERATOR,
        enabled: true,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    }, async (context) => await removeBannedWordCommand(context, moderationManager));
    commandHandler.registerCommand({
        name: 'moderation',
        description: 'Toggle moderation on/off',
        usage: '!moderation <on|off>',
        aliases: ['mod'],
        cooldown: 0,
        permission: types_1.PermissionLevel.MODERATOR,
        enabled: true,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    }, async (context) => await toggleModerationCommand(context, moderationManager));
    commandHandler.registerCommand({
        name: 'modstats',
        description: 'Show moderation statistics',
        usage: '!modstats',
        aliases: ['moderationstats'],
        cooldown: 10,
        permission: types_1.PermissionLevel.MODERATOR,
        enabled: true,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    }, async (context) => await moderationStatsCommand(context, moderationManager));
    commandHandler.registerCommand({
        name: 'usermod',
        description: 'Show user moderation information',
        usage: '!usermod <username>',
        aliases: ['userinfo'],
        cooldown: 5,
        permission: types_1.PermissionLevel.MODERATOR,
        enabled: true,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    }, async (context) => await userModerationCommand(context, moderationManager));
    commandHandler.registerCommand({
        name: 'clearuser',
        description: 'Clear user moderation data',
        usage: '!clearuser <username>',
        aliases: ['cleanusermod'],
        cooldown: 0,
        permission: types_1.PermissionLevel.MODERATOR,
        enabled: true,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    }, async (context) => await clearUserModerationCommand(context, moderationManager));
    commandHandler.registerCommand({
        name: 'slow',
        description: 'Enable/disable slow mode',
        usage: '!slow <on|off> [interval]',
        aliases: ['slowmode'],
        cooldown: 0,
        permission: types_1.PermissionLevel.MODERATOR,
        enabled: true,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    }, async (context) => await slowModeCommand(context));
    commandHandler.registerCommand({
        name: 'followers',
        description: 'Enable/disable followers only mode',
        usage: '!followers <on|off> [duration]',
        aliases: ['followersonly'],
        cooldown: 0,
        permission: types_1.PermissionLevel.MODERATOR,
        enabled: true,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    }, async (context) => await followersOnlyCommand(context));
    commandHandler.registerCommand({
        name: 'subs',
        description: 'Enable/disable subscribers only mode',
        usage: '!subs <on|off>',
        aliases: ['subsonly', 'subscribers'],
        cooldown: 0,
        permission: types_1.PermissionLevel.MODERATOR,
        enabled: true,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    }, async (context) => await subscribersOnlyCommand(context));
    logger_1.logger.info('Registered moderation commands');
}
async function addBannedWordCommand(context, moderationManager) {
    const { bot, message, args } = context;
    if (args.length === 0) {
        await bot.replyToMessage(message, 'Usage: !addword <word>');
        return;
    }
    const word = args[0];
    moderationManager.addBannedWord(word);
    await bot.replyToMessage(message, `✅ Added "${word}" to banned words list.`);
}
async function removeBannedWordCommand(context, moderationManager) {
    const { bot, message, args } = context;
    if (args.length === 0) {
        await bot.replyToMessage(message, 'Usage: !removeword <word>');
        return;
    }
    const word = args[0];
    const success = moderationManager.removeBannedWord(word);
    if (success) {
        await bot.replyToMessage(message, `✅ Removed "${word}" from banned words list.`);
    }
    else {
        await bot.replyToMessage(message, `❌ "${word}" was not found in banned words list.`);
    }
}
async function toggleModerationCommand(context, moderationManager) {
    const { bot, message, args } = context;
    if (args.length === 0) {
        await bot.replyToMessage(message, 'Usage: !moderation <on|off>');
        return;
    }
    const action = args[0].toLowerCase();
    if (action === 'on' || action === 'enable') {
        moderationManager.updateConfig({ enabled: true });
        await bot.replyToMessage(message, '✅ Auto-moderation enabled.');
    }
    else if (action === 'off' || action === 'disable') {
        moderationManager.updateConfig({ enabled: false });
        await bot.replyToMessage(message, '❌ Auto-moderation disabled.');
    }
    else {
        await bot.replyToMessage(message, 'Usage: !moderation <on|off>');
    }
}
async function moderationStatsCommand(context, moderationManager) {
    const { bot, message } = context;
    const stats = moderationManager.getModerationStats();
    const response = `📊 Moderation Stats: ${stats.totalUsers} users tracked | ${stats.totalViolations} violations | ${stats.totalTimeouts} timeouts | ${stats.totalBans} bans | Status: ${stats.config.enabled ? 'Enabled' : 'Disabled'}`;
    await bot.replyToMessage(message, response);
}
async function userModerationCommand(context, moderationManager) {
    const { bot, message, args } = context;
    if (args.length === 0) {
        await bot.replyToMessage(message, 'Usage: !usermod <username>');
        return;
    }
    const username = args[0];
    const userData = moderationManager.getUserModerationData(username);
    if (!userData) {
        await bot.replyToMessage(message, `No moderation data found for ${username}.`);
        return;
    }
    const response = `👤 ${userData.username}: ${userData.violations.length} violations | ${userData.timeoutCount} timeouts | ${userData.banCount} bans | ${userData.messageCount} messages`;
    await bot.replyToMessage(message, response);
}
async function clearUserModerationCommand(context, moderationManager) {
    const { bot, message, args } = context;
    if (args.length === 0) {
        await bot.replyToMessage(message, 'Usage: !clearuser <username>');
        return;
    }
    const username = args[0];
    const success = moderationManager.clearUserData(username);
    if (success) {
        await bot.replyToMessage(message, `✅ Cleared moderation data for ${username}.`);
    }
    else {
        await bot.replyToMessage(message, `❌ No moderation data found for ${username}.`);
    }
}
async function slowModeCommand(context) {
    const { bot, message, args } = context;
    if (args.length === 0) {
        await bot.replyToMessage(message, 'Usage: !slow <on|off> [interval]');
        return;
    }
    const action = args[0].toLowerCase();
    if (action === 'on' || action === 'enable') {
        const interval = args.length > 1 ? parseInt(args[1]) : 30;
        if (isNaN(interval) || interval < 1 || interval > 300) {
            await bot.replyToMessage(message, 'Invalid interval. Must be between 1-300 seconds.');
            return;
        }
        await bot.replyToMessage(message, `✅ Slow mode enabled (${interval}s interval).`);
    }
    else if (action === 'off' || action === 'disable') {
        await bot.replyToMessage(message, '❌ Slow mode disabled.');
    }
    else {
        await bot.replyToMessage(message, 'Usage: !slow <on|off> [interval]');
    }
}
async function followersOnlyCommand(context) {
    const { bot, message, args } = context;
    if (args.length === 0) {
        await bot.replyToMessage(message, 'Usage: !followers <on|off> [duration]');
        return;
    }
    const action = args[0].toLowerCase();
    if (action === 'on' || action === 'enable') {
        const duration = args.length > 1 ? parseInt(args[1]) : 0;
        await bot.replyToMessage(message, `✅ Followers only mode enabled${duration > 0 ? ` (${duration}m minimum follow time)` : ''}.`);
    }
    else if (action === 'off' || action === 'disable') {
        await bot.replyToMessage(message, '❌ Followers only mode disabled.');
    }
    else {
        await bot.replyToMessage(message, 'Usage: !followers <on|off> [duration]');
    }
}
async function subscribersOnlyCommand(context) {
    const { bot, message, args } = context;
    if (args.length === 0) {
        await bot.replyToMessage(message, 'Usage: !subs <on|off>');
        return;
    }
    const action = args[0].toLowerCase();
    if (action === 'on' || action === 'enable') {
        await bot.replyToMessage(message, '✅ Subscribers only mode enabled.');
    }
    else if (action === 'off' || action === 'disable') {
        await bot.replyToMessage(message, '❌ Subscribers only mode disabled.');
    }
    else {
        await bot.replyToMessage(message, 'Usage: !subs <on|off>');
    }
}
//# sourceMappingURL=moderationCommands.js.map