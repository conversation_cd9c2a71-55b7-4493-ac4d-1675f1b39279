{"version": 3, "file": "ChannelManager.js", "sourceRoot": "", "sources": ["../../src/channels/ChannelManager.ts"], "names": [], "mappings": ";;;AAAA,mCAAsC;AACtC,4CAAwE;AACxE,4CAAyC;AAezC,MAAa,cAAe,SAAQ,qBAAY;IAK9C;QACE,KAAK,EAAE,CAAC;QAJF,iBAAY,GAAG,IAAI,GAAG,EAAuB,CAAC;IAKtD,CAAC;IAKD,KAAK,CAAC,WAAW,CAAC,WAAmB;QACnC,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,oBAAoB,WAAW,EAAE,CAAC,CAAC;YAE/C,MAAM,OAAO,GAAG,MAAM,iBAAO,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;YACtD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,eAAM,CAAC,KAAK,CAAC,sBAAsB,WAAW,EAAE,CAAC,CAAC;gBAClD,OAAO,KAAK,CAAC;YACf,CAAC;YAGD,MAAM,QAAQ,GAAG,MAAM,iBAAO,CAAC,mBAAmB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAExE,MAAM,WAAW,GAAgB;gBAC/B,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,QAAQ;gBAC3B,MAAM,EAAE,KAAK;gBACb,WAAW,EAAE,CAAC;gBACd,aAAa,EAAE,CAAC;gBAChB,eAAe,EAAE,CAAC;gBAClB,UAAU,EAAE,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC/B,QAAQ;gBACR,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC;YAEF,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC;YAClC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YAGhD,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAE3B,eAAM,CAAC,IAAI,CAAC,gCAAgC,WAAW,SAAS,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC;YAC/E,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAC;YAEzC,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0BAA0B,WAAW,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKD,YAAY;QACV,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,eAAM,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC;YAE5D,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAE1B,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC;YACxC,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;YAEhC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAKD,iBAAiB;QACf,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAKD,KAAK,CAAC,iBAAiB;QACrB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,iBAAO,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACnE,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,eAAM,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;gBAChE,OAAO;YACT,CAAC;YAGD,IAAI,CAAC,cAAc,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;YAG7C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YAErE,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACpD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAKO,mBAAmB;QACzB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAG1B,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC,GAAG,EAAE;YACrC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3B,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IACpB,CAAC;IAKO,kBAAkB;QACxB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACnC,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;QAClC,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,sBAAsB,CAAC,QAAmC;QAC9D,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,eAAM,CAAC,KAAK,CAAC,mDAAmD,CAAC,CAAC;YAClE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,iBAAO,CAAC,sBAAsB,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAE/F,IAAI,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;gBAE5C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBACtD,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBACrE,eAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;YACxD,CAAC;YAED,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC3D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,WAAmB,EAAE;QACxC,OAAO,IAAI,CAAC,sBAAsB,CAAC;YACjC,SAAS,EAAE,IAAI;YACf,gBAAgB,EAAE,QAAQ;SAC3B,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,eAAe;QACnB,OAAO,IAAI,CAAC,sBAAsB,CAAC;YACjC,SAAS,EAAE,KAAK;YAChB,gBAAgB,EAAE,CAAC;SACpB,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,mBAAmB,CAAC,cAAsB,CAAC;QAC/C,OAAO,IAAI,CAAC,sBAAsB,CAAC;YACjC,cAAc,EAAE,IAAI;YACpB,sBAAsB,EAAE,WAAW;SACpC,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,oBAAoB;QACxB,OAAO,IAAI,CAAC,sBAAsB,CAAC;YACjC,cAAc,EAAE,KAAK;YACrB,sBAAsB,EAAE,CAAC;SAC1B,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,qBAAqB;QACzB,OAAO,IAAI,CAAC,sBAAsB,CAAC;YACjC,gBAAgB,EAAE,IAAI;SACvB,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,sBAAsB;QAC1B,OAAO,IAAI,CAAC,sBAAsB,CAAC;YACjC,gBAAgB,EAAE,KAAK;SACxB,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,gBAAgB;QACpB,OAAO,IAAI,CAAC,sBAAsB,CAAC;YACjC,WAAW,EAAE,IAAI;SAClB,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,iBAAiB;QACrB,OAAO,IAAI,CAAC,sBAAsB,CAAC;YACjC,WAAW,EAAE,KAAK;SACnB,CAAC,CAAC;IACL,CAAC;IAKD,gBAAgB,CAAC,WAAmB;QAClC,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IAC5C,CAAC;IAKD,UAAU;QACR,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC1B,eAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;IACvC,CAAC;IAKD,aAAa;QACX,OAAO;YACL,cAAc,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI;YACtC,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,IAAI,IAAI;YACjD,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc;SAClC,CAAC;IACJ,CAAC;IAKD,IAAI,SAAS;QACX,OAAO,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAKD,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,cAAc,EAAE,UAAU,CAAC;IACzC,CAAC;IAKD,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC;IACnC,CAAC;IAKD,IAAI,uBAAuB;QACzB,OAAO,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC;IACvC,CAAC;IAKD,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,SAAS,IAAI,KAAK,CAAC;IAC3D,CAAC;IAKD,IAAI,sBAAsB;QACxB,OAAO,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,cAAc,IAAI,KAAK,CAAC;IAChE,CAAC;IAKD,IAAI,wBAAwB;QAC1B,OAAO,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,gBAAgB,IAAI,KAAK,CAAC;IAClE,CAAC;IAKD,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,WAAW,IAAI,KAAK,CAAC;IAC7D,CAAC;IAKD,OAAO;QACL,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;IAClC,CAAC;CACF;AA9TD,wCA8TC"}