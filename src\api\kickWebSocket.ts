import WebSocket from 'ws';
import { EventEmitter } from 'events';
import { kickConfig } from '../config';
import { logger } from '../utils/logger';
import { ChatMessage, KickUser, WebSocketMessage } from '../types';

export interface PusherMessage {
  event: string;
  data: string;
  channel?: string;
}

export interface ChatMessageData {
  id: string;
  chatroom_id: number;
  content: string;
  type: string;
  created_at: string;
  sender: KickUser;
  metadata?: any;
}

export class KickWebSocket extends EventEmitter {
  private ws?: WebSocket;
  private channelId?: number;
  private chatroomId?: number;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 5000;
  private heartbeatInterval?: NodeJS.Timeout;
  private connectionTimeout?: NodeJS.Timeout;

  constructor() {
    super();
  }

  /**
   * Connect to Kick WebSocket for a specific channel
   */
  async connect(channelId: number, chatroomId: number): Promise<void> {
    this.channelId = channelId;
    this.chatroomId = chatroomId;

    return new Promise((resolve, reject) => {
      try {
        const wsUrl = `${kickConfig.websocketUrl}?protocol=7&client=js&version=8.4.0-rc2&flash=false`;
        
        logger.info(`Connecting to Kick WebSocket: ${wsUrl}`);
        this.ws = new WebSocket(wsUrl);

        this.connectionTimeout = setTimeout(() => {
          if (!this.isConnected) {
            logger.error('WebSocket connection timeout');
            this.ws?.terminate();
            reject(new Error('Connection timeout'));
          }
        }, 10000);

        this.ws.on('open', () => {
          logger.info('WebSocket connection opened');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          
          if (this.connectionTimeout) {
            clearTimeout(this.connectionTimeout);
          }

          // Send connection message
          this.sendConnectionMessage();
          
          // Subscribe to channel
          this.subscribeToChannel();
          
          // Start heartbeat
          this.startHeartbeat();
          
          resolve();
        });

        this.ws.on('message', (data: WebSocket.Data) => {
          try {
            const message = JSON.parse(data.toString()) as PusherMessage;
            this.handleMessage(message);
          } catch (error) {
            logger.error('Failed to parse WebSocket message:', error);
          }
        });

        this.ws.on('close', (code: number, reason: Buffer) => {
          logger.warn(`WebSocket connection closed: ${code} ${reason.toString()}`);
          this.isConnected = false;
          this.cleanup();
          
          if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          } else {
            logger.error('Max reconnection attempts reached');
            this.emit('error', new Error('Max reconnection attempts reached'));
          }
        });

        this.ws.on('error', (error: Error) => {
          logger.error('WebSocket error:', error);
          this.isConnected = false;
          this.emit('error', error);
          
          if (this.connectionTimeout) {
            clearTimeout(this.connectionTimeout);
          }
          
          reject(error);
        });

      } catch (error) {
        logger.error('Failed to create WebSocket connection:', error);
        reject(error);
      }
    });
  }

  /**
   * Send initial connection message to Pusher
   */
  private sendConnectionMessage(): void {
    const connectionMessage = {
      event: 'pusher:connection',
      data: {
        protocol: 7,
        client: 'js',
        version: '8.4.0-rc2',
      },
    };

    this.send(connectionMessage);
  }

  /**
   * Subscribe to the channel's chatroom
   */
  private subscribeToChannel(): void {
    if (!this.chatroomId) {
      logger.error('Cannot subscribe: chatroomId not set');
      return;
    }

    const subscribeMessage = {
      event: 'pusher:subscribe',
      data: {
        auth: '',
        channel: `chatrooms.${this.chatroomId}.v2`,
      },
    };

    this.send(subscribeMessage);
    logger.info(`Subscribed to chatroom ${this.chatroomId}`);
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleMessage(message: PusherMessage): void {
    logger.debug('Received WebSocket message:', message);

    switch (message.event) {
      case 'pusher:connection_established':
        logger.info('Pusher connection established');
        this.emit('connected');
        break;

      case 'pusher:subscription_succeeded':
        logger.info('Successfully subscribed to channel');
        this.emit('subscribed');
        break;

      case 'pusher:pong':
        logger.debug('Received pong from server');
        break;

      case 'App\\Events\\ChatMessageEvent':
        this.handleChatMessage(message);
        break;

      case 'App\\Events\\UserBannedEvent':
        this.handleUserBanned(message);
        break;

      case 'App\\Events\\UserUnbannedEvent':
        this.handleUserUnbanned(message);
        break;

      case 'App\\Events\\FollowersUpdated':
        this.handleFollowersUpdated(message);
        break;

      case 'App\\Events\\StreamerIsLive':
        this.handleStreamerLive(message);
        break;

      case 'App\\Events\\StreamerIsOffline':
        this.handleStreamerOffline(message);
        break;

      default:
        logger.debug(`Unhandled event: ${message.event}`);
        this.emit('unknown_event', message);
        break;
    }
  }

  /**
   * Handle chat message events
   */
  private handleChatMessage(message: PusherMessage): void {
    try {
      const data: ChatMessageData = JSON.parse(message.data);
      
      const chatMessage: ChatMessage = {
        id: data.id,
        chatroom_id: data.chatroom_id,
        content: data.content,
        type: data.type,
        created_at: data.created_at,
        sender: data.sender,
        metadata: data.metadata,
      };

      logger.chatMessage(chatMessage.sender.username, chatMessage.content, `chatroom-${chatMessage.chatroom_id}`);
      this.emit('message', chatMessage);
    } catch (error) {
      logger.error('Failed to parse chat message:', error);
    }
  }

  /**
   * Handle user banned events
   */
  private handleUserBanned(message: PusherMessage): void {
    try {
      const data = JSON.parse(message.data);
      logger.moderationAction('banned', data.user.username, data.banned_by?.username || 'system');
      this.emit('user_banned', data);
    } catch (error) {
      logger.error('Failed to parse user banned event:', error);
    }
  }

  /**
   * Handle user unbanned events
   */
  private handleUserUnbanned(message: PusherMessage): void {
    try {
      const data = JSON.parse(message.data);
      logger.moderationAction('unbanned', data.user.username, data.unbanned_by?.username || 'system');
      this.emit('user_unbanned', data);
    } catch (error) {
      logger.error('Failed to parse user unbanned event:', error);
    }
  }

  /**
   * Handle followers updated events
   */
  private handleFollowersUpdated(message: PusherMessage): void {
    try {
      const data = JSON.parse(message.data);
      logger.info(`New follower: ${data.username}`);
      this.emit('new_follower', data);
    } catch (error) {
      logger.error('Failed to parse followers updated event:', error);
    }
  }

  /**
   * Handle streamer live events
   */
  private handleStreamerLive(message: PusherMessage): void {
    try {
      const data = JSON.parse(message.data);
      logger.info('Streamer went live');
      this.emit('streamer_live', data);
    } catch (error) {
      logger.error('Failed to parse streamer live event:', error);
    }
  }

  /**
   * Handle streamer offline events
   */
  private handleStreamerOffline(message: PusherMessage): void {
    try {
      const data = JSON.parse(message.data);
      logger.info('Streamer went offline');
      this.emit('streamer_offline', data);
    } catch (error) {
      logger.error('Failed to parse streamer offline event:', error);
    }
  }

  /**
   * Send a message through the WebSocket
   */
  private send(message: any): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      logger.warn('Cannot send message: WebSocket not connected');
    }
  }

  /**
   * Start heartbeat to keep connection alive
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected) {
        this.send({ event: 'pusher:ping', data: {} });
      }
    }, 30000); // Send ping every 30 seconds
  }

  /**
   * Schedule reconnection attempt
   */
  private scheduleReconnect(): void {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * this.reconnectAttempts;
    
    logger.info(`Scheduling reconnection attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);
    
    setTimeout(() => {
      if (this.channelId && this.chatroomId) {
        this.connect(this.channelId, this.chatroomId).catch((error) => {
          logger.error('Reconnection failed:', error);
        });
      }
    }, delay);
  }

  /**
   * Clean up resources
   */
  private cleanup(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = undefined;
    }
    
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = undefined;
    }
  }

  /**
   * Disconnect from WebSocket
   */
  disconnect(): void {
    logger.info('Disconnecting from WebSocket');
    this.isConnected = false;
    this.cleanup();
    
    if (this.ws) {
      this.ws.close();
      this.ws = undefined;
    }
  }

  /**
   * Get connection status
   */
  get connected(): boolean {
    return this.isConnected;
  }
}
