import { BotConfig, PermissionLevel } from '../types';
export declare const config: BotConfig;
export declare const webConfig: {
    port: number;
    jwtSecret: string;
    adminUsername: string;
    adminPassword: string;
};
export declare const kickConfig: {
    apiBaseUrl: string;
    websocketUrl: string;
    pusherKey: string;
    pusherCluster: string;
};
export declare const databaseConfig: {
    path: string;
};
export declare const logConfig: {
    level: string;
    file: string;
};
export declare const defaultCommands: {
    name: string;
    description: string;
    usage: string;
    aliases: string[];
    cooldown: number;
    permission: PermissionLevel;
    enabled: boolean;
}[];
//# sourceMappingURL=index.d.ts.map