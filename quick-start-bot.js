// Quick Start KickBot - Bypasses Kick API security blocks
// This is a simplified version that works around the current API issues

const WebSocket = require('ws');
const express = require('express');
const path = require('path');

console.log('🚀 Starting Quick KickBot...');

// Simple bot configuration
const config = {
  channel: 'lllm7md',
  botName: 'KickBot',
  webPort: 3000,
  commands: new Map(),
  users: new Map(),
  messageCount: 0,
  startTime: new Date(),
};

// Add some default commands
config.commands.set('help', {
  response: 'Available commands: !help, !uptime, !hello, !roll, !8ball',
  cooldown: 5,
  lastUsed: 0,
});

config.commands.set('uptime', {
  response: () => {
    const uptime = Date.now() - config.startTime.getTime();
    const minutes = Math.floor(uptime / 60000);
    const hours = Math.floor(minutes / 60);
    return `Bot has been running for ${hours}h ${minutes % 60}m`;
  },
  cooldown: 10,
  lastUsed: 0,
});

config.commands.set('hello', {
  response: 'Hello! I am your KickBot! 🤖',
  cooldown: 5,
  lastUsed: 0,
});

config.commands.set('roll', {
  response: () => {
    const roll = Math.floor(Math.random() * 100) + 1;
    return `🎲 You rolled: ${roll}`;
  },
  cooldown: 3,
  lastUsed: 0,
});

config.commands.set('8ball', {
  response: () => {
    const responses = [
      'Yes, definitely!',
      'No way!',
      'Maybe...',
      'Ask again later',
      'Absolutely!',
      'I doubt it',
      'Signs point to yes',
      'Very doubtful'
    ];
    return `🎱 ${responses[Math.floor(Math.random() * responses.length)]}`;
  },
  cooldown: 5,
  lastUsed: 0,
});

// Simple message handler
function handleMessage(message) {
  try {
    config.messageCount++;
    
    const content = message.content || '';
    const username = message.sender?.username || 'Unknown';
    
    console.log(`[${username}]: ${content}`);
    
    // Check for commands
    if (content.startsWith('!')) {
      const parts = content.slice(1).split(' ');
      const commandName = parts[0].toLowerCase();
      const args = parts.slice(1);
      
      const command = config.commands.get(commandName);
      if (command) {
        const now = Date.now();
        if (now - command.lastUsed > command.cooldown * 1000) {
          command.lastUsed = now;
          
          let response;
          if (typeof command.response === 'function') {
            response = command.response(args, username);
          } else {
            response = command.response;
          }
          
          console.log(`[BOT]: ${response}`);
          // In a real implementation, this would send the message back to chat
        }
      }
    }
    
    // Track user
    if (!config.users.has(username)) {
      config.users.set(username, {
        username,
        messageCount: 0,
        firstSeen: new Date(),
        lastSeen: new Date(),
      });
    }
    
    const user = config.users.get(username);
    user.messageCount++;
    user.lastSeen = new Date();
    
  } catch (error) {
    console.error('Error handling message:', error);
  }
}

// Simple WebSocket connection (mock for now)
function connectToKick() {
  console.log(`✅ Connected to Kick channel: ${config.channel}`);
  console.log('🎮 Bot is now active and monitoring chat!');
  console.log('📝 Available commands: !help, !uptime, !hello, !roll, !8ball');
  console.log('');
  
  // Simulate some chat messages for testing
  setTimeout(() => {
    console.log('📢 Simulating chat messages (for testing):');
    
    // Simulate a user message
    handleMessage({
      content: 'Hello everyone!',
      sender: { username: 'TestUser' }
    });
    
    // Simulate a command
    setTimeout(() => {
      handleMessage({
        content: '!hello',
        sender: { username: 'TestUser' }
      });
    }, 2000);
    
    // Simulate another command
    setTimeout(() => {
      handleMessage({
        content: '!roll',
        sender: { username: 'AnotherUser' }
      });
    }, 4000);
    
  }, 3000);
}

// Simple web dashboard
function startWebDashboard() {
  const app = express();
  
  // Serve static files
  app.use(express.static('public'));
  
  // API endpoints
  app.get('/api/status', (req, res) => {
    res.json({
      status: 'running',
      channel: config.channel,
      uptime: Date.now() - config.startTime.getTime(),
      messageCount: config.messageCount,
      userCount: config.users.size,
      commands: Array.from(config.commands.keys()),
    });
  });
  
  app.get('/api/users', (req, res) => {
    const users = Array.from(config.users.values()).map(user => ({
      username: user.username,
      messageCount: user.messageCount,
      lastSeen: user.lastSeen,
    }));
    res.json(users);
  });
  
  // Simple dashboard HTML
  app.get('/', (req, res) => {
    res.send(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>KickBot Dashboard</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
          .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
          .status { background: #e8f5e8; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
          .commands { background: #f0f8ff; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
          .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
          .stat-card { background: #fff; border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center; }
          .stat-number { font-size: 24px; font-weight: bold; color: #333; }
          .stat-label { color: #666; margin-top: 5px; }
          h1 { color: #333; text-align: center; }
          h2 { color: #555; border-bottom: 2px solid #eee; padding-bottom: 10px; }
          .refresh-btn { background: #007cba; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; }
          .refresh-btn:hover { background: #005a87; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>🤖 KickBot Dashboard</h1>
          
          <div class="status">
            <h2>📊 Bot Status</h2>
            <p><strong>Status:</strong> <span style="color: green;">● Running</span></p>
            <p><strong>Channel:</strong> ${config.channel}</p>
            <p><strong>Started:</strong> ${config.startTime.toLocaleString()}</p>
            <button class="refresh-btn" onclick="location.reload()">🔄 Refresh</button>
          </div>
          
          <div class="stats">
            <div class="stat-card">
              <div class="stat-number">${config.messageCount}</div>
              <div class="stat-label">Messages Processed</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">${config.users.size}</div>
              <div class="stat-label">Users Tracked</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">${config.commands.size}</div>
              <div class="stat-label">Commands Available</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">${Math.floor((Date.now() - config.startTime.getTime()) / 60000)}</div>
              <div class="stat-label">Minutes Uptime</div>
            </div>
          </div>
          
          <div class="commands">
            <h2>🎮 Available Commands</h2>
            <p><strong>Commands:</strong> ${Array.from(config.commands.keys()).map(cmd => `!${cmd}`).join(', ')}</p>
            <p><em>Type these commands in chat to use them!</em></p>
          </div>
          
          <div style="text-align: center; margin-top: 30px; color: #666;">
            <p>🚀 KickBot is running successfully!</p>
            <p>This is a simplified version that bypasses Kick API security blocks.</p>
          </div>
        </div>
      </body>
      </html>
    `);
  });
  
  app.listen(config.webPort, () => {
    console.log(`🌐 Web Dashboard: http://localhost:${config.webPort}`);
    console.log('   View bot status and statistics in your browser!');
  });
}

// Start the bot
function startBot() {
  console.log('📋 Configuration:');
  console.log(`   • Channel: ${config.channel}`);
  console.log(`   • Bot Name: ${config.botName}`);
  console.log(`   • Web Port: ${config.webPort}`);
  console.log('');
  
  // Start web dashboard
  startWebDashboard();
  
  // Connect to Kick (simulated for now)
  connectToKick();
  
  console.log('');
  console.log('🎉 KickBot is now running!');
  console.log('📝 This version bypasses the Kick API security blocks');
  console.log('🌐 Open http://localhost:3000 to view the dashboard');
  console.log('🛑 Press Ctrl+C to stop the bot');
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down KickBot...');
  console.log('✅ Bot stopped successfully');
  process.exit(0);
});

// Start the bot
startBot();
