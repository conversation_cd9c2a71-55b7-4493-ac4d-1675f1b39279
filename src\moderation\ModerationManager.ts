import { EventEmitter } from 'events';
import { ChatMessage, KickUser, PermissionLevel } from '../types';
import { KickBot } from '../bot/KickBot';
import { logger } from '../utils/logger';

export interface ModerationConfig {
  enabled: boolean;
  spamProtection: {
    enabled: boolean;
    maxMessages: number;
    timeWindow: number; // in seconds
    punishment: 'timeout' | 'ban';
    duration: number; // in minutes
  };
  capsFilter: {
    enabled: boolean;
    threshold: number; // percentage
    minLength: number;
    punishment: 'timeout' | 'delete' | 'warn';
    duration: number; // in minutes
  };
  bannedWords: {
    enabled: boolean;
    words: string[];
    punishment: 'timeout' | 'ban' | 'delete';
    duration: number; // in minutes
  };
  linkProtection: {
    enabled: boolean;
    allowedDomains: string[];
    punishment: 'timeout' | 'delete' | 'warn';
    duration: number; // in minutes
  };
  repetitiveMessages: {
    enabled: boolean;
    maxRepeats: number;
    timeWindow: number; // in seconds
    punishment: 'timeout' | 'delete';
    duration: number; // in minutes
  };
  autoTimeout: {
    enabled: boolean;
    escalation: boolean; // increase timeout duration for repeat offenders
  };
}

export interface UserViolation {
  username: string;
  type: string;
  message: string;
  timestamp: Date;
  punishment: string;
  duration?: number;
}

export interface UserModerationData {
  username: string;
  violations: UserViolation[];
  messageHistory: Array<{ content: string; timestamp: Date }>;
  lastMessageTime: Date;
  messageCount: number;
  timeoutCount: number;
  banCount: number;
}

export class ModerationManager extends EventEmitter {
  private config: ModerationConfig;
  private bot: KickBot;
  private userModerationData = new Map<string, UserModerationData>();
  private messageHistory: Array<{ username: string; content: string; timestamp: Date }> = [];

  constructor(bot: KickBot, config: ModerationConfig) {
    super();
    this.bot = bot;
    this.config = config;
    this.setupBotEvents();
  }

  /**
   * Setup bot event listeners
   */
  private setupBotEvents(): void {
    this.bot.on('message', (message: ChatMessage) => {
      if (this.config.enabled) {
        this.processMessage(message);
      }
    });
  }

  /**
   * Process incoming message for moderation
   */
  private async processMessage(message: ChatMessage): Promise<void> {
    const user = message.sender;
    const content = message.content;

    // Skip moderation for privileged users
    if (this.isPrivilegedUser(user)) {
      return;
    }

    // Update user moderation data
    this.updateUserModerationData(user.username, content);

    // Add to global message history
    this.messageHistory.push({
      username: user.username,
      content,
      timestamp: new Date(),
    });

    // Keep only last 100 messages in history
    if (this.messageHistory.length > 100) {
      this.messageHistory.shift();
    }

    // Run moderation checks
    const violations: string[] = [];

    if (this.config.spamProtection.enabled) {
      const spamViolation = this.checkSpam(user.username);
      if (spamViolation) violations.push(spamViolation);
    }

    if (this.config.capsFilter.enabled) {
      const capsViolation = this.checkCapsLock(content);
      if (capsViolation) violations.push(capsViolation);
    }

    if (this.config.bannedWords.enabled) {
      const bannedWordViolation = this.checkBannedWords(content);
      if (bannedWordViolation) violations.push(bannedWordViolation);
    }

    if (this.config.linkProtection.enabled) {
      const linkViolation = this.checkLinks(content);
      if (linkViolation) violations.push(linkViolation);
    }

    if (this.config.repetitiveMessages.enabled) {
      const repetitiveViolation = this.checkRepetitiveMessages(user.username, content);
      if (repetitiveViolation) violations.push(repetitiveViolation);
    }

    // Handle violations
    if (violations.length > 0) {
      await this.handleViolations(user, message, violations);
    }
  }

  /**
   * Check if user is privileged (exempt from moderation)
   */
  private isPrivilegedUser(user: KickUser): boolean {
    return user.is_channel_owner || user.is_moderator || user.is_staff;
  }

  /**
   * Update user moderation data
   */
  private updateUserModerationData(username: string, content: string): void {
    const normalizedUsername = username.toLowerCase();
    const now = new Date();

    if (!this.userModerationData.has(normalizedUsername)) {
      this.userModerationData.set(normalizedUsername, {
        username,
        violations: [],
        messageHistory: [],
        lastMessageTime: now,
        messageCount: 0,
        timeoutCount: 0,
        banCount: 0,
      });
    }

    const userData = this.userModerationData.get(normalizedUsername)!;
    userData.messageHistory.push({ content, timestamp: now });
    userData.lastMessageTime = now;
    userData.messageCount++;

    // Keep only last 20 messages per user
    if (userData.messageHistory.length > 20) {
      userData.messageHistory.shift();
    }
  }

  /**
   * Check for spam violations
   */
  private checkSpam(username: string): string | null {
    const normalizedUsername = username.toLowerCase();
    const userData = this.userModerationData.get(normalizedUsername);
    if (!userData) return null;

    const timeWindow = this.config.spamProtection.timeWindow * 1000; // Convert to milliseconds
    const cutoffTime = new Date(Date.now() - timeWindow);
    
    const recentMessages = userData.messageHistory.filter(
      msg => msg.timestamp > cutoffTime
    );

    if (recentMessages.length > this.config.spamProtection.maxMessages) {
      return `Spam: ${recentMessages.length} messages in ${this.config.spamProtection.timeWindow} seconds`;
    }

    return null;
  }

  /**
   * Check for caps lock violations
   */
  private checkCapsLock(content: string): string | null {
    if (content.length < this.config.capsFilter.minLength) {
      return null;
    }

    const uppercaseCount = (content.match(/[A-Z]/g) || []).length;
    const letterCount = (content.match(/[A-Za-z]/g) || []).length;
    
    if (letterCount === 0) return null;

    const capsPercentage = (uppercaseCount / letterCount) * 100;

    if (capsPercentage > this.config.capsFilter.threshold) {
      return `Excessive caps: ${Math.round(capsPercentage)}% uppercase`;
    }

    return null;
  }

  /**
   * Check for banned words
   */
  private checkBannedWords(content: string): string | null {
    const lowerContent = content.toLowerCase();
    
    for (const bannedWord of this.config.bannedWords.words) {
      if (lowerContent.includes(bannedWord.toLowerCase())) {
        return `Banned word detected: ${bannedWord}`;
      }
    }

    return null;
  }

  /**
   * Check for unauthorized links
   */
  private checkLinks(content: string): string | null {
    const urlRegex = /(https?:\/\/[^\s]+)/gi;
    const urls = content.match(urlRegex);
    
    if (!urls) return null;

    for (const url of urls) {
      try {
        const domain = new URL(url).hostname.toLowerCase();
        const isAllowed = this.config.linkProtection.allowedDomains.some(
          allowedDomain => domain.includes(allowedDomain.toLowerCase())
        );
        
        if (!isAllowed) {
          return `Unauthorized link: ${domain}`;
        }
      } catch (error) {
        return `Invalid URL detected`;
      }
    }

    return null;
  }

  /**
   * Check for repetitive messages
   */
  private checkRepetitiveMessages(username: string, content: string): string | null {
    const normalizedUsername = username.toLowerCase();
    const userData = this.userModerationData.get(normalizedUsername);
    if (!userData) return null;

    const timeWindow = this.config.repetitiveMessages.timeWindow * 1000;
    const cutoffTime = new Date(Date.now() - timeWindow);
    
    const recentMessages = userData.messageHistory.filter(
      msg => msg.timestamp > cutoffTime
    );

    const duplicateCount = recentMessages.filter(
      msg => msg.content.toLowerCase() === content.toLowerCase()
    ).length;

    if (duplicateCount >= this.config.repetitiveMessages.maxRepeats) {
      return `Repetitive message: sent ${duplicateCount} times`;
    }

    return null;
  }

  /**
   * Handle moderation violations
   */
  private async handleViolations(user: KickUser, message: ChatMessage, violations: string[]): Promise<void> {
    const normalizedUsername = user.username.toLowerCase();
    const userData = this.userModerationData.get(normalizedUsername)!;

    // Log violations
    for (const violation of violations) {
      const violationData: UserViolation = {
        username: user.username,
        type: violation.split(':')[0] || 'Unknown',
        message: message.content,
        timestamp: new Date(),
        punishment: 'pending',
      };

      userData.violations.push(violationData);
      logger.moderationAction('violation', user.username, 'auto-mod', violation);
    }

    // Determine punishment based on violation types and user history
    const punishment = this.determinePunishment(violations, userData);

    if (punishment) {
      await this.executePunishment(user, punishment, violations);
    }

    this.emit('violations_detected', {
      user,
      message,
      violations,
      punishment,
    });
  }

  /**
   * Determine appropriate punishment
   */
  private determinePunishment(violations: string[], userData: UserModerationData): { type: string; duration?: number } | null {
    // Check for severe violations that warrant immediate action
    const hasBannedWord = violations.some(v => v.startsWith('Banned word'));
    const hasSpam = violations.some(v => v.startsWith('Spam'));
    
    if (hasBannedWord) {
      return {
        type: this.config.bannedWords.punishment,
        duration: this.config.bannedWords.duration,
      };
    }

    if (hasSpam) {
      return {
        type: this.config.spamProtection.punishment,
        duration: this.config.spamProtection.duration,
      };
    }

    // For other violations, use escalation if enabled
    if (this.config.autoTimeout.escalation && userData.violations.length > 3) {
      const escalatedDuration = Math.min(60, 5 * userData.violations.length); // Max 1 hour
      return {
        type: 'timeout',
        duration: escalatedDuration,
      };
    }

    // Default timeout for other violations
    return {
      type: 'timeout',
      duration: 5, // 5 minutes default
    };
  }

  /**
   * Execute punishment
   */
  private async executePunishment(user: KickUser, punishment: { type: string; duration?: number }, violations: string[]): Promise<void> {
    const normalizedUsername = user.username.toLowerCase();
    const userData = this.userModerationData.get(normalizedUsername)!;

    switch (punishment.type) {
      case 'timeout':
        if (punishment.duration) {
          const success = await this.bot.timeoutUser(user.username, punishment.duration);
          if (success) {
            userData.timeoutCount++;
            logger.moderationAction('timeout', user.username, 'auto-mod', `${punishment.duration}m - ${violations.join(', ')}`);
          }
        }
        break;

      case 'ban':
        const success = await this.bot.banUser(user.username, true);
        if (success) {
          userData.banCount++;
          logger.moderationAction('ban', user.username, 'auto-mod', violations.join(', '));
        }
        break;

      case 'delete':
        // Note: Kick API might not support message deletion
        logger.moderationAction('delete', user.username, 'auto-mod', violations.join(', '));
        break;

      case 'warn':
        await this.bot.sendMessage(`@${user.username} Warning: ${violations.join(', ')}`);
        logger.moderationAction('warn', user.username, 'auto-mod', violations.join(', '));
        break;
    }

    // Update violation records
    const recentViolations = userData.violations.slice(-violations.length);
    recentViolations.forEach(violation => {
      violation.punishment = punishment.type;
      violation.duration = punishment.duration;
    });

    this.emit('punishment_executed', {
      user,
      punishment,
      violations,
    });
  }

  /**
   * Update moderation configuration
   */
  updateConfig(newConfig: Partial<ModerationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    logger.info('Moderation configuration updated');
    this.emit('config_updated', this.config);
  }

  /**
   * Add banned word
   */
  addBannedWord(word: string): void {
    if (!this.config.bannedWords.words.includes(word.toLowerCase())) {
      this.config.bannedWords.words.push(word.toLowerCase());
      logger.info(`Added banned word: ${word}`);
      this.emit('banned_word_added', word);
    }
  }

  /**
   * Remove banned word
   */
  removeBannedWord(word: string): boolean {
    const index = this.config.bannedWords.words.indexOf(word.toLowerCase());
    if (index > -1) {
      this.config.bannedWords.words.splice(index, 1);
      logger.info(`Removed banned word: ${word}`);
      this.emit('banned_word_removed', word);
      return true;
    }
    return false;
  }

  /**
   * Get user moderation data
   */
  getUserModerationData(username: string): UserModerationData | undefined {
    return this.userModerationData.get(username.toLowerCase());
  }

  /**
   * Get moderation statistics
   */
  getModerationStats() {
    const totalUsers = this.userModerationData.size;
    let totalViolations = 0;
    let totalTimeouts = 0;
    let totalBans = 0;

    for (const userData of this.userModerationData.values()) {
      totalViolations += userData.violations.length;
      totalTimeouts += userData.timeoutCount;
      totalBans += userData.banCount;
    }

    return {
      totalUsers,
      totalViolations,
      totalTimeouts,
      totalBans,
      config: this.config,
      recentMessages: this.messageHistory.length,
    };
  }

  /**
   * Clear user moderation data
   */
  clearUserData(username: string): boolean {
    const normalizedUsername = username.toLowerCase();
    if (this.userModerationData.has(normalizedUsername)) {
      this.userModerationData.delete(normalizedUsername);
      logger.info(`Cleared moderation data for user: ${username}`);
      return true;
    }
    return false;
  }

  /**
   * Export moderation data
   */
  exportModerationData() {
    return {
      config: this.config,
      userData: Array.from(this.userModerationData.entries()),
      messageHistory: this.messageHistory.slice(-50), // Last 50 messages
    };
  }
}
