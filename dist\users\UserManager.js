"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserManager = void 0;
const events_1 = require("events");
const types_1 = require("../types");
const kickApi_1 = require("../api/kickApi");
const logger_1 = require("../utils/logger");
class UserManager extends events_1.EventEmitter {
    constructor() {
        super();
        this.userCache = {};
        this.moderators = new Set();
        this.vips = new Set();
        this.subscribers = new Set();
        this.followers = new Set();
        this.bannedUsers = new Set();
    }
    updateUser(user) {
        const username = user.username.toLowerCase();
        const now = new Date();
        if (!this.userCache[username]) {
            this.userCache[username] = {
                user,
                lastSeen: now,
                messageCount: 0,
                commandCount: 0,
                timeInChat: 0,
                points: 0,
            };
            logger_1.logger.debug(`Added new user to cache: ${username}`);
        }
        else {
            this.userCache[username].user = user;
            this.userCache[username].lastSeen = now;
        }
        this.updateUserPermissions(user);
    }
    updateUserPermissions(user) {
        const username = user.username.toLowerCase();
        if (user.is_moderator) {
            this.moderators.add(username);
        }
        else {
            this.moderators.delete(username);
        }
        if (user.is_vip) {
            this.vips.add(username);
        }
        else {
            this.vips.delete(username);
        }
        if (user.is_subscriber) {
            this.subscribers.add(username);
        }
        else {
            this.subscribers.delete(username);
        }
        if (user.is_follower) {
            this.followers.add(username);
        }
        else {
            this.followers.delete(username);
        }
        if (user.is_banned) {
            this.bannedUsers.add(username);
        }
        else {
            this.bannedUsers.delete(username);
        }
    }
    async getUser(username) {
        const normalizedUsername = username.toLowerCase();
        if (this.userCache[normalizedUsername]) {
            return this.userCache[normalizedUsername].user;
        }
        try {
            const user = await kickApi_1.kickAPI.getUser(username);
            if (user) {
                this.updateUser(user);
                return user;
            }
        }
        catch (error) {
            logger_1.logger.error(`Failed to fetch user ${username}:`, error);
        }
        return null;
    }
    getUserPermissionLevel(user) {
        let userData = null;
        if (typeof user === 'string') {
            const normalizedUsername = user.toLowerCase();
            userData = this.userCache[normalizedUsername]?.user || null;
        }
        else {
            userData = user;
        }
        if (!userData) {
            return types_1.PermissionLevel.EVERYONE;
        }
        if (userData.is_channel_owner)
            return types_1.PermissionLevel.OWNER;
        if (userData.is_moderator)
            return types_1.PermissionLevel.MODERATOR;
        if (userData.is_vip)
            return types_1.PermissionLevel.VIP;
        if (userData.is_subscriber)
            return types_1.PermissionLevel.SUBSCRIBER;
        if (userData.is_follower)
            return types_1.PermissionLevel.FOLLOWER;
        return types_1.PermissionLevel.EVERYONE;
    }
    hasPermission(user, requiredLevel) {
        const userLevel = this.getUserPermissionLevel(user);
        return userLevel >= requiredLevel;
    }
    incrementMessageCount(username) {
        const normalizedUsername = username.toLowerCase();
        if (this.userCache[normalizedUsername]) {
            this.userCache[normalizedUsername].messageCount++;
            this.userCache[normalizedUsername].lastSeen = new Date();
        }
    }
    incrementCommandCount(username) {
        const normalizedUsername = username.toLowerCase();
        if (this.userCache[normalizedUsername]) {
            this.userCache[normalizedUsername].commandCount++;
        }
    }
    addPoints(username, points) {
        const normalizedUsername = username.toLowerCase();
        if (this.userCache[normalizedUsername]) {
            this.userCache[normalizedUsername].points += points;
            this.emit('points_added', { username, points, total: this.userCache[normalizedUsername].points });
        }
    }
    removePoints(username, points) {
        const normalizedUsername = username.toLowerCase();
        if (this.userCache[normalizedUsername]) {
            const currentPoints = this.userCache[normalizedUsername].points;
            if (currentPoints >= points) {
                this.userCache[normalizedUsername].points -= points;
                this.emit('points_removed', { username, points, total: this.userCache[normalizedUsername].points });
                return true;
            }
        }
        return false;
    }
    getUserStats(username) {
        const normalizedUsername = username.toLowerCase();
        const userData = this.userCache[normalizedUsername];
        if (!userData) {
            return null;
        }
        return {
            username: userData.user.username,
            messages_sent: userData.messageCount,
            commands_used: userData.commandCount,
            time_in_chat: userData.timeInChat,
            points: userData.points,
            last_seen: userData.lastSeen.toISOString(),
            is_active: this.isUserActive(username),
        };
    }
    isUserActive(username) {
        const normalizedUsername = username.toLowerCase();
        const userData = this.userCache[normalizedUsername];
        if (!userData) {
            return false;
        }
        const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000);
        return userData.lastSeen > tenMinutesAgo;
    }
    getModerators() {
        return Array.from(this.moderators);
    }
    getVips() {
        return Array.from(this.vips);
    }
    getSubscribers() {
        return Array.from(this.subscribers);
    }
    getFollowers() {
        return Array.from(this.followers);
    }
    getBannedUsers() {
        return Array.from(this.bannedUsers);
    }
    getActiveUsers() {
        const activeUsers = [];
        const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000);
        for (const [username, userData] of Object.entries(this.userCache)) {
            if (userData.lastSeen > tenMinutesAgo) {
                activeUsers.push(userData.user.username);
            }
        }
        return activeUsers;
    }
    getTopUsersByPoints(limit = 10) {
        const users = Object.values(this.userCache)
            .map(userData => ({
            username: userData.user.username,
            points: userData.points,
        }))
            .sort((a, b) => b.points - a.points)
            .slice(0, limit);
        return users;
    }
    getTopUsersByMessages(limit = 10) {
        const users = Object.values(this.userCache)
            .map(userData => ({
            username: userData.user.username,
            messages: userData.messageCount,
        }))
            .sort((a, b) => b.messages - a.messages)
            .slice(0, limit);
        return users;
    }
    clearInactiveUsers() {
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        let clearedCount = 0;
        for (const [username, userData] of Object.entries(this.userCache)) {
            if (userData.lastSeen < oneDayAgo) {
                delete this.userCache[username];
                clearedCount++;
            }
        }
        if (clearedCount > 0) {
            logger_1.logger.info(`Cleared ${clearedCount} inactive users from cache`);
        }
        return clearedCount;
    }
    getCacheStats() {
        const totalUsers = Object.keys(this.userCache).length;
        const activeUsers = this.getActiveUsers().length;
        return {
            totalUsers,
            activeUsers,
            moderators: this.moderators.size,
            vips: this.vips.size,
            subscribers: this.subscribers.size,
            followers: this.followers.size,
            bannedUsers: this.bannedUsers.size,
        };
    }
    exportUserData() {
        return { ...this.userCache };
    }
    importUserData(data) {
        this.userCache = { ...data };
        for (const userData of Object.values(this.userCache)) {
            this.updateUserPermissions(userData.user);
        }
        logger_1.logger.info(`Imported ${Object.keys(data).length} users from backup`);
    }
}
exports.UserManager = UserManager;
//# sourceMappingURL=UserManager.js.map