export interface AuthCredentials {
    email?: string;
    password?: string;
    token?: string;
}
export interface AuthResult {
    success: boolean;
    userId?: number;
    username?: string;
    error?: string;
}
export declare class AuthManager {
    private isAuthenticated;
    private currentUserId?;
    private currentUsername?;
    private authToken?;
    authenticate(credentials: AuthCredentials): Promise<AuthResult>;
    private authenticateWithToken;
    private authenticateWithPassword;
    logout(): void;
    get authenticated(): boolean;
    get userId(): number | undefined;
    get username(): string | undefined;
    get token(): string | undefined;
    refreshAuth(): Promise<boolean>;
    validateAuth(): Promise<boolean>;
}
export declare const authManager: AuthManager;
//# sourceMappingURL=AuthManager.d.ts.map