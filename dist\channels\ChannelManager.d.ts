import { EventEmitter } from 'events';
import { ChatroomSettings } from '../api/kickApi';
export interface ChannelInfo {
    id: number;
    slug: string;
    name: string;
    isLive: boolean;
    viewerCount: number;
    followerCount: number;
    subscriberCount: number;
    chatroomId: number;
    settings: ChatroomSettings | null;
    lastUpdated: Date;
}
export declare class ChannelManager extends EventEmitter {
    private currentChannel?;
    private channelCache;
    private updateInterval?;
    constructor();
    joinChannel(channelSlug: string): Promise<boolean>;
    leaveChannel(): void;
    getCurrentChannel(): ChannelInfo | undefined;
    updateChannelInfo(): Promise<void>;
    private startChannelUpdates;
    private stopChannelUpdates;
    updateChatroomSettings(settings: Partial<ChatroomSettings>): Promise<boolean>;
    enableSlowMode(interval?: number): Promise<boolean>;
    disableSlowMode(): Promise<boolean>;
    enableFollowersOnly(minDuration?: number): Promise<boolean>;
    disableFollowersOnly(): Promise<boolean>;
    enableSubscribersOnly(): Promise<boolean>;
    disableSubscribersOnly(): Promise<boolean>;
    enableEmotesMode(): Promise<boolean>;
    disableEmotesMode(): Promise<boolean>;
    getCachedChannel(channelSlug: string): ChannelInfo | undefined;
    clearCache(): void;
    getCacheStats(): {
        cachedChannels: number;
        currentChannel: string | null;
        isUpdating: boolean;
    };
    get inChannel(): boolean;
    get currentChatroomId(): number | undefined;
    get currentChannelSlug(): string | undefined;
    get currentChatroomSettings(): ChatroomSettings | null | undefined;
    get isSlowModeEnabled(): boolean;
    get isFollowersOnlyEnabled(): boolean;
    get isSubscribersOnlyEnabled(): boolean;
    get isEmotesModeEnabled(): boolean;
    cleanup(): void;
}
//# sourceMappingURL=ChannelManager.d.ts.map