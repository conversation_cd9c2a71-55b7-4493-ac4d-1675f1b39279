{"version": 3, "file": "ChannelManager.d.ts", "sourceRoot": "", "sources": ["../../src/channels/ChannelManager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,EAAwB,gBAAgB,EAAE,MAAM,gBAAgB,CAAC;AAGxE,MAAM,WAAW,WAAW;IAC1B,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,OAAO,CAAC;IAChB,WAAW,EAAE,MAAM,CAAC;IACpB,aAAa,EAAE,MAAM,CAAC;IACtB,eAAe,EAAE,MAAM,CAAC;IACxB,UAAU,EAAE,MAAM,CAAC;IACnB,QAAQ,EAAE,gBAAgB,GAAG,IAAI,CAAC;IAClC,WAAW,EAAE,IAAI,CAAC;CACnB;AAED,qBAAa,cAAe,SAAQ,YAAY;IAC9C,OAAO,CAAC,cAAc,CAAC,CAAc;IACrC,OAAO,CAAC,YAAY,CAAkC;IACtD,OAAO,CAAC,cAAc,CAAC,CAAiB;;IASlC,WAAW,CAAC,WAAW,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IA6CxD,YAAY,IAAI,IAAI;IAgBpB,iBAAiB,IAAI,WAAW,GAAG,SAAS;IAOtC,iBAAiB,IAAI,OAAO,CAAC,IAAI,CAAC;IA2BxC,OAAO,CAAC,mBAAmB;IAY3B,OAAO,CAAC,kBAAkB;IAUpB,sBAAsB,CAAC,QAAQ,EAAE,OAAO,CAAC,gBAAgB,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC;IA0B7E,cAAc,CAAC,QAAQ,GAAE,MAAW,GAAG,OAAO,CAAC,OAAO,CAAC;IAUvD,eAAe,IAAI,OAAO,CAAC,OAAO,CAAC;IAUnC,mBAAmB,CAAC,WAAW,GAAE,MAAU,GAAG,OAAO,CAAC,OAAO,CAAC;IAU9D,oBAAoB,IAAI,OAAO,CAAC,OAAO,CAAC;IAUxC,qBAAqB,IAAI,OAAO,CAAC,OAAO,CAAC;IASzC,sBAAsB,IAAI,OAAO,CAAC,OAAO,CAAC;IAS1C,gBAAgB,IAAI,OAAO,CAAC,OAAO,CAAC;IASpC,iBAAiB,IAAI,OAAO,CAAC,OAAO,CAAC;IAS3C,gBAAgB,CAAC,WAAW,EAAE,MAAM,GAAG,WAAW,GAAG,SAAS;IAO9D,UAAU,IAAI,IAAI;IAQlB,aAAa;;;;;IAWb,IAAI,SAAS,IAAI,OAAO,CAEvB;IAKD,IAAI,iBAAiB,IAAI,MAAM,GAAG,SAAS,CAE1C;IAKD,IAAI,kBAAkB,IAAI,MAAM,GAAG,SAAS,CAE3C;IAKD,IAAI,uBAAuB,IAAI,gBAAgB,GAAG,IAAI,GAAG,SAAS,CAEjE;IAKD,IAAI,iBAAiB,IAAI,OAAO,CAE/B;IAKD,IAAI,sBAAsB,IAAI,OAAO,CAEpC;IAKD,IAAI,wBAAwB,IAAI,OAAO,CAEtC;IAKD,IAAI,mBAAmB,IAAI,OAAO,CAEjC;IAKD,OAAO,IAAI,IAAI;CAKhB"}