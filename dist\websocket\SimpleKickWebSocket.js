"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SimpleKickWebSocket = void 0;
const ws_1 = __importDefault(require("ws"));
const events_1 = require("events");
const logger_1 = require("../utils/logger");
class SimpleKickWebSocket extends events_1.EventEmitter {
    constructor() {
        super();
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 5000;
        this.isConnecting = false;
        this.isConnected = false;
    }
    async connect(chatroomId) {
        if (this.isConnecting || this.isConnected) {
            return true;
        }
        this.isConnecting = true;
        this.chatroomId = chatroomId;
        try {
            logger_1.logger.info(`Connecting to Kick WebSocket for chatroom ${chatroomId}...`);
            const wsUrl = `wss://ws-us2.pusher.com/app/32cbd69e4b950bf97679?protocol=7&client=js&version=7.4.0&flash=false`;
            this.ws = new ws_1.default(wsUrl);
            this.ws.on('open', () => {
                logger_1.logger.info('WebSocket connected to Kick');
                this.isConnected = true;
                this.isConnecting = false;
                this.reconnectAttempts = 0;
                this.subscribeToChatroom();
                this.startPingInterval();
                this.emit('connected');
            });
            this.ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data.toString());
                    this.handleMessage(message);
                }
                catch (error) {
                    logger_1.logger.error('Failed to parse WebSocket message:', error);
                }
            });
            this.ws.on('close', (code, reason) => {
                logger_1.logger.warn(`WebSocket closed: ${code} - ${reason}`);
                this.isConnected = false;
                this.isConnecting = false;
                this.stopPingInterval();
                this.emit('disconnected', { code, reason });
                if (this.reconnectAttempts < this.maxReconnectAttempts) {
                    this.scheduleReconnect();
                }
                else {
                    logger_1.logger.error('Max reconnection attempts reached');
                    this.emit('error', new Error('Max reconnection attempts reached'));
                }
            });
            this.ws.on('error', (error) => {
                logger_1.logger.error('WebSocket error:', error);
                this.isConnected = false;
                this.isConnecting = false;
                this.emit('error', error);
            });
            return true;
        }
        catch (error) {
            logger_1.logger.error('Failed to connect to WebSocket:', error);
            this.isConnecting = false;
            return false;
        }
    }
    subscribeToChatroom() {
        if (!this.ws || !this.chatroomId)
            return;
        const subscribeMessage = {
            event: 'pusher:subscribe',
            data: {
                auth: '',
                channel: `chatrooms.${this.chatroomId}.v2`,
            },
        };
        logger_1.logger.debug(`Subscribing to chatroom ${this.chatroomId}`);
        this.ws.send(JSON.stringify(subscribeMessage));
    }
    handleMessage(message) {
        try {
            switch (message.event) {
                case 'pusher:connection_established':
                    logger_1.logger.info('Pusher connection established');
                    break;
                case 'pusher:subscription_succeeded':
                    logger_1.logger.info('Successfully subscribed to chatroom');
                    this.emit('subscribed');
                    break;
                case 'App\\Events\\ChatMessageEvent':
                    this.handleChatMessage(message);
                    break;
                case 'App\\Events\\UserBannedEvent':
                    this.emit('user_banned', message.data);
                    break;
                case 'App\\Events\\UserUnbannedEvent':
                    this.emit('user_unbanned', message.data);
                    break;
                case 'App\\Events\\FollowersUpdated':
                    this.emit('new_follower', message.data);
                    break;
                case 'pusher:pong':
                    break;
                default:
                    logger_1.logger.debug('Unhandled WebSocket event:', message.event);
            }
        }
        catch (error) {
            logger_1.logger.error('Error handling WebSocket message:', error);
        }
    }
    handleChatMessage(message) {
        try {
            const chatMessage = {
                id: message.data.id,
                type: message.event,
                data: message.data,
            };
            logger_1.logger.debug(`Chat message from ${chatMessage.data.sender.username}: ${chatMessage.data.content}`);
            this.emit('message', chatMessage);
        }
        catch (error) {
            logger_1.logger.error('Error processing chat message:', error);
        }
    }
    startPingInterval() {
        this.pingInterval = setInterval(() => {
            if (this.ws && this.isConnected) {
                this.ws.send(JSON.stringify({ event: 'pusher:ping', data: {} }));
            }
        }, 30000);
    }
    stopPingInterval() {
        if (this.pingInterval) {
            clearInterval(this.pingInterval);
            this.pingInterval = undefined;
        }
    }
    scheduleReconnect() {
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * this.reconnectAttempts;
        logger_1.logger.info(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);
        setTimeout(() => {
            if (this.chatroomId) {
                this.connect(this.chatroomId);
            }
        }, delay);
    }
    async sendMessage(content) {
        logger_1.logger.info(`Would send message: ${content}`);
        return true;
    }
    disconnect() {
        logger_1.logger.info('Disconnecting from WebSocket...');
        this.stopPingInterval();
        if (this.ws) {
            this.ws.close();
            this.ws = undefined;
        }
        this.isConnected = false;
        this.isConnecting = false;
        this.reconnectAttempts = 0;
    }
    get connected() {
        return this.isConnected;
    }
    get currentChatroomId() {
        return this.chatroomId;
    }
}
exports.SimpleKickWebSocket = SimpleKickWebSocket;
//# sourceMappingURL=SimpleKickWebSocket.js.map