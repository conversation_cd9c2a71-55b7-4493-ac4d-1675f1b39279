import { EventEmitter } from 'events';
export interface SimpleKickMessage {
    id: string;
    type: string;
    data: {
        id: string;
        chatroom_id: number;
        content: string;
        type: string;
        created_at: string;
        sender: {
            id: number;
            username: string;
            slug: string;
            identity: {
                color: string;
                badges: any[];
            };
        };
    };
}
export declare class SimpleKickWebSocket extends EventEmitter {
    private ws?;
    private chatroomId?;
    private reconnectAttempts;
    private maxReconnectAttempts;
    private reconnectDelay;
    private isConnecting;
    private isConnected;
    private pingInterval?;
    constructor();
    connect(chatroomId: number): Promise<boolean>;
    private subscribeToChatroom;
    private handleMessage;
    private handleChatMessage;
    private startPingInterval;
    private stopPingInterval;
    private scheduleReconnect;
    sendMessage(content: string): Promise<boolean>;
    disconnect(): void;
    get connected(): boolean;
    get currentChatroomId(): number | undefined;
}
//# sourceMappingURL=SimpleKickWebSocket.d.ts.map