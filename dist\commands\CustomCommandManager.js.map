{"version": 3, "file": "CustomCommandManager.js", "sourceRoot": "", "sources": ["../../src/commands/CustomCommandManager.ts"], "names": [], "mappings": ";;;AAAA,mCAAsC;AACtC,oCAAiE;AAEjE,4CAAyC;AAkBzC,MAAa,oBAAqB,SAAQ,qBAAY;IAKpD,YAAY,cAA8B;QACxC,KAAK,EAAE,CAAC;QALF,mBAAc,GAAG,IAAI,GAAG,EAAyB,CAAC;QAClD,cAAS,GAAG,IAAI,GAAG,EAA2B,CAAC;QAKrD,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC/B,CAAC;IAKO,qBAAqB;QAC3B,IAAI,CAAC,gBAAgB,CAAC;YACpB,IAAI,EAAE,MAAM;YACZ,WAAW,EAAE,iDAAiD;YAC9D,OAAO,EAAE,QAAQ;YACjB,QAAQ,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ;SAC7C,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC;YACpB,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,0BAA0B;YACvC,OAAO,EAAE,WAAW;YACpB,QAAQ,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,IAAI,SAAS;SACrE,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC;YACpB,IAAI,EAAE,QAAQ;YACd,WAAW,EAAE,YAAY;YACzB,OAAO,EAAE,UAAU;YACnB,QAAQ,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,eAAe;SAC9D,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC;YACpB,IAAI,EAAE,OAAO;YACb,WAAW,EAAE,4CAA4C;YACzD,OAAO,EAAE,SAAS;YAClB,QAAQ,EAAE,CAAC,OAAO,EAAE,EAAE;gBACpB,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC9D,OAAO,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;YACzD,CAAC;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC;YACpB,IAAI,EAAE,QAAQ;YACd,WAAW,EAAE,6BAA6B;YAC1C,OAAO,EAAE,UAAU;YACnB,QAAQ,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE;SAC/D,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC;YACpB,IAAI,EAAE,MAAM;YACZ,WAAW,EAAE,qCAAqC;YAClD,OAAO,EAAE,QAAQ;YACjB,QAAQ,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;SAC9C,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC;YACpB,IAAI,EAAE,MAAM;YACZ,WAAW,EAAE,sCAAsC;YACnD,OAAO,EAAE,QAAQ;YACjB,QAAQ,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;SAC7C,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC;YACpB,IAAI,EAAE,MAAM;YACZ,WAAW,EAAE,uCAAuC;YACpD,OAAO,EAAE,QAAQ;YACjB,QAAQ,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;SAC7C,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC;YACpB,IAAI,EAAE,MAAM;YACZ,WAAW,EAAE,sCAAsC;YACnD,OAAO,EAAE,QAAQ;YACjB,QAAQ,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;SAC7C,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC;YACpB,IAAI,EAAE,MAAM;YACZ,WAAW,EAAE,cAAc;YAC3B,OAAO,EAAE,QAAQ;YACjB,QAAQ,EAAE,GAAG,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,kBAAkB,EAAE;SAChD,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC;YACpB,IAAI,EAAE,MAAM;YACZ,WAAW,EAAE,cAAc;YAC3B,OAAO,EAAE,QAAQ;YACjB,QAAQ,EAAE,GAAG,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,kBAAkB,EAAE;SAChD,CAAC,CAAC;IACL,CAAC;IAKD,gBAAgB,CAAC,QAAyB;QACxC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC5C,eAAM,CAAC,KAAK,CAAC,gCAAgC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;IAChE,CAAC;IAKD,KAAK,CAAC,aAAa,CACjB,IAAY,EACZ,QAAgB,EAChB,OAOC;QAED,IAAI,CAAC;YAEH,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC1E,eAAM,CAAC,IAAI,CAAC,WAAW,IAAI,iBAAiB,CAAC,CAAC;gBAC9C,OAAO,KAAK,CAAC;YACf,CAAC;YAGD,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;gBACnC,eAAM,CAAC,IAAI,CAAC,yBAAyB,IAAI,EAAE,CAAC,CAAC;gBAC7C,OAAO,KAAK,CAAC;YACf,CAAC;YAGD,MAAM,aAAa,GAAkB;gBACnC,EAAE,EAAE,IAAI,CAAC,iBAAiB,EAAE;gBAC5B,IAAI;gBACJ,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,mBAAmB,IAAI,EAAE;gBAC7D,KAAK,EAAE,IAAI,IAAI,EAAE;gBACjB,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,EAAE;gBAC9B,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,CAAC;gBAC/B,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,uBAAe,CAAC,QAAQ;gBAC1D,OAAO,EAAE,OAAO,CAAC,OAAO,KAAK,KAAK;gBAClC,QAAQ;gBACR,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC;gBAC1C,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,CAAC;gBACX,UAAU,EAAE,OAAO,CAAC,SAAS;gBAC7B,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACpC,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACrC,CAAC;YAGF,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;YAG7C,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;gBACnE,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;YAC1D,CAAC,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,2BAA2B,IAAI,EAAE,CAAC,CAAC;YAC/C,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC;YAE5C,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,aAAa,CACjB,IAAY,EACZ,OAOC;QAED,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACpD,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,eAAM,CAAC,IAAI,CAAC,kBAAkB,IAAI,YAAY,CAAC,CAAC;gBAChD,OAAO,KAAK,CAAC;YACf,CAAC;YAGD,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;gBACnC,aAAa,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;gBAC1C,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACpE,CAAC;YACD,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS;gBAAE,aAAa,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;YACvF,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS;gBAAE,aAAa,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;YAC3E,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS;gBAAE,aAAa,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;YAC9E,IAAI,OAAO,CAAC,UAAU,KAAK,SAAS;gBAAE,aAAa,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;YACpF,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS;gBAAE,aAAa,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;YAE3E,aAAa,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;YAGpD,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;YAEvD,eAAM,CAAC,IAAI,CAAC,2BAA2B,IAAI,EAAE,CAAC,CAAC;YAC/C,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC;YAE5C,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,aAAa,CAAC,IAAY;QAC9B,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACpD,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,eAAM,CAAC,IAAI,CAAC,kBAAkB,IAAI,YAAY,CAAC,CAAC;gBAChD,OAAO,KAAK,CAAC;YACf,CAAC;YAGD,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAGjC,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE5C,eAAM,CAAC,IAAI,CAAC,2BAA2B,IAAI,EAAE,CAAC,CAAC;YAC/C,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC;YAE5C,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,oBAAoB,CAAC,OAAuB,EAAE,aAA4B;QACtF,IAAI,CAAC;YAEH,aAAa,CAAC,QAAQ,EAAE,CAAC;YACzB,aAAa,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;YAGlD,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAGvF,MAAM,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;YAErE,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBAC5B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,QAAQ;gBAC3B,QAAQ,EAAE,iBAAiB;aAC5B,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,aAAa,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YAC7E,MAAM,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,EAAE,gDAAgD,CAAC,CAAC;QACtG,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,gBAAgB,CAAC,QAAgB,EAAE,OAAuB;QACtE,IAAI,iBAAiB,GAAG,QAAQ,CAAC;QAGjC,MAAM,eAAe,GAAG,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QACvD,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,OAAO,iBAAiB,CAAC;QAC3B,CAAC;QAGD,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE,CAAC;YACpC,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YAElD,IAAI,QAAQ,EAAE,CAAC;gBACb,IAAI,CAAC;oBACH,MAAM,KAAK,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;oBAC/C,iBAAiB,GAAG,iBAAiB,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBAC9D,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,YAAY,GAAG,EAAE,KAAK,CAAC,CAAC;oBACjE,iBAAiB,GAAG,iBAAiB,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,YAAY,GAAG,CAAC,CAAC;gBAC5E,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAKO,gBAAgB,CAAC,QAAgB;QACvC,MAAM,eAAe,GAAG,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QACvD,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,OAAO,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1D,CAAC;IAKO,kBAAkB,CAAC,IAAY;QAErC,OAAO,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;IAC/E,CAAC;IAKO,iBAAiB;QACvB,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACxE,CAAC;IAKD,gBAAgB,CAAC,IAAY;QAC3B,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAKD,oBAAoB;QAClB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;IAClD,CAAC;IAKD,6BAA6B,CAAC,eAAgC;QAC5D,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,IAAI,eAAe,CAAC,CAAC;IACtF,CAAC;IAKD,qBAAqB;QACnB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;IAC7C,CAAC;IAKD,eAAe;QACb,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC7C,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QACvE,MAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAE9E,OAAO;YACL,aAAa,EAAE,QAAQ,CAAC,MAAM;YAC9B,eAAe,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,MAAM;YAC3D,gBAAgB,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,MAAM;YAC7D,SAAS;YACT,WAAW,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9E,gBAAgB,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBACrC,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,IAAI,EAAE,GAAG,CAAC,QAAQ;gBAClB,QAAQ,EAAE,GAAG,CAAC,QAAQ;aACvB,CAAC,CAAC;YACH,kBAAkB,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;SACxC,CAAC;IACJ,CAAC;IAKD,cAAc;QACZ,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC;IACrC,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,QAAyB;QAC5C,IAAI,aAAa,GAAG,CAAC,CAAC;QAEtB,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,IAAI,CAAC;gBAEH,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC1C,eAAM,CAAC,IAAI,CAAC,wCAAwC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;oBACpE,SAAS;gBACX,CAAC;gBAGD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBAG/C,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;oBAC7D,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;gBACpD,CAAC,CAAC,CAAC;gBAEH,aAAa,EAAE,CAAC;YAClB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,OAAO,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YACnE,CAAC;QACH,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,YAAY,aAAa,kBAAkB,CAAC,CAAC;QACzD,OAAO,aAAa,CAAC;IACvB,CAAC;CACF;AAhaD,oDAgaC"}