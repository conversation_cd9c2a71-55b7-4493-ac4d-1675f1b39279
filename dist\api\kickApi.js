"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.kickAPI = void 0;
const axios_1 = __importDefault(require("axios"));
const config_1 = require("../config");
const logger_1 = require("../utils/logger");
class KickAPI {
    constructor() {
        this.api = axios_1.default.create({
            baseURL: config_1.kickConfig.apiBaseUrl,
            timeout: 10000,
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'User-Agent': 'KickBot/1.0.0',
            },
        });
        this.api.interceptors.request.use((config) => {
            logger_1.logger.debug(`API Request: ${config.method?.toUpperCase()} ${config.url}`, {
                params: config.params,
                data: config.data,
            });
            return config;
        }, (error) => {
            logger_1.logger.error('API Request Error:', error);
            return Promise.reject(error);
        });
        this.api.interceptors.response.use((response) => {
            logger_1.logger.debug(`API Response: ${response.status} ${response.config.url}`, {
                data: response.data,
            });
            return response;
        }, (error) => {
            logger_1.logger.error('API Response Error:', {
                status: error.response?.status,
                statusText: error.response?.statusText,
                data: error.response?.data,
                url: error.config?.url,
            });
            return Promise.reject(error);
        });
    }
    async authenticate(email, password) {
        try {
            const csrfResponse = await this.api.get('/sanctum/csrf-cookie');
            const cookies = csrfResponse.headers['set-cookie'];
            let csrfToken = '';
            if (cookies) {
                for (const cookie of cookies) {
                    if (cookie.includes('XSRF-TOKEN=')) {
                        csrfToken = cookie.split('XSRF-TOKEN=')[1]?.split(';')[0] || '';
                        break;
                    }
                }
            }
            const loginResponse = await this.api.post('/login', {
                email,
                password,
                remember: true,
            }, {
                headers: {
                    'X-XSRF-TOKEN': decodeURIComponent(csrfToken),
                    'Cookie': cookies?.join('; ') || '',
                },
            });
            if (loginResponse.status === 200) {
                this.authToken = loginResponse.data.token || 'authenticated';
                this.userId = loginResponse.data.user?.id;
                this.api.defaults.headers.common['Authorization'] = `Bearer ${this.authToken}`;
                logger_1.logger.info('Successfully authenticated with Kick API');
                return true;
            }
            return false;
        }
        catch (error) {
            logger_1.logger.error('Authentication failed:', error.response?.data || error.message);
            return false;
        }
    }
    async getChannel(channelSlug) {
        try {
            const response = await this.api.get(`/channels/${channelSlug}`);
            return response.data;
        }
        catch (error) {
            logger_1.logger.error(`Failed to get channel ${channelSlug}:`, error.response?.data || error.message);
            return null;
        }
    }
    async getUser(username) {
        try {
            const response = await this.api.get(`/users/${username}`);
            return response.data;
        }
        catch (error) {
            logger_1.logger.error(`Failed to get user ${username}:`, error.response?.data || error.message);
            return null;
        }
    }
    async sendMessage(chatroomId, message) {
        try {
            const response = await this.api.post(`/chatrooms/${chatroomId}/messages`, {
                content: message,
                type: 'message',
            });
            return response.status === 200;
        }
        catch (error) {
            logger_1.logger.error('Failed to send message:', error.response?.data || error.message);
            return false;
        }
    }
    async banUser(chatroomId, username, permanent = false) {
        try {
            const response = await this.api.post(`/chatrooms/${chatroomId}/bans`, {
                banned_username: username,
                permanent,
            });
            return response.status === 200;
        }
        catch (error) {
            logger_1.logger.error(`Failed to ban user ${username}:`, error.response?.data || error.message);
            return false;
        }
    }
    async timeoutUser(chatroomId, username, duration) {
        try {
            const response = await this.api.post(`/chatrooms/${chatroomId}/timeouts`, {
                banned_username: username,
                duration,
            });
            return response.status === 200;
        }
        catch (error) {
            logger_1.logger.error(`Failed to timeout user ${username}:`, error.response?.data || error.message);
            return false;
        }
    }
    async unbanUser(chatroomId, username) {
        try {
            const response = await this.api.delete(`/chatrooms/${chatroomId}/bans/${username}`);
            return response.status === 200;
        }
        catch (error) {
            logger_1.logger.error(`Failed to unban user ${username}:`, error.response?.data || error.message);
            return false;
        }
    }
    async getChatroomSettings(chatroomId) {
        try {
            const response = await this.api.get(`/chatrooms/${chatroomId}/settings`);
            return response.data;
        }
        catch (error) {
            logger_1.logger.error(`Failed to get chatroom settings:`, error.response?.data || error.message);
            return null;
        }
    }
    async updateChatroomSettings(chatroomId, settings) {
        try {
            const response = await this.api.put(`/chatrooms/${chatroomId}/settings`, settings);
            return response.status === 200;
        }
        catch (error) {
            logger_1.logger.error('Failed to update chatroom settings:', error.response?.data || error.message);
            return false;
        }
    }
    get currentUserId() {
        return this.userId;
    }
    get isAuthenticated() {
        return !!this.authToken;
    }
}
exports.kickAPI = new KickAPI();
//# sourceMappingURL=kickApi.js.map