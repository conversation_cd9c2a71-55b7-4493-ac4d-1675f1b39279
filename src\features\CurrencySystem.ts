import { EventEmitter } from 'events';
import { database } from '../database/Database';
import { logger } from '../utils/logger';
import { KickBot } from '../bot/KickBot';

export interface CurrencyConfig {
  enabled: boolean;
  currencyName: string;
  currencySymbol: string;
  passiveEarning: {
    enabled: boolean;
    amount: number;
    interval: number; // minutes
    requireActive: boolean;
  };
  messageRewards: {
    enabled: boolean;
    amount: number;
    cooldown: number; // seconds
  };
  followRewards: {
    enabled: boolean;
    amount: number;
  };
  subscribeRewards: {
    enabled: boolean;
    amount: number;
  };
  dailyBonus: {
    enabled: boolean;
    amount: number;
    streakMultiplier: number;
  };
}

export interface Transaction {
  id: string;
  username: string;
  amount: number;
  type: 'earn' | 'spend' | 'transfer' | 'bonus' | 'penalty';
  reason: string;
  timestamp: Date;
}

export class CurrencySystem extends EventEmitter {
  private config: CurrencyConfig;
  private bot: KickBot;
  private passiveEarningInterval?: NodeJS.Timeout;
  private messageRewardCooldowns = new Map<string, number>();
  private dailyBonusClaimed = new Map<string, string>(); // username -> date

  constructor(bot: KickBot, config: CurrencyConfig) {
    super();
    this.bot = bot;
    this.config = config;
    this.setupEventHandlers();
    
    if (config.enabled) {
      this.startPassiveEarning();
    }
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    this.bot.on('message', (message) => {
      if (this.config.messageRewards.enabled) {
        this.handleMessageReward(message.sender.username);
      }
    });

    this.bot.on('new_follower', (data) => {
      if (this.config.followRewards.enabled) {
        this.addPoints(data.username, this.config.followRewards.amount, 'New follower bonus');
      }
    });

    // Note: Subscribe events would need to be implemented in the WebSocket handler
  }

  /**
   * Start passive earning system
   */
  private startPassiveEarning(): void {
    if (!this.config.passiveEarning.enabled) return;

    const intervalMs = this.config.passiveEarning.interval * 60 * 1000;
    
    this.passiveEarningInterval = setInterval(async () => {
      await this.distributePassiveEarnings();
    }, intervalMs);

    logger.info(`Started passive earning: ${this.config.passiveEarning.amount} ${this.config.currencyName} every ${this.config.passiveEarning.interval} minutes`);
  }

  /**
   * Distribute passive earnings to active users
   */
  private async distributePassiveEarnings(): Promise<void> {
    try {
      // Get active users (this would need to be implemented in UserManager)
      // For now, we'll give to all users who have been active recently
      const activeUsers = []; // This should come from UserManager.getActiveUsers()
      
      for (const username of activeUsers) {
        await this.addPoints(
          username,
          this.config.passiveEarning.amount,
          'Passive earning'
        );
      }

      if (activeUsers.length > 0) {
        logger.debug(`Distributed ${this.config.passiveEarning.amount} ${this.config.currencyName} to ${activeUsers.length} active users`);
      }
    } catch (error) {
      logger.error('Failed to distribute passive earnings:', error);
    }
  }

  /**
   * Handle message reward
   */
  private async handleMessageReward(username: string): Promise<void> {
    const now = Date.now();
    const lastReward = this.messageRewardCooldowns.get(username) || 0;
    const cooldownMs = this.config.messageRewards.cooldown * 1000;

    if (now - lastReward >= cooldownMs) {
      await this.addPoints(username, this.config.messageRewards.amount, 'Message reward');
      this.messageRewardCooldowns.set(username, now);
    }
  }

  /**
   * Add points to a user
   */
  async addPoints(username: string, amount: number, reason: string): Promise<boolean> {
    try {
      // Get or create user
      let user = await database.getUser(username);
      if (!user) {
        await database.createUser({ username, display_name: username });
        user = await database.getUser(username);
      }

      if (!user) {
        logger.error(`Failed to get/create user: ${username}`);
        return false;
      }

      // Update points
      const newPoints = user.points + amount;
      await database.updateUser(username, { points: newPoints });

      // Log transaction
      const transaction: Transaction = {
        id: this.generateTransactionId(),
        username,
        amount,
        type: amount > 0 ? 'earn' : 'spend',
        reason,
        timestamp: new Date(),
      };

      this.emit('points_added', { username, amount, newTotal: newPoints, reason });
      this.emit('transaction', transaction);

      logger.debug(`Added ${amount} ${this.config.currencyName} to ${username} (${reason}). New total: ${newPoints}`);
      return true;
    } catch (error) {
      logger.error(`Failed to add points to ${username}:`, error);
      return false;
    }
  }

  /**
   * Remove points from a user
   */
  async removePoints(username: string, amount: number, reason: string): Promise<boolean> {
    try {
      const user = await database.getUser(username);
      if (!user) {
        logger.warn(`User not found: ${username}`);
        return false;
      }

      if (user.points < amount) {
        logger.warn(`Insufficient points for ${username}: has ${user.points}, needs ${amount}`);
        return false;
      }

      const newPoints = user.points - amount;
      await database.updateUser(username, { points: newPoints });

      const transaction: Transaction = {
        id: this.generateTransactionId(),
        username,
        amount: -amount,
        type: 'spend',
        reason,
        timestamp: new Date(),
      };

      this.emit('points_removed', { username, amount, newTotal: newPoints, reason });
      this.emit('transaction', transaction);

      logger.debug(`Removed ${amount} ${this.config.currencyName} from ${username} (${reason}). New total: ${newPoints}`);
      return true;
    } catch (error) {
      logger.error(`Failed to remove points from ${username}:`, error);
      return false;
    }
  }

  /**
   * Transfer points between users
   */
  async transferPoints(fromUsername: string, toUsername: string, amount: number): Promise<boolean> {
    try {
      const fromUser = await database.getUser(fromUsername);
      if (!fromUser || fromUser.points < amount) {
        return false;
      }

      // Remove from sender
      const removeSuccess = await this.removePoints(fromUsername, amount, `Transfer to ${toUsername}`);
      if (!removeSuccess) return false;

      // Add to receiver
      const addSuccess = await this.addPoints(toUsername, amount, `Transfer from ${fromUsername}`);
      if (!addSuccess) {
        // Rollback - add points back to sender
        await this.addPoints(fromUsername, amount, 'Transfer rollback');
        return false;
      }

      this.emit('points_transferred', { from: fromUsername, to: toUsername, amount });
      return true;
    } catch (error) {
      logger.error(`Failed to transfer points from ${fromUsername} to ${toUsername}:`, error);
      return false;
    }
  }

  /**
   * Get user points
   */
  async getUserPoints(username: string): Promise<number> {
    try {
      const user = await database.getUser(username);
      return user ? user.points : 0;
    } catch (error) {
      logger.error(`Failed to get points for ${username}:`, error);
      return 0;
    }
  }

  /**
   * Get leaderboard
   */
  async getLeaderboard(limit: number = 10): Promise<Array<{ username: string; points: number; rank: number }>> {
    try {
      const users = await database.db.all(`
        SELECT username, points 
        FROM users 
        WHERE points > 0 
        ORDER BY points DESC 
        LIMIT ?
      `, [limit]);

      return users.map((user: any, index: number) => ({
        username: user.username,
        points: user.points,
        rank: index + 1,
      }));
    } catch (error) {
      logger.error('Failed to get leaderboard:', error);
      return [];
    }
  }

  /**
   * Claim daily bonus
   */
  async claimDailyBonus(username: string): Promise<{ success: boolean; amount?: number; streak?: number; message: string }> {
    if (!this.config.dailyBonus.enabled) {
      return { success: false, message: 'Daily bonus is disabled' };
    }

    const today = new Date().toDateString();
    const lastClaimed = this.dailyBonusClaimed.get(username);

    if (lastClaimed === today) {
      return { success: false, message: 'Daily bonus already claimed today' };
    }

    // Calculate streak (simplified - would need database storage for proper streaks)
    const streak = 1; // This should be calculated based on consecutive days
    const bonusAmount = this.config.dailyBonus.amount * (1 + (streak - 1) * this.config.dailyBonus.streakMultiplier);

    const success = await this.addPoints(username, bonusAmount, `Daily bonus (streak: ${streak})`);
    
    if (success) {
      this.dailyBonusClaimed.set(username, today);
      return {
        success: true,
        amount: bonusAmount,
        streak,
        message: `Daily bonus claimed! +${bonusAmount} ${this.config.currencyName} (${streak} day streak)`
      };
    }

    return { success: false, message: 'Failed to claim daily bonus' };
  }

  /**
   * Generate transaction ID
   */
  private generateTransactionId(): string {
    return `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<CurrencyConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    if (this.config.enabled && this.config.passiveEarning.enabled && !this.passiveEarningInterval) {
      this.startPassiveEarning();
    } else if (!this.config.enabled && this.passiveEarningInterval) {
      clearInterval(this.passiveEarningInterval);
      this.passiveEarningInterval = undefined;
    }

    this.emit('config_updated', this.config);
  }

  /**
   * Get currency statistics
   */
  async getStats(): Promise<any> {
    try {
      const totalUsers = await database.db.get('SELECT COUNT(*) as count FROM users WHERE points > 0');
      const totalPoints = await database.db.get('SELECT SUM(points) as total FROM users');
      const avgPoints = await database.db.get('SELECT AVG(points) as avg FROM users WHERE points > 0');
      const topUser = await database.db.get('SELECT username, points FROM users ORDER BY points DESC LIMIT 1');

      return {
        totalUsers: totalUsers.count,
        totalPointsInCirculation: totalPoints.total || 0,
        averagePoints: Math.round(avgPoints.avg || 0),
        topUser: topUser ? { username: topUser.username, points: topUser.points } : null,
        config: this.config,
      };
    } catch (error) {
      logger.error('Failed to get currency stats:', error);
      return {
        totalUsers: 0,
        totalPointsInCirculation: 0,
        averagePoints: 0,
        topUser: null,
        config: this.config,
      };
    }
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    if (this.passiveEarningInterval) {
      clearInterval(this.passiveEarningInterval);
      this.passiveEarningInterval = undefined;
    }
  }
}
