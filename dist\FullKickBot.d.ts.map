{"version": 3, "file": "FullKickBot.d.ts", "sourceRoot": "", "sources": ["../src/FullKickBot.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AACxC,OAAO,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAC3D,OAAO,EAAE,oBAAoB,EAAE,MAAM,iCAAiC,CAAC;AACvE,OAAO,EAAE,iBAAiB,EAAE,MAAM,gCAAgC,CAAC;AACnE,OAAO,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAC3D,OAAO,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAC3D,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAClD,OAAO,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAS3D,MAAM,WAAW,iBAAiB;IAEhC,GAAG,EAAE;QACH,QAAQ,EAAE,MAAM,CAAC;QACjB,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,OAAO,EAAE,MAAM,CAAC;QAChB,MAAM,EAAE,MAAM,CAAC;KAChB,CAAC;IAGF,QAAQ,EAAE;QACR,UAAU,EAAE,OAAO,CAAC;QACpB,cAAc,EAAE,OAAO,CAAC;QACxB,QAAQ,EAAE,OAAO,CAAC;QAClB,SAAS,EAAE,OAAO,CAAC;QACnB,SAAS,EAAE,OAAO,CAAC;QACnB,YAAY,EAAE,OAAO,CAAC;KACvB,CAAC;IAGF,QAAQ,EAAE;QACR,OAAO,EAAE,OAAO,CAAC;QACjB,YAAY,EAAE,MAAM,CAAC;QACrB,cAAc,EAAE,MAAM,CAAC;QACvB,cAAc,EAAE;YACd,OAAO,EAAE,OAAO,CAAC;YACjB,MAAM,EAAE,MAAM,CAAC;YACf,QAAQ,EAAE,MAAM,CAAC;YACjB,aAAa,EAAE,OAAO,CAAC;SACxB,CAAC;QACF,cAAc,EAAE;YACd,OAAO,EAAE,OAAO,CAAC;YACjB,MAAM,EAAE,MAAM,CAAC;YACf,QAAQ,EAAE,MAAM,CAAC;SAClB,CAAC;QACF,aAAa,EAAE;YACb,OAAO,EAAE,OAAO,CAAC;YACjB,MAAM,EAAE,MAAM,CAAC;SAChB,CAAC;QACF,gBAAgB,EAAE;YAChB,OAAO,EAAE,OAAO,CAAC;YACjB,MAAM,EAAE,MAAM,CAAC;SAChB,CAAC;QACF,UAAU,EAAE;YACV,OAAO,EAAE,OAAO,CAAC;YACjB,MAAM,EAAE,MAAM,CAAC;YACf,gBAAgB,EAAE,MAAM,CAAC;SAC1B,CAAC;KACH,CAAC;IAGF,UAAU,EAAE;QACV,OAAO,EAAE,OAAO,CAAC;QACjB,cAAc,EAAE;YACd,OAAO,EAAE,OAAO,CAAC;YACjB,WAAW,EAAE,MAAM,CAAC;YACpB,UAAU,EAAE,MAAM,CAAC;YACnB,UAAU,EAAE,SAAS,GAAG,KAAK,CAAC;YAC9B,QAAQ,EAAE,MAAM,CAAC;SAClB,CAAC;QACF,UAAU,EAAE;YACV,OAAO,EAAE,OAAO,CAAC;YACjB,SAAS,EAAE,MAAM,CAAC;YAClB,SAAS,EAAE,MAAM,CAAC;YAClB,UAAU,EAAE,SAAS,GAAG,QAAQ,GAAG,MAAM,CAAC;YAC1C,QAAQ,EAAE,MAAM,CAAC;SAClB,CAAC;QACF,WAAW,EAAE;YACX,OAAO,EAAE,OAAO,CAAC;YACjB,KAAK,EAAE,MAAM,EAAE,CAAC;YAChB,UAAU,EAAE,SAAS,GAAG,KAAK,GAAG,QAAQ,CAAC;YACzC,QAAQ,EAAE,MAAM,CAAC;SAClB,CAAC;QACF,cAAc,EAAE;YACd,OAAO,EAAE,OAAO,CAAC;YACjB,cAAc,EAAE,MAAM,EAAE,CAAC;YACzB,UAAU,EAAE,SAAS,GAAG,QAAQ,GAAG,MAAM,CAAC;YAC1C,QAAQ,EAAE,MAAM,CAAC;SAClB,CAAC;QACF,kBAAkB,EAAE;YAClB,OAAO,EAAE,OAAO,CAAC;YACjB,UAAU,EAAE,MAAM,CAAC;YACnB,UAAU,EAAE,MAAM,CAAC;YACnB,UAAU,EAAE,SAAS,GAAG,QAAQ,CAAC;YACjC,QAAQ,EAAE,MAAM,CAAC;SAClB,CAAC;QACF,WAAW,EAAE;YACX,OAAO,EAAE,OAAO,CAAC;YACjB,UAAU,EAAE,OAAO,CAAC;SACrB,CAAC;KACH,CAAC;CACH;AAED,qBAAa,WAAW;IACtB,OAAO,CAAC,GAAG,CAAU;IACrB,OAAO,CAAC,cAAc,CAAiB;IACvC,OAAO,CAAC,oBAAoB,CAAC,CAAuB;IACpD,OAAO,CAAC,iBAAiB,CAAC,CAAoB;IAC9C,OAAO,CAAC,cAAc,CAAC,CAAiB;IACxC,OAAO,CAAC,cAAc,CAAC,CAAiB;IACxC,OAAO,CAAC,WAAW,CAAc;IACjC,OAAO,CAAC,cAAc,CAAiB;IACvC,OAAO,CAAC,SAAS,CAAC,CAAY;IAC9B,OAAO,CAAC,MAAM,CAAoB;IAClC,OAAO,CAAC,SAAS,CAAS;gBAEd,MAAM,EAAE,iBAAiB;IAuCrC,OAAO,CAAC,kBAAkB;IA8B1B,OAAO,CAAC,kBAAkB;IAqGpB,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IA8BtB,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC;IA8B3B,OAAO,CAAC,gBAAgB;IAsBxB,OAAO,CAAC,gBAAgB;IAalB,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC;IAyB9B,IAAI,WAAW,IAAI,OAAO,CAEzB;IAKD,IAAI,OAAO,IAAI,OAAO,CAErB;IAKD,IAAI,QAAQ;;;;;;;;MAUX;CACF"}