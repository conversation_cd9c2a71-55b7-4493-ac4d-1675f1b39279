import WebSocket from 'ws';
import { EventEmitter } from 'events';
import { logger } from '../utils/logger';

export interface SimpleKickMessage {
  id: string;
  type: string;
  data: {
    id: string;
    chatroom_id: number;
    content: string;
    type: string;
    created_at: string;
    sender: {
      id: number;
      username: string;
      slug: string;
      identity: {
        color: string;
        badges: any[];
      };
    };
  };
}

export class SimpleKickWebSocket extends EventEmitter {
  private ws?: WebSocket;
  private chatroomId?: number;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 5000;
  private isConnecting = false;
  private isConnected = false;
  private pingInterval?: NodeJS.Timeout;

  constructor() {
    super();
  }

  /**
   * Connect to Kick WebSocket
   */
  async connect(chatroomId: number): Promise<boolean> {
    if (this.isConnecting || this.isConnected) {
      return true;
    }

    this.isConnecting = true;
    this.chatroomId = chatroomId;

    try {
      logger.info(`Connecting to Kick WebSocket for chatroom ${chatroomId}...`);

      // Use the public WebSocket endpoint
      const wsUrl = `wss://ws-us2.pusher.com/app/32cbd69e4b950bf97679?protocol=7&client=js&version=7.4.0&flash=false`;
      
      this.ws = new WebSocket(wsUrl);

      this.ws.on('open', () => {
        logger.info('WebSocket connected to Kick');
        this.isConnected = true;
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        
        // Subscribe to chatroom
        this.subscribeToChatroom();
        
        // Start ping interval
        this.startPingInterval();
        
        this.emit('connected');
      });

      this.ws.on('message', (data: WebSocket.Data) => {
        try {
          const message = JSON.parse(data.toString());
          this.handleMessage(message);
        } catch (error) {
          logger.error('Failed to parse WebSocket message:', error);
        }
      });

      this.ws.on('close', (code: number, reason: string) => {
        logger.warn(`WebSocket closed: ${code} - ${reason}`);
        this.isConnected = false;
        this.isConnecting = false;
        this.stopPingInterval();
        
        this.emit('disconnected', { code, reason });
        
        // Attempt to reconnect
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.scheduleReconnect();
        } else {
          logger.error('Max reconnection attempts reached');
          this.emit('error', new Error('Max reconnection attempts reached'));
        }
      });

      this.ws.on('error', (error: Error) => {
        logger.error('WebSocket error:', error);
        this.isConnected = false;
        this.isConnecting = false;
        this.emit('error', error);
      });

      return true;
    } catch (error) {
      logger.error('Failed to connect to WebSocket:', error);
      this.isConnecting = false;
      return false;
    }
  }

  /**
   * Subscribe to chatroom events
   */
  private subscribeToChatroom(): void {
    if (!this.ws || !this.chatroomId) return;

    const subscribeMessage = {
      event: 'pusher:subscribe',
      data: {
        auth: '',
        channel: `chatrooms.${this.chatroomId}.v2`,
      },
    };

    logger.debug(`Subscribing to chatroom ${this.chatroomId}`);
    this.ws.send(JSON.stringify(subscribeMessage));
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleMessage(message: any): void {
    try {
      switch (message.event) {
        case 'pusher:connection_established':
          logger.info('Pusher connection established');
          break;

        case 'pusher:subscription_succeeded':
          logger.info('Successfully subscribed to chatroom');
          this.emit('subscribed');
          break;

        case 'App\\Events\\ChatMessageEvent':
          this.handleChatMessage(message);
          break;

        case 'App\\Events\\UserBannedEvent':
          this.emit('user_banned', message.data);
          break;

        case 'App\\Events\\UserUnbannedEvent':
          this.emit('user_unbanned', message.data);
          break;

        case 'App\\Events\\FollowersUpdated':
          this.emit('new_follower', message.data);
          break;

        case 'pusher:pong':
          // Pong received
          break;

        default:
          logger.debug('Unhandled WebSocket event:', message.event);
      }
    } catch (error) {
      logger.error('Error handling WebSocket message:', error);
    }
  }

  /**
   * Handle chat message events
   */
  private handleChatMessage(message: any): void {
    try {
      const chatMessage: SimpleKickMessage = {
        id: message.data.id,
        type: message.event,
        data: message.data,
      };

      logger.debug(`Chat message from ${chatMessage.data.sender.username}: ${chatMessage.data.content}`);
      this.emit('message', chatMessage);
    } catch (error) {
      logger.error('Error processing chat message:', error);
    }
  }

  /**
   * Start ping interval to keep connection alive
   */
  private startPingInterval(): void {
    this.pingInterval = setInterval(() => {
      if (this.ws && this.isConnected) {
        this.ws.send(JSON.stringify({ event: 'pusher:ping', data: {} }));
      }
    }, 30000); // Ping every 30 seconds
  }

  /**
   * Stop ping interval
   */
  private stopPingInterval(): void {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = undefined;
    }
  }

  /**
   * Schedule reconnection attempt
   */
  private scheduleReconnect(): void {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * this.reconnectAttempts;
    
    logger.info(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);
    
    setTimeout(() => {
      if (this.chatroomId) {
        this.connect(this.chatroomId);
      }
    }, delay);
  }

  /**
   * Send a message (placeholder - would need proper implementation)
   */
  async sendMessage(content: string): Promise<boolean> {
    // This would require proper API authentication
    // For now, just log the message
    logger.info(`Would send message: ${content}`);
    return true;
  }

  /**
   * Disconnect from WebSocket
   */
  disconnect(): void {
    logger.info('Disconnecting from WebSocket...');
    
    this.stopPingInterval();
    
    if (this.ws) {
      this.ws.close();
      this.ws = undefined;
    }
    
    this.isConnected = false;
    this.isConnecting = false;
    this.reconnectAttempts = 0;
  }

  /**
   * Check if connected
   */
  get connected(): boolean {
    return this.isConnected;
  }

  /**
   * Get current chatroom ID
   */
  get currentChatroomId(): number | undefined {
    return this.chatroomId;
  }
}
