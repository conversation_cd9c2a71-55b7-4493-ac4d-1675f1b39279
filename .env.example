# Kick Bot Configuration
BOT_USERNAME=your_bot_username
BOT_PASSWORD=your_bot_password
BOT_TOKEN=your_bot_token

# Channel Configuration
DEFAULT_CHANNEL=your_channel_name

# Database Configuration
DATABASE_PATH=./data/kickbot.db

# Web Dashboard Configuration
WEB_PORT=3000
JWT_SECRET=your_jwt_secret_here
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_admin_password

# API Configuration
KICK_API_BASE_URL=https://kick.com/api/v2
KICK_WEBSOCKET_URL=wss://ws-us2.pusher.app/app/32cbd69e4b950bf97679

# Features Configuration
ENABLE_MODERATION=true
ENABLE_COMMANDS=true
ENABLE_SONG_REQUESTS=true
ENABLE_GAMES=true
ENABLE_ANALYTICS=true

# Moderation Settings
MAX_MESSAGE_LENGTH=500
SPAM_THRESHOLD=5
CAPS_THRESHOLD=70
LINK_PROTECTION=true

# Song Request Settings (if using Spotify/YouTube)
SPOTIFY_CLIENT_ID=your_spotify_client_id
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret
YOUTUBE_API_KEY=your_youtube_api_key

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/kickbot.log
