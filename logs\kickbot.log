[2025-07-02T07:07:03.811Z] [INFO] Started passive earning: 10 points every 10 minutes
[2025-07-02T07:07:03.824Z] [INFO] 🚀 Starting FullKickBot...
[2025-07-02T07:07:03.913Z] [INFO] Database tables created successfully
[2025-07-02T07:07:03.915Z] [INFO] Database initialized successfully
[2025-07-02T07:07:03.916Z] [INFO] Registered command: help (aliases: commands, h)
[2025-07-02T07:07:03.917Z] [INFO] Registered command: uptime (aliases: up)
[2025-07-02T07:07:03.919Z] [INFO] Registered command: roll (aliases: dice, random)
[2025-07-02T07:07:03.920Z] [INFO] Registered command: 8ball (aliases: eightball, magic8ball)
[2025-07-02T07:07:03.921Z] [INFO] Registered command: timeout (aliases: to)
[2025-07-02T07:07:03.922Z] [INFO] Registered command: ban (aliases: )
[2025-07-02T07:07:03.925Z] [INFO] Registered command: unban (aliases: )
[2025-07-02T07:07:03.926Z] [INFO] Registered command: addcom (aliases: addcommand)
[2025-07-02T07:07:03.928Z] [INFO] Registered command: editcom (aliases: editcommand)
[2025-07-02T07:07:03.929Z] [INFO] Registered command: delcom (aliases: deletecommand, removecommand)
[2025-07-02T07:07:03.930Z] [INFO] Registered command: commands (aliases: listcommands)
[2025-07-02T07:07:03.931Z] [INFO] Registered default commands
[2025-07-02T07:07:03.933Z] [INFO] Registered command: addword (aliases: banword)
[2025-07-02T07:07:03.934Z] [INFO] Registered command: removeword (aliases: unbanword)
[2025-07-02T07:07:03.935Z] [INFO] Registered command: moderation (aliases: mod)
[2025-07-02T07:07:03.936Z] [INFO] Registered command: modstats (aliases: moderationstats)
[2025-07-02T07:07:03.937Z] [INFO] Registered command: usermod (aliases: userinfo)
[2025-07-02T07:07:03.938Z] [INFO] Registered command: clearuser (aliases: cleanusermod)
[2025-07-02T07:07:03.939Z] [INFO] Registered command: slow (aliases: slowmode)
[2025-07-02T07:07:03.941Z] [INFO] Registered command: followers (aliases: followersonly)
[2025-07-02T07:07:03.942Z] [INFO] Registered command: subs (aliases: subsonly, subscribers)
[2025-07-02T07:07:03.943Z] [INFO] Registered moderation commands
[2025-07-02T07:07:03.944Z] [INFO] Registered command: points (aliases: balance, coins)
[2025-07-02T07:07:03.945Z] [INFO] Registered command: give (aliases: givepoints)
[2025-07-02T07:07:03.946Z] [INFO] Registered command: transfer (aliases: send)
[2025-07-02T07:07:03.948Z] [INFO] Registered command: leaderboard (aliases: top, rich)
[2025-07-02T07:07:03.949Z] [INFO] Registered command: daily (aliases: bonus)
[2025-07-02T07:07:03.950Z] [INFO] Registered command: giveaway (aliases: startgiveaway)
[2025-07-02T07:07:03.951Z] [INFO] Registered command: enter (aliases: join)
[2025-07-02T07:07:03.952Z] [INFO] Registered command: endgiveaway (aliases: stopgiveaway)
[2025-07-02T07:07:03.953Z] [INFO] Registered command: cancelgiveaway (aliases: )
[2025-07-02T07:07:03.954Z] [INFO] Registered command: giveawayinfo (aliases: ginfo)
[2025-07-02T07:07:03.955Z] [INFO] Registered advanced feature commands
[2025-07-02T07:07:03.956Z] [INFO] 📝 All commands registered successfully
[2025-07-02T07:07:03.958Z] [INFO] Starting KickBot...
[2025-07-02T07:07:05.369Z] [ERROR] API Response Error: {"status":403,"statusText":"Forbidden","data":{"error":"Request blocked by security policy.","reference":"9e4db7e3"},"url":"/channels/YourChannelName"}
[2025-07-02T07:07:05.372Z] [ERROR] Failed to get channel YourChannelName: {"error":"Request blocked by security policy.","reference":"9e4db7e3"}
[2025-07-02T07:07:05.373Z] [ERROR] Failed to start bot: {}
[2025-07-02T07:07:05.375Z] [ERROR] 🚨 Bot error: {}
[2025-07-02T07:07:05.378Z] [ERROR] ❌ Failed to start FullKickBot: {}
[2025-07-02T07:09:03.612Z] [INFO] Started passive earning: 10 points every 10 minutes
[2025-07-02T07:09:03.628Z] [INFO] 🚀 Starting FullKickBot...
[2025-07-02T07:09:03.651Z] [INFO] Database tables created successfully
[2025-07-02T07:09:03.654Z] [INFO] Database initialized successfully
[2025-07-02T07:09:03.656Z] [INFO] Registered command: help (aliases: commands, h)
[2025-07-02T07:09:03.657Z] [INFO] Registered command: uptime (aliases: up)
[2025-07-02T07:09:03.659Z] [INFO] Registered command: roll (aliases: dice, random)
[2025-07-02T07:09:03.660Z] [INFO] Registered command: 8ball (aliases: eightball, magic8ball)
[2025-07-02T07:09:03.661Z] [INFO] Registered command: timeout (aliases: to)
[2025-07-02T07:09:03.663Z] [INFO] Registered command: ban (aliases: )
[2025-07-02T07:09:03.664Z] [INFO] Registered command: unban (aliases: )
[2025-07-02T07:09:03.665Z] [INFO] Registered command: addcom (aliases: addcommand)
[2025-07-02T07:09:03.667Z] [INFO] Registered command: editcom (aliases: editcommand)
[2025-07-02T07:09:03.668Z] [INFO] Registered command: delcom (aliases: deletecommand, removecommand)
[2025-07-02T07:09:03.669Z] [INFO] Registered command: commands (aliases: listcommands)
[2025-07-02T07:09:03.671Z] [INFO] Registered default commands
[2025-07-02T07:09:03.672Z] [INFO] Registered command: addword (aliases: banword)
[2025-07-02T07:09:03.673Z] [INFO] Registered command: removeword (aliases: unbanword)
[2025-07-02T07:09:03.674Z] [INFO] Registered command: moderation (aliases: mod)
[2025-07-02T07:09:03.675Z] [INFO] Registered command: modstats (aliases: moderationstats)
[2025-07-02T07:09:03.676Z] [INFO] Registered command: usermod (aliases: userinfo)
[2025-07-02T07:09:03.677Z] [INFO] Registered command: clearuser (aliases: cleanusermod)
[2025-07-02T07:09:03.678Z] [INFO] Registered command: slow (aliases: slowmode)
[2025-07-02T07:09:03.679Z] [INFO] Registered command: followers (aliases: followersonly)
[2025-07-02T07:09:03.680Z] [INFO] Registered command: subs (aliases: subsonly, subscribers)
[2025-07-02T07:09:03.680Z] [INFO] Registered moderation commands
[2025-07-02T07:09:03.682Z] [INFO] Registered command: points (aliases: balance, coins)
[2025-07-02T07:09:03.683Z] [INFO] Registered command: give (aliases: givepoints)
[2025-07-02T07:09:03.684Z] [INFO] Registered command: transfer (aliases: send)
[2025-07-02T07:09:03.686Z] [INFO] Registered command: leaderboard (aliases: top, rich)
[2025-07-02T07:09:03.687Z] [INFO] Registered command: daily (aliases: bonus)
[2025-07-02T07:09:03.688Z] [INFO] Registered command: giveaway (aliases: startgiveaway)
[2025-07-02T07:09:03.689Z] [INFO] Registered command: enter (aliases: join)
[2025-07-02T07:09:03.690Z] [INFO] Registered command: endgiveaway (aliases: stopgiveaway)
[2025-07-02T07:09:03.691Z] [INFO] Registered command: cancelgiveaway (aliases: )
[2025-07-02T07:09:03.692Z] [INFO] Registered command: giveawayinfo (aliases: ginfo)
[2025-07-02T07:09:03.693Z] [INFO] Registered advanced feature commands
[2025-07-02T07:09:03.694Z] [INFO] 📝 All commands registered successfully
[2025-07-02T07:09:03.695Z] [INFO] Starting KickBot...
[2025-07-02T07:09:03.917Z] [ERROR] API Response Error: {"status":403,"statusText":"Forbidden","data":{"error":"Request blocked by security policy.","reference":"9e4db7e3"},"url":"/sanctum/csrf-cookie"}
[2025-07-02T07:09:03.920Z] [ERROR] Authentication failed: {"error":"Request blocked by security policy.","reference":"9e4db7e3"}
[2025-07-02T07:09:03.922Z] [ERROR] Failed to start bot: {}
[2025-07-02T07:09:03.923Z] [ERROR] 🚨 Bot error: {}
[2025-07-02T07:09:03.924Z] [ERROR] ❌ Failed to start FullKickBot: {}
