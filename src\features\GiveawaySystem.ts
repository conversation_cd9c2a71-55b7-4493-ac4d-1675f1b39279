import { EventEmitter } from 'events';
import { database } from '../database/Database';
import { logger } from '../utils/logger';
import { KickBot } from '../bot/KickBot';
import { PermissionLevel } from '../types';

export interface Giveaway {
  id: number;
  title: string;
  description?: string;
  prize: string;
  duration: number; // in minutes
  maxEntries: number; // -1 for unlimited
  entryCost: number; // points cost to enter
  status: 'active' | 'ended' | 'cancelled';
  winner?: string;
  createdBy: string;
  createdAt: Date;
  endedAt?: Date;
  entries: GiveawayEntry[];
}

export interface GiveawayEntry {
  username: string;
  entries: number;
  timestamp: Date;
}

export class GiveawaySystem extends EventEmitter {
  private bot: KickBot;
  private activeGiveaways = new Map<number, Giveaway>();
  private giveawayTimers = new Map<number, NodeJS.Timeout>();

  constructor(bot: KickBot) {
    super();
    this.bot = bot;
    this.setupEventHandlers();
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    this.bot.on('started', () => {
      this.loadActiveGiveaways();
    });
  }

  /**
   * Load active giveaways from database
   */
  private async loadActiveGiveaways(): Promise<void> {
    try {
      const giveaways = await database.db.all(`
        SELECT * FROM giveaways 
        WHERE status = 'active'
      `);

      for (const giveawayData of giveaways) {
        const giveaway = await this.loadGiveawayWithEntries(giveawayData.id);
        if (giveaway) {
          this.activeGiveaways.set(giveaway.id, giveaway);
          this.scheduleGiveawayEnd(giveaway);
        }
      }

      logger.info(`Loaded ${giveaways.length} active giveaways`);
    } catch (error) {
      logger.error('Failed to load active giveaways:', error);
    }
  }

  /**
   * Load giveaway with entries
   */
  private async loadGiveawayWithEntries(giveawayId: number): Promise<Giveaway | null> {
    try {
      const giveawayData = await database.db.get('SELECT * FROM giveaways WHERE id = ?', [giveawayId]);
      if (!giveawayData) return null;

      const entries = await database.db.all(`
        SELECT username, entries, timestamp 
        FROM giveaway_entries 
        WHERE giveaway_id = ?
      `, [giveawayId]);

      return {
        id: giveawayData.id,
        title: giveawayData.title,
        description: giveawayData.description,
        prize: giveawayData.prize,
        duration: giveawayData.duration,
        maxEntries: giveawayData.max_entries,
        entryCost: giveawayData.entry_cost,
        status: giveawayData.status,
        winner: giveawayData.winner,
        createdBy: giveawayData.created_by,
        createdAt: new Date(giveawayData.created_at),
        endedAt: giveawayData.ended_at ? new Date(giveawayData.ended_at) : undefined,
        entries: entries.map((entry: any) => ({
          username: entry.username,
          entries: entry.entries,
          timestamp: new Date(entry.timestamp),
        })),
      };
    } catch (error) {
      logger.error(`Failed to load giveaway ${giveawayId}:`, error);
      return null;
    }
  }

  /**
   * Create a new giveaway
   */
  async createGiveaway(
    title: string,
    prize: string,
    duration: number,
    createdBy: string,
    options: {
      description?: string;
      maxEntries?: number;
      entryCost?: number;
    } = {}
  ): Promise<{ success: boolean; giveaway?: Giveaway; message: string }> {
    try {
      // Insert into database
      const result = await database.db.run(`
        INSERT INTO giveaways (title, description, prize, duration, max_entries, entry_cost, created_by)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        title,
        options.description || null,
        prize,
        duration,
        options.maxEntries || -1,
        options.entryCost || 0,
        createdBy
      ]);

      const giveawayId = result.lastID;
      const giveaway: Giveaway = {
        id: giveawayId,
        title,
        description: options.description,
        prize,
        duration,
        maxEntries: options.maxEntries || -1,
        entryCost: options.entryCost || 0,
        status: 'active',
        createdBy,
        createdAt: new Date(),
        entries: [],
      };

      this.activeGiveaways.set(giveawayId, giveaway);
      this.scheduleGiveawayEnd(giveaway);

      // Announce giveaway
      const announcement = `🎉 NEW GIVEAWAY: ${title} | Prize: ${prize} | Duration: ${duration}m${options.entryCost ? ` | Cost: ${options.entryCost} points` : ''} | Use !enter to join!`;
      await this.bot.sendMessage(announcement);

      this.emit('giveaway_created', giveaway);
      logger.info(`Created giveaway: ${title} (ID: ${giveawayId})`);

      return {
        success: true,
        giveaway,
        message: `Giveaway "${title}" created successfully!`
      };
    } catch (error) {
      logger.error('Failed to create giveaway:', error);
      return {
        success: false,
        message: 'Failed to create giveaway'
      };
    }
  }

  /**
   * Enter a giveaway
   */
  async enterGiveaway(username: string, giveawayId?: number): Promise<{ success: boolean; message: string }> {
    try {
      // If no giveaway ID specified, use the most recent active one
      if (!giveawayId) {
        const activeGiveawayIds = Array.from(this.activeGiveaways.keys());
        if (activeGiveawayIds.length === 0) {
          return { success: false, message: 'No active giveaways' };
        }
        giveawayId = Math.max(...activeGiveawayIds);
      }

      const giveaway = this.activeGiveaways.get(giveawayId);
      if (!giveaway || giveaway.status !== 'active') {
        return { success: false, message: 'Giveaway not found or not active' };
      }

      // Check if user already entered
      const existingEntry = giveaway.entries.find(entry => entry.username.toLowerCase() === username.toLowerCase());
      if (existingEntry) {
        return { success: false, message: 'You are already entered in this giveaway' };
      }

      // Check max entries
      if (giveaway.maxEntries > 0 && giveaway.entries.length >= giveaway.maxEntries) {
        return { success: false, message: 'Giveaway is full' };
      }

      // Check entry cost
      if (giveaway.entryCost > 0) {
        const user = await database.getUser(username);
        if (!user || user.points < giveaway.entryCost) {
          return { success: false, message: `Insufficient points. Need ${giveaway.entryCost} points to enter` };
        }

        // Deduct points
        await database.updateUser(username, { points: user.points - giveaway.entryCost });
      }

      // Add entry
      const entry: GiveawayEntry = {
        username,
        entries: 1,
        timestamp: new Date(),
      };

      giveaway.entries.push(entry);

      // Save to database
      await database.db.run(`
        INSERT INTO giveaway_entries (giveaway_id, username, entries)
        VALUES (?, ?, ?)
      `, [giveawayId, username, 1]);

      this.emit('giveaway_entry', { giveaway, entry });
      logger.debug(`${username} entered giveaway: ${giveaway.title}`);

      return {
        success: true,
        message: `Successfully entered giveaway "${giveaway.title}"! (${giveaway.entries.length} total entries)`
      };
    } catch (error) {
      logger.error(`Failed to enter giveaway for ${username}:`, error);
      return { success: false, message: 'Failed to enter giveaway' };
    }
  }

  /**
   * End a giveaway and pick winner
   */
  async endGiveaway(giveawayId: number, forcedBy?: string): Promise<{ success: boolean; winner?: string; message: string }> {
    try {
      const giveaway = this.activeGiveaways.get(giveawayId);
      if (!giveaway) {
        return { success: false, message: 'Giveaway not found' };
      }

      if (giveaway.entries.length === 0) {
        giveaway.status = 'ended';
        await database.db.run(`
          UPDATE giveaways 
          SET status = 'ended', ended_at = CURRENT_TIMESTAMP 
          WHERE id = ?
        `, [giveawayId]);

        this.activeGiveaways.delete(giveawayId);
        this.clearGiveawayTimer(giveawayId);

        const message = `Giveaway "${giveaway.title}" ended with no entries.`;
        await this.bot.sendMessage(message);

        return { success: true, message };
      }

      // Pick random winner
      const totalEntries = giveaway.entries.reduce((sum, entry) => sum + entry.entries, 0);
      const randomNum = Math.floor(Math.random() * totalEntries) + 1;
      
      let currentSum = 0;
      let winner = '';
      
      for (const entry of giveaway.entries) {
        currentSum += entry.entries;
        if (randomNum <= currentSum) {
          winner = entry.username;
          break;
        }
      }

      // Update giveaway
      giveaway.status = 'ended';
      giveaway.winner = winner;
      giveaway.endedAt = new Date();

      await database.db.run(`
        UPDATE giveaways 
        SET status = 'ended', winner = ?, ended_at = CURRENT_TIMESTAMP 
        WHERE id = ?
      `, [winner, giveawayId]);

      this.activeGiveaways.delete(giveawayId);
      this.clearGiveawayTimer(giveawayId);

      // Announce winner
      const announcement = `🎊 GIVEAWAY WINNER: @${winner} won "${giveaway.prize}" from "${giveaway.title}"! Congratulations! 🎊`;
      await this.bot.sendMessage(announcement);

      this.emit('giveaway_ended', { giveaway, winner });
      logger.info(`Giveaway "${giveaway.title}" ended. Winner: ${winner}`);

      return {
        success: true,
        winner,
        message: `Giveaway ended. Winner: ${winner}`
      };
    } catch (error) {
      logger.error(`Failed to end giveaway ${giveawayId}:`, error);
      return { success: false, message: 'Failed to end giveaway' };
    }
  }

  /**
   * Cancel a giveaway
   */
  async cancelGiveaway(giveawayId: number, cancelledBy: string): Promise<{ success: boolean; message: string }> {
    try {
      const giveaway = this.activeGiveaways.get(giveawayId);
      if (!giveaway) {
        return { success: false, message: 'Giveaway not found' };
      }

      // Refund entry costs
      if (giveaway.entryCost > 0) {
        for (const entry of giveaway.entries) {
          const user = await database.getUser(entry.username);
          if (user) {
            await database.updateUser(entry.username, { 
              points: user.points + (giveaway.entryCost * entry.entries) 
            });
          }
        }
      }

      // Update status
      giveaway.status = 'cancelled';
      await database.db.run(`
        UPDATE giveaways 
        SET status = 'cancelled', ended_at = CURRENT_TIMESTAMP 
        WHERE id = ?
      `, [giveawayId]);

      this.activeGiveaways.delete(giveawayId);
      this.clearGiveawayTimer(giveawayId);

      const message = `Giveaway "${giveaway.title}" has been cancelled${giveaway.entryCost > 0 ? '. Entry costs have been refunded' : ''}.`;
      await this.bot.sendMessage(message);

      this.emit('giveaway_cancelled', giveaway);
      logger.info(`Giveaway "${giveaway.title}" cancelled by ${cancelledBy}`);

      return { success: true, message };
    } catch (error) {
      logger.error(`Failed to cancel giveaway ${giveawayId}:`, error);
      return { success: false, message: 'Failed to cancel giveaway' };
    }
  }

  /**
   * Get active giveaways
   */
  getActiveGiveaways(): Giveaway[] {
    return Array.from(this.activeGiveaways.values());
  }

  /**
   * Get giveaway by ID
   */
  getGiveaway(giveawayId: number): Giveaway | undefined {
    return this.activeGiveaways.get(giveawayId);
  }

  /**
   * Schedule giveaway end
   */
  private scheduleGiveawayEnd(giveaway: Giveaway): void {
    const endTime = giveaway.createdAt.getTime() + (giveaway.duration * 60 * 1000);
    const timeUntilEnd = endTime - Date.now();

    if (timeUntilEnd > 0) {
      const timer = setTimeout(() => {
        this.endGiveaway(giveaway.id);
      }, timeUntilEnd);

      this.giveawayTimers.set(giveaway.id, timer);
    } else {
      // Giveaway should have already ended
      this.endGiveaway(giveaway.id);
    }
  }

  /**
   * Clear giveaway timer
   */
  private clearGiveawayTimer(giveawayId: number): void {
    const timer = this.giveawayTimers.get(giveawayId);
    if (timer) {
      clearTimeout(timer);
      this.giveawayTimers.delete(giveawayId);
    }
  }

  /**
   * Get giveaway statistics
   */
  async getStats(): Promise<any> {
    try {
      const totalGiveaways = await database.db.get('SELECT COUNT(*) as count FROM giveaways');
      const activeGiveaways = await database.db.get('SELECT COUNT(*) as count FROM giveaways WHERE status = "active"');
      const totalEntries = await database.db.get('SELECT COUNT(*) as count FROM giveaway_entries');
      const recentWinners = await database.db.all(`
        SELECT winner, title, prize, ended_at 
        FROM giveaways 
        WHERE winner IS NOT NULL 
        ORDER BY ended_at DESC 
        LIMIT 5
      `);

      return {
        totalGiveaways: totalGiveaways.count,
        activeGiveaways: activeGiveaways.count,
        totalEntries: totalEntries.count,
        recentWinners: recentWinners.map((g: any) => ({
          winner: g.winner,
          title: g.title,
          prize: g.prize,
          endedAt: g.ended_at,
        })),
      };
    } catch (error) {
      logger.error('Failed to get giveaway stats:', error);
      return {
        totalGiveaways: 0,
        activeGiveaways: 0,
        totalEntries: 0,
        recentWinners: [],
      };
    }
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    for (const timer of this.giveawayTimers.values()) {
      clearTimeout(timer);
    }
    this.giveawayTimers.clear();
  }
}
