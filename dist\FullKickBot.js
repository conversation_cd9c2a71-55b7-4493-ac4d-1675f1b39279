"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FullKickBot = void 0;
const KickBot_1 = require("./bot/KickBot");
const CommandHandler_1 = require("./commands/CommandHandler");
const CustomCommandManager_1 = require("./commands/CustomCommandManager");
const ModerationManager_1 = require("./moderation/ModerationManager");
const CurrencySystem_1 = require("./features/CurrencySystem");
const GiveawaySystem_1 = require("./features/GiveawaySystem");
const UserManager_1 = require("./users/UserManager");
const ChannelManager_1 = require("./channels/ChannelManager");
const Database_1 = require("./database/Database");
const defaultCommands_1 = require("./commands/defaultCommands");
const moderationCommands_1 = require("./commands/moderationCommands");
const advancedCommands_1 = require("./commands/advancedCommands");
const WebServer_1 = require("./web/WebServer");
const logger_1 = require("./utils/logger");
class FullKickBot {
    constructor(config) {
        this.isRunning = false;
        this.config = config;
        this.bot = new KickBot_1.KickBot({
            username: config.bot.username,
            password: config.bot.password,
            token: config.bot.token,
            channel: config.bot.channel,
            prefix: config.bot.prefix,
            features: {
                moderation: config.features.moderation,
                commands: config.features.customCommands,
                songRequests: false,
                games: true,
                analytics: config.features.analytics,
            },
            moderation: {
                maxMessageLength: 500,
                spamThreshold: config.moderation.spamProtection.maxMessages,
                capsThreshold: config.moderation.capsFilter.threshold,
                linkProtection: config.moderation.linkProtection.enabled,
                bannedWords: config.moderation.bannedWords.words,
                autoTimeout: config.moderation.autoTimeout.enabled,
                timeoutDuration: config.moderation.spamProtection.duration,
            },
        });
        this.commandHandler = new CommandHandler_1.CommandHandler(this.bot);
        this.userManager = new UserManager_1.UserManager();
        this.channelManager = new ChannelManager_1.ChannelManager();
        this.initializeFeatures();
        this.setupEventHandlers();
    }
    initializeFeatures() {
        if (this.config.features.customCommands) {
            this.customCommandManager = new CustomCommandManager_1.CustomCommandManager(this.commandHandler);
        }
        if (this.config.features.moderation) {
            this.moderationManager = new ModerationManager_1.ModerationManager(this.bot, this.config.moderation);
        }
        if (this.config.features.currency) {
            this.currencySystem = new CurrencySystem_1.CurrencySystem(this.bot, this.config.currency);
        }
        if (this.config.features.giveaways) {
            this.giveawaySystem = new GiveawaySystem_1.GiveawaySystem(this.bot);
        }
        if (this.config.features.webDashboard) {
            this.webServer = new WebServer_1.WebServer(this);
        }
    }
    setupEventHandlers() {
        this.bot.on('started', () => {
            logger_1.logger.info('🤖 FullKickBot started successfully!');
            this.isRunning = true;
        });
        this.bot.on('stopped', () => {
            logger_1.logger.info('🤖 FullKickBot stopped');
            this.isRunning = false;
        });
        this.bot.on('message', (message) => {
            this.userManager.updateUser(message.sender);
            this.userManager.incrementMessageCount(message.sender.username);
            if (this.config.features.analytics) {
                Database_1.database.logMessage(message.sender.username, message.content, this.config.bot.channel, 'message');
            }
        });
        this.bot.on('command', (commandData) => {
            this.userManager.incrementCommandCount(commandData.user.username);
        });
        this.bot.on('new_follower', (data) => {
            logger_1.logger.info(`🎉 New follower: ${data.username}`);
            this.bot.sendMessage(`Welcome to the stream, @${data.username}! Thanks for following! 🎉`);
        });
        if (this.currencySystem) {
            this.currencySystem.on('points_added', (data) => {
                logger_1.logger.debug(`💰 ${data.username} earned ${data.amount} points: ${data.reason}`);
            });
            this.currencySystem.on('points_transferred', (data) => {
                logger_1.logger.info(`💸 ${data.from} transferred ${data.amount} points to ${data.to}`);
            });
        }
        if (this.giveawaySystem) {
            this.giveawaySystem.on('giveaway_created', (giveaway) => {
                logger_1.logger.info(`🎁 Giveaway created: ${giveaway.title}`);
            });
            this.giveawaySystem.on('giveaway_ended', (data) => {
                logger_1.logger.info(`🏆 Giveaway "${data.giveaway.title}" won by ${data.winner}`);
            });
        }
        if (this.moderationManager) {
            this.moderationManager.on('violations_detected', (data) => {
                logger_1.logger.warn(`⚠️ Violations detected for ${data.user.username}: ${data.violations.join(', ')}`);
            });
            this.moderationManager.on('punishment_executed', (data) => {
                logger_1.logger.info(`🔨 Punishment: ${data.punishment.type} for ${data.user.username}`);
                if (this.config.features.analytics) {
                    Database_1.database.logModerationAction(data.user.username, data.punishment.type, data.violations.join(', '), data.punishment.duration || null, 'auto-mod');
                }
            });
        }
        this.bot.on('error', (error) => {
            logger_1.logger.error('🚨 Bot error:', error);
            setTimeout(async () => {
                try {
                    logger_1.logger.info('🔄 Attempting to restart bot after error...');
                    await this.stop();
                    await this.start();
                }
                catch (restartError) {
                    logger_1.logger.error('❌ Failed to restart bot:', restartError);
                }
            }, 5000);
        });
    }
    async start() {
        try {
            logger_1.logger.info('🚀 Starting FullKickBot...');
            await Database_1.database.initialize();
            this.registerCommands();
            await this.bot.start();
            if (this.webServer) {
                await this.webServer.start();
            }
            logger_1.logger.info('✅ FullKickBot is now fully operational!');
            this.logFeatureStatus();
        }
        catch (error) {
            logger_1.logger.error('❌ Failed to start FullKickBot:', error);
            throw error;
        }
    }
    async stop() {
        try {
            logger_1.logger.info('🛑 Stopping FullKickBot...');
            await this.bot.stop();
            if (this.webServer) {
                await this.webServer.stop();
            }
            this.currencySystem?.cleanup();
            this.giveawaySystem?.cleanup();
            this.channelManager.cleanup();
            await Database_1.database.close();
            logger_1.logger.info('✅ FullKickBot stopped successfully');
        }
        catch (error) {
            logger_1.logger.error('❌ Error stopping FullKickBot:', error);
            throw error;
        }
    }
    registerCommands() {
        (0, defaultCommands_1.registerDefaultCommands)(this.commandHandler, this.customCommandManager);
        if (this.moderationManager) {
            (0, moderationCommands_1.registerModerationCommands)(this.commandHandler, this.moderationManager);
        }
        (0, advancedCommands_1.registerAdvancedCommands)(this.commandHandler, this.currencySystem, this.giveawaySystem);
        logger_1.logger.info('📝 All commands registered successfully');
    }
    logFeatureStatus() {
        logger_1.logger.info('🔧 Feature Status:');
        logger_1.logger.info(`   • Custom Commands: ${this.config.features.customCommands ? '✅' : '❌'}`);
        logger_1.logger.info(`   • Moderation: ${this.config.features.moderation ? '✅' : '❌'}`);
        logger_1.logger.info(`   • Currency System: ${this.config.features.currency ? '✅' : '❌'}`);
        logger_1.logger.info(`   • Giveaways: ${this.config.features.giveaways ? '✅' : '❌'}`);
        logger_1.logger.info(`   • Analytics: ${this.config.features.analytics ? '✅' : '❌'}`);
        logger_1.logger.info(`   • Web Dashboard: ${this.config.features.webDashboard ? '✅' : '❌'}`);
    }
    async getStats() {
        const botStats = this.bot.getStats();
        const dbStats = await Database_1.database.getStats();
        const userStats = this.userManager.getCacheStats();
        const channelStats = this.channelManager.getCacheStats();
        const stats = {
            bot: botStats,
            database: dbStats,
            users: userStats,
            channels: channelStats,
            features: {
                customCommands: this.customCommandManager?.getCommandStats(),
                moderation: this.moderationManager?.getModerationStats(),
                currency: this.currencySystem ? await this.currencySystem.getStats() : null,
                giveaways: this.giveawaySystem ? await this.giveawaySystem.getStats() : null,
            },
        };
        return stats;
    }
    get botInstance() {
        return this.bot;
    }
    get running() {
        return this.isRunning;
    }
    get features() {
        return {
            commands: this.commandHandler,
            customCommands: this.customCommandManager,
            moderation: this.moderationManager,
            currency: this.currencySystem,
            giveaways: this.giveawaySystem,
            users: this.userManager,
            channels: this.channelManager,
        };
    }
}
exports.FullKickBot = FullKickBot;
//# sourceMappingURL=FullKickBot.js.map