import fs from 'fs';
import path from 'path';
import { logConfig } from '../config';

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
}

class Logger {
  private logLevel: LogLevel;
  private logFile: string;

  constructor() {
    this.logLevel = this.getLogLevel(logConfig.level);
    this.logFile = logConfig.file;
    this.ensureLogDirectory();
  }

  private getLogLevel(level: string): LogLevel {
    switch (level.toLowerCase()) {
      case 'error': return LogLevel.ERROR;
      case 'warn': return LogLevel.WARN;
      case 'info': return LogLevel.INFO;
      case 'debug': return LogLevel.DEBUG;
      default: return LogLevel.INFO;
    }
  }

  private ensureLogDirectory(): void {
    const logDir = path.dirname(this.logFile);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
  }

  private formatMessage(level: string, message: string, data?: any): string {
    const timestamp = new Date().toISOString();
    const dataStr = data ? ` ${JSON.stringify(data)}` : '';
    return `[${timestamp}] [${level}] ${message}${dataStr}`;
  }

  private writeToFile(message: string): void {
    try {
      fs.appendFileSync(this.logFile, message + '\n');
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  private log(level: LogLevel, levelName: string, message: string, data?: any): void {
    if (level <= this.logLevel) {
      const formattedMessage = this.formatMessage(levelName, message, data);
      
      // Console output with colors
      const colors = {
        ERROR: '\x1b[31m', // Red
        WARN: '\x1b[33m',  // Yellow
        INFO: '\x1b[36m',  // Cyan
        DEBUG: '\x1b[37m', // White
        RESET: '\x1b[0m'   // Reset
      };
      
      console.log(`${colors[levelName as keyof typeof colors]}${formattedMessage}${colors.RESET}`);
      
      // File output
      this.writeToFile(formattedMessage);
    }
  }

  error(message: string, data?: any): void {
    this.log(LogLevel.ERROR, 'ERROR', message, data);
  }

  warn(message: string, data?: any): void {
    this.log(LogLevel.WARN, 'WARN', message, data);
  }

  info(message: string, data?: any): void {
    this.log(LogLevel.INFO, 'INFO', message, data);
  }

  debug(message: string, data?: any): void {
    this.log(LogLevel.DEBUG, 'DEBUG', message, data);
  }

  // Special methods for bot events
  chatMessage(username: string, message: string, channel: string): void {
    this.info(`[${channel}] ${username}: ${message}`);
  }

  commandUsed(username: string, command: string, args: string[], channel: string): void {
    this.info(`[${channel}] ${username} used command: ${command} ${args.join(' ')}`);
  }

  moderationAction(action: string, target: string, moderator: string, reason?: string): void {
    const reasonStr = reason ? ` (${reason})` : '';
    this.info(`Moderation: ${moderator} ${action} ${target}${reasonStr}`);
  }

  botEvent(event: string, data?: any): void {
    this.info(`Bot Event: ${event}`, data);
  }
}

export const logger = new Logger();
