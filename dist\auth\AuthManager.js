"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authManager = exports.AuthManager = void 0;
const kickApi_1 = require("../api/kickApi");
const logger_1 = require("../utils/logger");
class AuthManager {
    constructor() {
        this.isAuthenticated = false;
    }
    async authenticate(credentials) {
        try {
            logger_1.logger.info('Attempting to authenticate with Kick...');
            if (credentials.token) {
                return await this.authenticateWithToken(credentials.token);
            }
            else if (credentials.email && credentials.password) {
                return await this.authenticateWithPassword(credentials.email, credentials.password);
            }
            else {
                return {
                    success: false,
                    error: 'No valid authentication credentials provided',
                };
            }
        }
        catch (error) {
            logger_1.logger.error('Authentication failed:', error);
            return {
                success: false,
                error: error.message || 'Authentication failed',
            };
        }
    }
    async authenticateWithToken(token) {
        try {
            this.authToken = token;
            this.isAuthenticated = true;
            logger_1.logger.info('Successfully authenticated with token');
            return {
                success: true,
                userId: this.currentUserId,
                username: this.currentUsername,
            };
        }
        catch (error) {
            logger_1.logger.error('Token authentication failed:', error);
            return {
                success: false,
                error: 'Invalid token',
            };
        }
    }
    async authenticateWithPassword(email, password) {
        try {
            const success = await kickApi_1.kickAPI.authenticate(email, password);
            if (success) {
                this.isAuthenticated = true;
                this.currentUserId = kickApi_1.kickAPI.currentUserId;
                logger_1.logger.info('Successfully authenticated with email/password');
                return {
                    success: true,
                    userId: this.currentUserId,
                    username: this.currentUsername,
                };
            }
            else {
                return {
                    success: false,
                    error: 'Invalid email or password',
                };
            }
        }
        catch (error) {
            logger_1.logger.error('Password authentication failed:', error);
            return {
                success: false,
                error: error.message || 'Authentication failed',
            };
        }
    }
    logout() {
        this.isAuthenticated = false;
        this.currentUserId = undefined;
        this.currentUsername = undefined;
        this.authToken = undefined;
        logger_1.logger.info('Logged out successfully');
    }
    get authenticated() {
        return this.isAuthenticated;
    }
    get userId() {
        return this.currentUserId;
    }
    get username() {
        return this.currentUsername;
    }
    get token() {
        return this.authToken;
    }
    async refreshAuth() {
        if (!this.isAuthenticated) {
            return false;
        }
        try {
            return this.isAuthenticated;
        }
        catch (error) {
            logger_1.logger.error('Failed to refresh authentication:', error);
            this.logout();
            return false;
        }
    }
    async validateAuth() {
        if (!this.isAuthenticated) {
            return false;
        }
        try {
            return kickApi_1.kickAPI.isAuthenticated;
        }
        catch (error) {
            logger_1.logger.error('Authentication validation failed:', error);
            this.logout();
            return false;
        }
    }
}
exports.AuthManager = AuthManager;
exports.authManager = new AuthManager();
//# sourceMappingURL=AuthManager.js.map