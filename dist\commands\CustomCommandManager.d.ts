import { EventEmitter } from 'events';
import { Command, PermissionLevel } from '../types';
import { CommandHandler, CommandContext } from './CommandHandler';
export interface CustomCommand extends Command {
    id: string;
    response: string;
    variables: string[];
    isCustom: true;
    useCount: number;
    lastUsed?: string;
}
export interface CommandVariable {
    name: string;
    description: string;
    example: string;
    resolver: (context: CommandContext) => string | Promise<string>;
}
export declare class CustomCommandManager extends EventEmitter {
    private customCommands;
    private variables;
    private commandHandler;
    constructor(commandHandler: CommandHandler);
    private setupDefaultVariables;
    registerVariable(variable: CommandVariable): void;
    createCommand(name: string, response: string, options: {
        description?: string;
        aliases?: string[];
        cooldown?: number;
        permission?: PermissionLevel;
        enabled?: boolean;
        createdBy: string;
    }): Promise<boolean>;
    updateCommand(name: string, updates: {
        response?: string;
        description?: string;
        aliases?: string[];
        cooldown?: number;
        permission?: PermissionLevel;
        enabled?: boolean;
    }): Promise<boolean>;
    deleteCommand(name: string): Promise<boolean>;
    private executeCustomCommand;
    private processVariables;
    private extractVariables;
    private isValidCommandName;
    private generateCommandId;
    getCustomCommand(name: string): CustomCommand | undefined;
    getAllCustomCommands(): CustomCommand[];
    getCustomCommandsByPermission(permissionLevel: PermissionLevel): CustomCommand[];
    getAvailableVariables(): CommandVariable[];
    getCommandStats(): {
        totalCommands: number;
        enabledCommands: number;
        disabledCommands: number;
        totalUses: number;
        averageUses: number;
        mostUsedCommands: {
            name: string;
            uses: number;
            lastUsed: string | undefined;
        }[];
        availableVariables: number;
    };
    exportCommands(): CustomCommand[];
    importCommands(commands: CustomCommand[]): Promise<number>;
}
//# sourceMappingURL=CustomCommandManager.d.ts.map