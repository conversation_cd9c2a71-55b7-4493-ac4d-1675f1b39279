{"version": 3, "file": "kickWebSocket.js", "sourceRoot": "", "sources": ["../../src/api/kickWebSocket.ts"], "names": [], "mappings": ";;;;;;AAAA,4CAA2B;AAC3B,mCAAsC;AACtC,sCAAuC;AACvC,4CAAyC;AAmBzC,MAAa,aAAc,SAAQ,qBAAY;IAW7C;QACE,KAAK,EAAE,CAAC;QARF,gBAAW,GAAG,KAAK,CAAC;QACpB,sBAAiB,GAAG,CAAC,CAAC;QACtB,yBAAoB,GAAG,CAAC,CAAC;QACzB,mBAAc,GAAG,IAAI,CAAC;IAM9B,CAAC;IAKD,KAAK,CAAC,OAAO,CAAC,SAAiB,EAAE,UAAkB;QACjD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAE7B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC;gBA<PERSON>,MAAM,KAAK,GAAG,GAAG,mBAAU,CAAC,YAAY,qDAAqD,CAAC;gBAE9F,eAAM,CAAC,IAAI,CAAC,iCAAiC,KAAK,EAAE,CAAC,CAAC;gBACtD,IAAI,CAAC,EAAE,GAAG,IAAI,YAAS,CAAC,KAAK,CAAC,CAAC;gBAE/B,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC,GAAG,EAAE;oBACvC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;wBACtB,eAAM,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;wBAC7C,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,CAAC;wBACrB,MAAM,CAAC,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC;oBAC1C,CAAC;gBACH,CAAC,EAAE,KAAK,CAAC,CAAC;gBAEV,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;oBACtB,eAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;oBAC3C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;oBACxB,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;oBAE3B,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;wBAC3B,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;oBACvC,CAAC;oBAGD,IAAI,CAAC,qBAAqB,EAAE,CAAC;oBAG7B,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAG1B,IAAI,CAAC,cAAc,EAAE,CAAC;oBAEtB,OAAO,EAAE,CAAC;gBACZ,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,IAAoB,EAAE,EAAE;oBAC7C,IAAI,CAAC;wBACH,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAkB,CAAC;wBAC7D,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;oBAC9B,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;oBAC5D,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAY,EAAE,MAAc,EAAE,EAAE;oBACnD,eAAM,CAAC,IAAI,CAAC,gCAAgC,IAAI,IAAI,MAAM,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;oBACzE,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;oBACzB,IAAI,CAAC,OAAO,EAAE,CAAC;oBAEf,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;wBACvD,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBAC3B,CAAC;yBAAM,CAAC;wBACN,eAAM,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;wBAClD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC,CAAC;oBACrE,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;oBACnC,eAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;oBACxC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;oBACzB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;oBAE1B,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;wBAC3B,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;oBACvC,CAAC;oBAED,MAAM,CAAC,KAAK,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC;YAEL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;gBAC9D,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAKO,qBAAqB;QAC3B,MAAM,iBAAiB,GAAG;YACxB,KAAK,EAAE,mBAAmB;YAC1B,IAAI,EAAE;gBACJ,QAAQ,EAAE,CAAC;gBACX,MAAM,EAAE,IAAI;gBACZ,OAAO,EAAE,WAAW;aACrB;SACF,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAC/B,CAAC;IAKO,kBAAkB;QACxB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,eAAM,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;YACrD,OAAO;QACT,CAAC;QAED,MAAM,gBAAgB,GAAG;YACvB,KAAK,EAAE,kBAAkB;YACzB,IAAI,EAAE;gBACJ,IAAI,EAAE,EAAE;gBACR,OAAO,EAAE,aAAa,IAAI,CAAC,UAAU,KAAK;aAC3C;SACF,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC5B,eAAM,CAAC,IAAI,CAAC,0BAA0B,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;IAC3D,CAAC;IAKO,aAAa,CAAC,OAAsB;QAC1C,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,OAAO,CAAC,CAAC;QAErD,QAAQ,OAAO,CAAC,KAAK,EAAE,CAAC;YACtB,KAAK,+BAA+B;gBAClC,eAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;gBAC7C,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACvB,MAAM;YAER,KAAK,+BAA+B;gBAClC,eAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;gBAClD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACxB,MAAM;YAER,KAAK,aAAa;gBAChB,eAAM,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;gBAC1C,MAAM;YAER,KAAK,+BAA+B;gBAClC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;gBAChC,MAAM;YAER,KAAK,8BAA8B;gBACjC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;gBAC/B,MAAM;YAER,KAAK,gCAAgC;gBACnC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;gBACjC,MAAM;YAER,KAAK,+BAA+B;gBAClC,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;gBACrC,MAAM;YAER,KAAK,6BAA6B;gBAChC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;gBACjC,MAAM;YAER,KAAK,gCAAgC;gBACnC,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;gBACpC,MAAM;YAER;gBACE,eAAM,CAAC,KAAK,CAAC,oBAAoB,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;gBAClD,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;gBACpC,MAAM;QACV,CAAC;IACH,CAAC;IAKO,iBAAiB,CAAC,OAAsB;QAC9C,IAAI,CAAC;YACH,MAAM,IAAI,GAAoB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAEvD,MAAM,WAAW,GAAgB;gBAC/B,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;aACxB,CAAC;YAEF,eAAM,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,OAAO,EAAE,YAAY,WAAW,CAAC,WAAW,EAAE,CAAC,CAAC;YAC5G,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QACpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAKO,gBAAgB,CAAC,OAAsB;QAC7C,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACtC,eAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAE,QAAQ,IAAI,QAAQ,CAAC,CAAC;YAC5F,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QACjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAKO,kBAAkB,CAAC,OAAsB;QAC/C,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACtC,eAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE,QAAQ,IAAI,QAAQ,CAAC,CAAC;YAChG,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAKO,sBAAsB,CAAC,OAAsB;QACnD,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACtC,eAAM,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC9C,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;QAClC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAKO,kBAAkB,CAAC,OAAsB;QAC/C,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACtC,eAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAClC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAKO,qBAAqB,CAAC,OAAsB;QAClD,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACtC,eAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACrC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;QACtC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAKO,IAAI,CAAC,OAAY;QACvB,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,KAAK,YAAS,CAAC,IAAI,EAAE,CAAC;YACrD,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;QACxC,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAKO,cAAc;QACpB,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC,GAAG,EAAE;YACxC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,aAAa,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;YAChD,CAAC;QACH,CAAC,EAAE,KAAK,CAAC,CAAC;IACZ,CAAC;IAKO,iBAAiB;QACvB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAE3D,eAAM,CAAC,IAAI,CAAC,mCAAmC,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,oBAAoB,OAAO,KAAK,IAAI,CAAC,CAAC;QAEpH,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBACtC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;oBAC5D,eAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;gBAC9C,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,EAAE,KAAK,CAAC,CAAC;IACZ,CAAC;IAKO,OAAO;QACb,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3B,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACtC,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;QACrC,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3B,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACrC,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;QACrC,CAAC;IACH,CAAC;IAKD,UAAU;QACR,eAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAC5C,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,OAAO,EAAE,CAAC;QAEf,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC;YACZ,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;YAChB,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC;QACtB,CAAC;IACH,CAAC;IAKD,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;CACF;AAhWD,sCAgWC"}