export declare enum LogLevel {
    ERROR = 0,
    WARN = 1,
    INFO = 2,
    DEBUG = 3
}
declare class Logger {
    private logLevel;
    private logFile;
    constructor();
    private getLogLevel;
    private ensureLogDirectory;
    private formatMessage;
    private writeToFile;
    private log;
    error(message: string, data?: any): void;
    warn(message: string, data?: any): void;
    info(message: string, data?: any): void;
    debug(message: string, data?: any): void;
    chatMessage(username: string, message: string, channel: string): void;
    commandUsed(username: string, command: string, args: string[], channel: string): void;
    moderationAction(action: string, target: string, moderator: string, reason?: string): void;
    botEvent(event: string, data?: any): void;
}
export declare const logger: Logger;
export {};
//# sourceMappingURL=logger.d.ts.map