# KickBot - Advanced Kick Streaming Bot

A comprehensive, feature-rich bot for Kick streaming platform with moderation, custom commands, interactive features, and a web dashboard.

## Features

### 🛡️ Moderation
- Auto-moderation (spam, caps, links)
- Banned words filter
- Timeout and ban commands
- User permission system
- Chat logging and analytics

### 🎮 Interactive Features
- Custom commands system
- Chat games (!roll, !8ball, !roulette)
- Song request system
- Giveaways and polls
- Loyalty points system

### 📊 Analytics & Management
- Web dashboard for configuration
- User activity tracking
- Command usage statistics
- Chat logs and moderation history
- Real-time bot status

### 🔧 Technical Features
- WebSocket connection to Kick chat
- SQLite database for data persistence
- RESTful API for web dashboard
- TypeScript for type safety
- Comprehensive logging system

## Quick Start

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd kickbot
```

2. Install dependencies:
```bash
npm install
```

3. Configure the bot:
```bash
cp .env.example .env
# Edit .env with your bot credentials and settings
```

4. Build and start:
```bash
npm run build
npm start
```

For development:
```bash
npm run dev
```

## Configuration

### Environment Variables

Copy `.env.example` to `.env` and configure:

- `BOT_USERNAME` - Your bot's Kick username
- `BOT_PASSWORD` - Your bot's password (if using password auth)
- `BOT_TOKEN` - Your bot's API token (preferred method)
- `DEFAULT_CHANNEL` - Channel to join on startup
- `WEB_PORT` - Port for web dashboard (default: 3000)

### Bot Features

Enable/disable features in `.env`:
- `ENABLE_MODERATION=true` - Auto-moderation features
- `ENABLE_COMMANDS=true` - Custom commands system
- `ENABLE_SONG_REQUESTS=true` - Song request functionality
- `ENABLE_GAMES=true` - Chat games and interactive features
- `ENABLE_ANALYTICS=true` - Analytics and logging

## Usage

### Web Dashboard
Access the web dashboard at `http://localhost:3000` (or your configured port) to:
- Manage custom commands
- Configure moderation settings
- View analytics and logs
- Monitor bot status

### Default Commands

#### Everyone
- `!help` - Show available commands
- `!uptime` - Show bot uptime
- `!roll [max]` - Roll a dice
- `!8ball <question>` - Ask the magic 8-ball

#### Moderators
- `!timeout <user> [duration] [reason]` - Timeout a user
- `!ban <user> [reason]` - Ban a user
- `!unban <user>` - Unban a user

### Custom Commands
Create custom commands through the web dashboard or by using moderator commands in chat.

## Development

### Project Structure
```
src/
├── bot/           # Core bot functionality
├── commands/      # Command handlers
├── moderation/    # Moderation features
├── database/      # Database models and queries
├── web/           # Web dashboard
├── utils/         # Utility functions
├── types/         # TypeScript type definitions
└── config/        # Configuration files
```

### Building
```bash
npm run build
```

### Testing
```bash
npm test
```

### Linting
```bash
npm run lint
npm run lint:fix
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Join our Discord server (link)
- Check the documentation wiki

## Roadmap

- [ ] Advanced analytics dashboard
- [ ] Plugin system for custom features
- [ ] Multi-channel support
- [ ] Integration with other streaming platforms
- [ ] Mobile app for bot management
- [ ] Advanced AI moderation features
