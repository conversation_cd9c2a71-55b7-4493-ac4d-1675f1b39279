# 🤖 KickBot - Complete Kick Streaming Bot

**The most comprehensive, feature-rich bot for Kick streaming platform!**

A professional-grade bot that rivals and surpasses popular solutions like Botrix and other Kick bots. Built with TypeScript, featuring advanced moderation, currency system, giveaways, custom commands, and much more!

## ✨ **What Makes This Bot Special**

### 🏆 **Beyond Botrix & Other Bots**
- **Complete Source Code** - Fully customizable, not a black box
- **Advanced Currency System** - Points, daily bonuses, transfers, leaderboards
- **Smart Giveaway System** - Automated with entry costs and winner selection
- **Professional Moderation** - Multi-layer protection with escalation
- **Database Persistence** - Never lose data, everything is saved
- **Extensible Architecture** - Easy to add new features
- **Production Ready** - Error handling, logging, graceful shutdown

### 🛡️ **Advanced Moderation System**
- **Spam Protection** - Configurable message limits and time windows
- **Caps Lock Filter** - Prevents excessive uppercase messages
- **Banned Words Filter** - Customizable word blacklist with auto-punishment
- **Link Protection** - Blocks unauthorized URLs, allows whitelisted domains
- **Repetitive Message Detection** - Stops message spam
- **Auto-Escalation** - Increasing punishments for repeat offenders
- **Real-time Logging** - All moderation actions logged to database

### 💰 **Currency & Economy System**
- **Passive Earning** - Users earn points for being active
- **Message Rewards** - Points for chatting (with cooldowns)
- **Follow/Subscribe Bonuses** - Reward new followers and subscribers
- **Daily Bonus System** - Streak multipliers for consecutive days
- **Point Transfers** - Users can send points to each other
- **Leaderboards** - Top users by points
- **Admin Controls** - Give/remove points, view balances

### 🎁 **Giveaway System**
- **Automated Giveaways** - Set duration and let the bot handle everything
- **Entry Costs** - Charge points for entries (optional)
- **Max Entries** - Limit number of participants
- **Random Winner Selection** - Fair and transparent
- **Entry Tracking** - See who entered and when
- **Multiple Giveaways** - Run several at once
- **Winner Announcements** - Automatic winner notifications

### 🎮 **Interactive Features**
- **Custom Commands** - Create unlimited commands with variables
- **Variable System** - 10+ built-in variables ({user}, {uptime}, {random}, etc.)
- **Chat Games** - Roll dice, magic 8-ball, and more
- **Permission System** - Role-based command access
- **Cooldown Management** - Prevent command spam
- **Command Statistics** - Track usage and popularity

### 📊 **Analytics & Data**
- **SQLite Database** - Persistent data storage
- **Chat Logging** - Complete message history
- **User Tracking** - Activity, points, violations, statistics
- **Command Analytics** - Usage statistics and trends
- **Moderation Logs** - Complete audit trail
- **Export/Import** - Backup and restore data

### 🌐 **Web Dashboard**
- **Beautiful Web Interface** - Modern, responsive design
- **Real-time Statistics** - Live bot status and metrics
- **Command Management** - Create, edit, delete commands through GUI
- **User Management** - View user profiles, points, activity
- **Moderation Control** - Manage banned words, view violation logs
- **Giveaway Management** - Create and manage giveaways
- **Chat Logs Viewer** - Browse chat history with search
- **Secure Authentication** - JWT-based login system
- **Mobile Responsive** - Works on all devices

## 🚀 **Quick Start (2 Minutes Setup)**

### **Prerequisites**
- Node.js 18+ installed
- A Kick account for your bot
- Basic terminal/command line knowledge

### **Super Quick Setup**
```bash
# 1. Clone and setup
git clone <repository-url>
cd kickbot
npm run setup

# 2. Configure your bot
# Edit .env file with your credentials:
# BOT_USERNAME=YourBotUsername
# BOT_PASSWORD=YourBotPassword
# DEFAULT_CHANNEL=YourChannelName

# 3. Start the complete bot
npm run start:full
```

### **Manual Setup**
```bash
# Install dependencies
npm install

# Copy and edit configuration
cp .env.example .env
# Edit .env with your bot credentials

# Build the project
npm run build

# Start basic bot
npm start

# OR start complete bot with all features
npm run start:full

# 🌐 Access Web Dashboard
# Open http://localhost:3000 in your browser
# Default login: admin / admin123
```

### **Development Mode**
```bash
# Run in development mode with auto-reload
npm run dev

# OR run complete bot in development
npm run dev:full
```

## Configuration

### Environment Variables

Copy `.env.example` to `.env` and configure:

- `BOT_USERNAME` - Your bot's Kick username
- `BOT_PASSWORD` - Your bot's password (if using password auth)
- `BOT_TOKEN` - Your bot's API token (preferred method)
- `DEFAULT_CHANNEL` - Channel to join on startup
- `WEB_PORT` - Port for web dashboard (default: 3000)

### Bot Features

Enable/disable features in `.env`:
- `ENABLE_MODERATION=true` - Auto-moderation features
- `ENABLE_COMMANDS=true` - Custom commands system
- `ENABLE_SONG_REQUESTS=true` - Song request functionality
- `ENABLE_GAMES=true` - Chat games and interactive features
- `ENABLE_ANALYTICS=true` - Analytics and logging

## Usage

### Web Dashboard
Access the web dashboard at `http://localhost:3000` (or your configured port) to:
- Manage custom commands
- Configure moderation settings
- View analytics and logs
- Monitor bot status

### Default Commands

#### Everyone
- `!help` - Show available commands
- `!uptime` - Show bot uptime
- `!roll [max]` - Roll a dice
- `!8ball <question>` - Ask the magic 8-ball

#### Moderators
- `!timeout <user> [duration] [reason]` - Timeout a user
- `!ban <user> [reason]` - Ban a user
- `!unban <user>` - Unban a user

### Custom Commands
Create custom commands through the web dashboard or by using moderator commands in chat.

## Development

### Project Structure
```
src/
├── bot/           # Core bot functionality
├── commands/      # Command handlers
├── moderation/    # Moderation features
├── database/      # Database models and queries
├── web/           # Web dashboard
├── utils/         # Utility functions
├── types/         # TypeScript type definitions
└── config/        # Configuration files
```

### Building
```bash
npm run build
```

### Testing
```bash
npm test
```

### Linting
```bash
npm run lint
npm run lint:fix
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Join our Discord server (link)
- Check the documentation wiki

## Roadmap

- [ ] Advanced analytics dashboard
- [ ] Plugin system for custom features
- [ ] Multi-channel support
- [ ] Integration with other streaming platforms
- [ ] Mobile app for bot management
- [ ] Advanced AI moderation features
#   k k b t o 
 
 