import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import path from 'path';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import { FullKickBot } from '../FullKickBot';
import { webConfig } from '../config';
import { logger } from '../utils/logger';
import { database } from '../database/Database';

export interface AuthRequest extends Request {
  user?: { username: string; isAdmin: boolean };
}

export class WebServer {
  private app: express.Application;
  private server?: any;
  private bot: FullKickBot;

  constructor(bot: FullKickBot) {
    this.bot = bot;
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
  }

  /**
   * Setup Express middleware
   */
  private setupMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
          scriptSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    }));

    this.app.use(cors({
      origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
      credentials: true,
    }));

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));

    // Static files
    this.app.use('/static', express.static(path.join(__dirname, '../web/public')));

    // Request logging
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      logger.debug(`${req.method} ${req.path}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      next();
    });
  }

  /**
   * Setup API routes
   */
  private setupRoutes(): void {
    // Health check
    this.app.get('/api/health', (req: Request, res: Response) => {
      res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        botRunning: this.bot.running
      });
    });

    // Authentication routes
    this.app.post('/api/auth/login', this.handleLogin.bind(this));
    this.app.post('/api/auth/logout', this.handleLogout.bind(this));
    this.app.get('/api/auth/me', this.authenticateToken.bind(this), this.handleMe.bind(this));

    // Protected routes
    this.app.use('/api', this.authenticateToken.bind(this));

    // Bot status and control
    this.app.get('/api/bot/status', this.getBotStatus.bind(this));
    this.app.get('/api/bot/stats', this.getBotStats.bind(this));
    this.app.post('/api/bot/restart', this.restartBot.bind(this));

    // Commands management
    this.app.get('/api/commands', this.getCommands.bind(this));
    this.app.post('/api/commands', this.createCommand.bind(this));
    this.app.put('/api/commands/:name', this.updateCommand.bind(this));
    this.app.delete('/api/commands/:name', this.deleteCommand.bind(this));

    // Moderation management
    this.app.get('/api/moderation/stats', this.getModerationStats.bind(this));
    this.app.get('/api/moderation/logs', this.getModerationLogs.bind(this));
    this.app.post('/api/moderation/banned-words', this.addBannedWord.bind(this));
    this.app.delete('/api/moderation/banned-words/:word', this.removeBannedWord.bind(this));

    // Currency system
    this.app.get('/api/currency/stats', this.getCurrencyStats.bind(this));
    this.app.get('/api/currency/leaderboard', this.getCurrencyLeaderboard.bind(this));
    this.app.post('/api/currency/give', this.givePoints.bind(this));

    // Giveaways
    this.app.get('/api/giveaways', this.getGiveaways.bind(this));
    this.app.post('/api/giveaways', this.createGiveaway.bind(this));
    this.app.post('/api/giveaways/:id/end', this.endGiveaway.bind(this));
    this.app.delete('/api/giveaways/:id', this.cancelGiveaway.bind(this));

    // Users
    this.app.get('/api/users', this.getUsers.bind(this));
    this.app.get('/api/users/:username', this.getUser.bind(this));

    // Chat logs
    this.app.get('/api/chat/logs', this.getChatLogs.bind(this));

    // Serve dashboard HTML
    this.app.get('/', (req: Request, res: Response) => {
      res.sendFile(path.join(__dirname, '../web/public/index.html'));
    });

    // Catch-all for SPA routing
    this.app.get('*', (req: Request, res: Response) => {
      if (req.path.startsWith('/api/')) {
        res.status(404).json({ error: 'API endpoint not found' });
      } else {
        res.sendFile(path.join(__dirname, '../web/public/index.html'));
      }
    });

    // Error handling
    this.app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
      logger.error('Web server error:', err);
      res.status(500).json({ error: 'Internal server error' });
    });
  }

  /**
   * Authentication middleware
   */
  private authenticateToken(req: AuthRequest, res: Response, next: NextFunction): void {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      res.status(401).json({ error: 'Access token required' });
      return;
    }

    jwt.verify(token, webConfig.jwtSecret, (err: any, user: any) => {
      if (err) {
        res.status(403).json({ error: 'Invalid token' });
        return;
      }
      req.user = user;
      next();
    });
  }

  /**
   * Login handler
   */
  private async handleLogin(req: Request, res: Response): Promise<void> {
    try {
      const { username, password } = req.body;

      if (!username || !password) {
        res.status(400).json({ error: 'Username and password required' });
        return;
      }

      // Simple admin authentication (in production, use proper user management)
      if (username === webConfig.adminUsername) {
        const isValid = await bcrypt.compare(password, await bcrypt.hash(webConfig.adminPassword, 10));

        if (isValid || password === webConfig.adminPassword) {
          const token = jwt.sign(
            { username, isAdmin: true },
            webConfig.jwtSecret,
            { expiresIn: '24h' }
          );

          res.json({
            token,
            user: { username, isAdmin: true },
            expiresIn: '24h'
          });
          return;
        }
      }

      res.status(401).json({ error: 'Invalid credentials' });
    } catch (error) {
      logger.error('Login error:', error);
      res.status(500).json({ error: 'Login failed' });
    }
  }

  /**
   * Logout handler
   */
  private handleLogout(req: Request, res: Response): void {
    // In a real implementation, you might want to blacklist the token
    res.json({ message: 'Logged out successfully' });
  }

  /**
   * Get current user info
   */
  private handleMe(req: AuthRequest, res: Response): void {
    res.json({ user: req.user });
  }

  /**
   * Get bot status
   */
  private getBotStatus(req: AuthRequest, res: Response): void {
    const status = {
      running: this.bot.running,
      connected: this.bot.botInstance.connected,
      uptime: this.bot.botInstance.getStats().uptimeFormatted,
      channel: this.bot.botInstance.currentChannel?.slug,
    };
    res.json(status);
  }

  /**
   * Get bot statistics
   */
  private async getBotStats(req: AuthRequest, res: Response): Promise<void> {
    try {
      const stats = await this.bot.getStats();
      res.json(stats);
    } catch (error) {
      logger.error('Failed to get bot stats:', error);
      res.status(500).json({ error: 'Failed to get statistics' });
    }
  }

  /**
   * Restart bot
   */
  private async restartBot(req: AuthRequest, res: Response): Promise<void> {
    try {
      logger.info(`Bot restart requested by ${req.user?.username}`);

      // Stop and start the bot
      await this.bot.stop();
      await this.bot.start();

      res.json({ message: 'Bot restarted successfully' });
    } catch (error) {
      logger.error('Failed to restart bot:', error);
      res.status(500).json({ error: 'Failed to restart bot' });
    }
  }

  /**
   * Start the web server
   */
  async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.server = this.app.listen(webConfig.port, () => {
          logger.info(`🌐 Web dashboard started on http://localhost:${webConfig.port}`);
          resolve();
        });

        this.server.on('error', (error: Error) => {
          logger.error('Web server error:', error);
          reject(error);
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Get commands
   */
  private async getCommands(req: AuthRequest, res: Response): Promise<void> {
    try {
      const commands = await database.getAllCommands();
      res.json(commands);
    } catch (error) {
      logger.error('Failed to get commands:', error);
      res.status(500).json({ error: 'Failed to get commands' });
    }
  }

  /**
   * Create command
   */
  private async createCommand(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { name, response, description, cooldown, permission } = req.body;

      if (!name || !response) {
        res.status(400).json({ error: 'Name and response are required' });
        return;
      }

      const success = await this.bot.features.customCommands?.createCommand(name, response, {
        description,
        cooldown,
        permission,
        createdBy: req.user?.username || 'web-admin',
      });

      if (success) {
        res.json({ message: 'Command created successfully' });
      } else {
        res.status(400).json({ error: 'Failed to create command' });
      }
    } catch (error) {
      logger.error('Failed to create command:', error);
      res.status(500).json({ error: 'Failed to create command' });
    }
  }

  /**
   * Update command
   */
  private async updateCommand(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { name } = req.params;
      const updates = req.body;

      const success = await this.bot.features.customCommands?.updateCommand(name, updates);

      if (success) {
        res.json({ message: 'Command updated successfully' });
      } else {
        res.status(404).json({ error: 'Command not found' });
      }
    } catch (error) {
      logger.error('Failed to update command:', error);
      res.status(500).json({ error: 'Failed to update command' });
    }
  }

  /**
   * Delete command
   */
  private async deleteCommand(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { name } = req.params;

      const success = await this.bot.features.customCommands?.deleteCommand(name);

      if (success) {
        res.json({ message: 'Command deleted successfully' });
      } else {
        res.status(404).json({ error: 'Command not found' });
      }
    } catch (error) {
      logger.error('Failed to delete command:', error);
      res.status(500).json({ error: 'Failed to delete command' });
    }
  }

  /**
   * Get moderation stats
   */
  private getModerationStats(req: AuthRequest, res: Response): void {
    try {
      const stats = this.bot.features.moderation?.getModerationStats();
      res.json(stats || {});
    } catch (error) {
      logger.error('Failed to get moderation stats:', error);
      res.status(500).json({ error: 'Failed to get moderation stats' });
    }
  }

  /**
   * Get moderation logs
   */
  private async getModerationLogs(req: AuthRequest, res: Response): Promise<void> {
    try {
      const limit = parseInt(req.query.limit as string) || 50;
      const logs = await database.db.all(`
        SELECT * FROM moderation_logs
        ORDER BY timestamp DESC
        LIMIT ?
      `, [limit]);

      res.json(logs);
    } catch (error) {
      logger.error('Failed to get moderation logs:', error);
      res.status(500).json({ error: 'Failed to get moderation logs' });
    }
  }

  /**
   * Add banned word
   */
  private addBannedWord(req: AuthRequest, res: Response): void {
    try {
      const { word } = req.body;

      if (!word) {
        res.status(400).json({ error: 'Word is required' });
        return;
      }

      this.bot.features.moderation?.addBannedWord(word);
      res.json({ message: 'Banned word added successfully' });
    } catch (error) {
      logger.error('Failed to add banned word:', error);
      res.status(500).json({ error: 'Failed to add banned word' });
    }
  }

  /**
   * Remove banned word
   */
  private removeBannedWord(req: AuthRequest, res: Response): void {
    try {
      const { word } = req.params;

      const success = this.bot.features.moderation?.removeBannedWord(word);

      if (success) {
        res.json({ message: 'Banned word removed successfully' });
      } else {
        res.status(404).json({ error: 'Banned word not found' });
      }
    } catch (error) {
      logger.error('Failed to remove banned word:', error);
      res.status(500).json({ error: 'Failed to remove banned word' });
    }
  }

  /**
   * Get currency stats
   */
  private async getCurrencyStats(req: AuthRequest, res: Response): Promise<void> {
    try {
      const stats = await this.bot.features.currency?.getStats();
      res.json(stats || {});
    } catch (error) {
      logger.error('Failed to get currency stats:', error);
      res.status(500).json({ error: 'Failed to get currency stats' });
    }
  }

  /**
   * Get currency leaderboard
   */
  private async getCurrencyLeaderboard(req: AuthRequest, res: Response): Promise<void> {
    try {
      const limit = parseInt(req.query.limit as string) || 10;
      const leaderboard = await this.bot.features.currency?.getLeaderboard(limit);
      res.json(leaderboard || []);
    } catch (error) {
      logger.error('Failed to get currency leaderboard:', error);
      res.status(500).json({ error: 'Failed to get currency leaderboard' });
    }
  }

  /**
   * Give points to user
   */
  private async givePoints(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { username, amount, reason } = req.body;

      if (!username || !amount) {
        res.status(400).json({ error: 'Username and amount are required' });
        return;
      }

      const success = await this.bot.features.currency?.addPoints(
        username,
        amount,
        reason || `Admin gift by ${req.user?.username}`
      );

      if (success) {
        res.json({ message: 'Points given successfully' });
      } else {
        res.status(400).json({ error: 'Failed to give points' });
      }
    } catch (error) {
      logger.error('Failed to give points:', error);
      res.status(500).json({ error: 'Failed to give points' });
    }
  }

  /**
   * Get giveaways
   */
  private async getGiveaways(req: AuthRequest, res: Response): Promise<void> {
    try {
      const activeGiveaways = this.bot.features.giveaways?.getActiveGiveaways() || [];
      const stats = await this.bot.features.giveaways?.getStats();

      res.json({
        active: activeGiveaways,
        stats: stats || {}
      });
    } catch (error) {
      logger.error('Failed to get giveaways:', error);
      res.status(500).json({ error: 'Failed to get giveaways' });
    }
  }

  /**
   * Create giveaway
   */
  private async createGiveaway(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { title, prize, duration, description, maxEntries, entryCost } = req.body;

      if (!title || !prize || !duration) {
        res.status(400).json({ error: 'Title, prize, and duration are required' });
        return;
      }

      const result = await this.bot.features.giveaways?.createGiveaway(
        title,
        prize,
        duration,
        req.user?.username || 'web-admin',
        { description, maxEntries, entryCost }
      );

      if (result?.success) {
        res.json({ message: 'Giveaway created successfully', giveaway: result.giveaway });
      } else {
        res.status(400).json({ error: result?.message || 'Failed to create giveaway' });
      }
    } catch (error) {
      logger.error('Failed to create giveaway:', error);
      res.status(500).json({ error: 'Failed to create giveaway' });
    }
  }

  /**
   * End giveaway
   */
  private async endGiveaway(req: AuthRequest, res: Response): Promise<void> {
    try {
      const giveawayId = parseInt(req.params.id);

      const result = await this.bot.features.giveaways?.endGiveaway(giveawayId, req.user?.username);

      if (result?.success) {
        res.json({ message: result.message, winner: result.winner });
      } else {
        res.status(400).json({ error: result?.message || 'Failed to end giveaway' });
      }
    } catch (error) {
      logger.error('Failed to end giveaway:', error);
      res.status(500).json({ error: 'Failed to end giveaway' });
    }
  }

  /**
   * Cancel giveaway
   */
  private async cancelGiveaway(req: AuthRequest, res: Response): Promise<void> {
    try {
      const giveawayId = parseInt(req.params.id);

      const result = await this.bot.features.giveaways?.cancelGiveaway(giveawayId, req.user?.username || 'web-admin');

      if (result?.success) {
        res.json({ message: result.message });
      } else {
        res.status(400).json({ error: result?.message || 'Failed to cancel giveaway' });
      }
    } catch (error) {
      logger.error('Failed to cancel giveaway:', error);
      res.status(500).json({ error: 'Failed to cancel giveaway' });
    }
  }

  /**
   * Get users
   */
  private async getUsers(req: AuthRequest, res: Response): Promise<void> {
    try {
      const limit = parseInt(req.query.limit as string) || 50;
      const users = await database.db.all(`
        SELECT username, display_name, points, messages_sent, commands_used,
               last_seen, is_follower, is_subscriber, is_vip, is_moderator
        FROM users
        ORDER BY last_seen DESC
        LIMIT ?
      `, [limit]);

      res.json(users);
    } catch (error) {
      logger.error('Failed to get users:', error);
      res.status(500).json({ error: 'Failed to get users' });
    }
  }

  /**
   * Get specific user
   */
  private async getUser(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { username } = req.params;
      const user = await database.getUser(username);

      if (user) {
        res.json(user);
      } else {
        res.status(404).json({ error: 'User not found' });
      }
    } catch (error) {
      logger.error('Failed to get user:', error);
      res.status(500).json({ error: 'Failed to get user' });
    }
  }

  /**
   * Get chat logs
   */
  private async getChatLogs(req: AuthRequest, res: Response): Promise<void> {
    try {
      const limit = parseInt(req.query.limit as string) || 100;
      const logs = await database.db.all(`
        SELECT username, message, timestamp, message_type
        FROM chat_logs
        ORDER BY timestamp DESC
        LIMIT ?
      `, [limit]);

      res.json(logs);
    } catch (error) {
      logger.error('Failed to get chat logs:', error);
      res.status(500).json({ error: 'Failed to get chat logs' });
    }
  }

  /**
   * Stop the web server
   */
  async stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          logger.info('Web server stopped');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
}