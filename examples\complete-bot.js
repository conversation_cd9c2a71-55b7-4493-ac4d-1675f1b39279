// Complete KickBot Example - Production Ready
// This example shows how to set up a full-featured Kick bot with all systems

const { FullKickBot } = require('../dist/FullKickBot');
const { logger } = require('../dist/utils/logger');

// Complete bot configuration
const fullBotConfig = {
  // Bot credentials and basic settings
  bot: {
    username: process.env.BOT_USERNAME || 'YourBotUsername',
    password: process.env.BOT_PASSWORD, // or use token
    token: process.env.BOT_TOKEN,
    channel: process.env.DEFAULT_CHANNEL || 'YourChannelName',
    prefix: process.env.COMMAND_PREFIX || '!',
  },

  // Feature toggles - enable/disable major features
  features: {
    moderation: true,        // Auto-moderation system
    customCommands: true,    // Custom command creation
    currency: true,          // Points/currency system
    giveaways: true,         // Giveaway system
    analytics: true,         // Chat logging and analytics
    webDashboard: true,      // Web dashboard
  },

  // Currency system configuration
  currency: {
    enabled: true,
    currencyName: 'points',
    currencySymbol: '💎',
    
    // Passive earning - users earn points for being active
    passiveEarning: {
      enabled: true,
      amount: 10,              // Points earned
      interval: 10,            // Every 10 minutes
      requireActive: true,     // Must be chatting to earn
    },
    
    // Message rewards - earn points for chatting
    messageRewards: {
      enabled: true,
      amount: 1,               // Points per message
      cooldown: 60,            // 60 second cooldown between rewards
    },
    
    // Follow/subscribe rewards
    followRewards: {
      enabled: true,
      amount: 100,             // Points for following
    },
    subscribeRewards: {
      enabled: true,
      amount: 500,             // Points for subscribing
    },
    
    // Daily bonus system
    dailyBonus: {
      enabled: true,
      amount: 50,              // Base daily bonus
      streakMultiplier: 0.1,   // 10% bonus per consecutive day
    },
  },

  // Moderation system configuration
  moderation: {
    enabled: true,
    
    // Spam protection
    spamProtection: {
      enabled: true,
      maxMessages: 5,          // Max messages
      timeWindow: 30,          // In 30 seconds
      punishment: 'timeout',   // timeout or ban
      duration: 5,             // 5 minutes
    },
    
    // Caps lock filter
    capsFilter: {
      enabled: true,
      threshold: 70,           // 70% caps triggers filter
      minLength: 10,           // Minimum message length to check
      punishment: 'timeout',   // timeout, delete, or warn
      duration: 2,             // 2 minutes
    },
    
    // Banned words filter
    bannedWords: {
      enabled: true,
      words: [                 // Add your banned words here
        'spam',
        'scam',
        'hack',
        'cheat',
      ],
      punishment: 'timeout',   // timeout, ban, or delete
      duration: 10,            // 10 minutes
    },
    
    // Link protection
    linkProtection: {
      enabled: true,
      allowedDomains: [        // Allowed domains
        'kick.com',
        'youtube.com',
        'twitch.tv',
        'discord.gg',
        'twitter.com',
      ],
      punishment: 'delete',    // timeout, delete, or warn
      duration: 5,             // 5 minutes
    },
    
    // Repetitive message detection
    repetitiveMessages: {
      enabled: true,
      maxRepeats: 3,           // Max identical messages
      timeWindow: 60,          // In 60 seconds
      punishment: 'timeout',   // timeout or delete
      duration: 5,             // 5 minutes
    },
    
    // Auto timeout settings
    autoTimeout: {
      enabled: true,
      escalation: true,        // Increase punishment for repeat offenders
    },
  },
};

async function startCompleteBot() {
  try {
    console.log('🚀 Starting Complete KickBot...');
    console.log('📋 Configuration:');
    console.log(`   • Channel: ${fullBotConfig.bot.channel}`);
    console.log(`   • Bot: ${fullBotConfig.bot.username}`);
    console.log(`   • Features: ${Object.entries(fullBotConfig.features).filter(([k, v]) => v).map(([k]) => k).join(', ')}`);

    // Create the full bot instance
    const kickBot = new FullKickBot(fullBotConfig);

    // Setup additional event handlers
    setupAdvancedEventHandlers(kickBot);

    // Start the bot
    await kickBot.start();

    console.log('✅ Complete KickBot is now running!');
    console.log('📝 Available commands:');
    console.log('   Basic: !help, !uptime, !roll, !8ball');
    console.log('   Custom: !addcom, !editcom, !delcom, !commands');
    console.log('   Moderation: !timeout, !ban, !unban, !moderation, !addword');
    console.log('   Currency: !points, !transfer, !daily, !leaderboard');
    console.log('   Giveaways: !giveaway, !enter, !giveawayinfo');
    console.log('   Chat Control: !slow, !followers, !subs');
    console.log('');
    console.log('🌐 Web Dashboard: http://localhost:3000');
    console.log('   Username: admin');
    console.log('   Password: admin123');
    console.log('');
    console.log('🎮 Bot is ready for action! Press Ctrl+C to stop.');

    // Display stats every 30 minutes
    setInterval(async () => {
      try {
        const stats = await kickBot.getStats();
        console.log('📊 Bot Statistics:');
        console.log(`   • Uptime: ${stats.bot.uptimeFormatted}`);
        console.log(`   • Messages: ${stats.bot.messageCount}`);
        console.log(`   • Commands: ${stats.bot.commandCount}`);
        console.log(`   • Users: ${stats.users.totalUsers}`);
        if (stats.features.currency) {
          console.log(`   • Points in circulation: ${stats.features.currency.totalPointsInCirculation}`);
        }
        if (stats.features.giveaways) {
          console.log(`   • Active giveaways: ${stats.features.giveaways.activeGiveaways}`);
        }
      } catch (error) {
        console.error('Failed to get stats:', error);
      }
    }, 30 * 60 * 1000); // 30 minutes

    // Graceful shutdown
    process.on('SIGINT', async () => {
      console.log('\n🛑 Shutting down Complete KickBot...');
      try {
        await kickBot.stop();
        console.log('✅ Bot stopped successfully');
        process.exit(0);
      } catch (error) {
        console.error('❌ Error during shutdown:', error);
        process.exit(1);
      }
    });

  } catch (error) {
    console.error('❌ Failed to start Complete KickBot:', error);
    process.exit(1);
  }
}

function setupAdvancedEventHandlers(kickBot) {
  const bot = kickBot.botInstance;
  const features = kickBot.features;

  // Welcome new followers with points
  bot.on('new_follower', async (data) => {
    if (features.currency) {
      await features.currency.addPoints(data.username, 100, 'New follower bonus');
      await bot.sendMessage(`@${data.username} Welcome! You've earned 100 points for following! 💎`);
    }
  });

  // Announce giveaway winners
  if (features.giveaways) {
    features.giveaways.on('giveaway_ended', async (data) => {
      // Give winner bonus points
      if (features.currency) {
        await features.currency.addPoints(data.winner, 500, 'Giveaway winner bonus');
      }
    });
  }

  // Currency milestones
  if (features.currency) {
    features.currency.on('points_added', async (data) => {
      // Announce milestones
      const milestones = [1000, 5000, 10000, 25000, 50000, 100000];
      const milestone = milestones.find(m => 
        data.newTotal >= m && (data.newTotal - data.amount) < m
      );
      
      if (milestone) {
        await bot.sendMessage(`🎉 Congratulations @${data.username}! You've reached ${milestone} points! 🎉`);
      }
    });
  }

  // Auto-create welcome command if it doesn't exist
  setTimeout(async () => {
    if (features.customCommands) {
      const existingCommand = features.customCommands.getCustomCommand('welcome');
      if (!existingCommand) {
        await features.customCommands.createCommand(
          'welcome',
          'Welcome to the stream, {user}! Type !help for commands and !points to check your balance! 🎮',
          {
            description: 'Welcome new viewers',
            cooldown: 30,
            createdBy: 'system',
          }
        );
        console.log('✅ Auto-created welcome command');
      }
    }
  }, 5000);

  // Log important events
  bot.on('message', (message) => {
    // Log VIP/Moderator messages differently
    if (message.sender.is_vip || message.sender.is_moderator) {
      console.log(`👑 [${message.sender.username}]: ${message.content}`);
    }
  });

  // Handle moderation escalation
  if (features.moderation) {
    features.moderation.on('punishment_executed', async (data) => {
      // Notify moderators of serious violations
      if (data.punishment.type === 'ban') {
        await bot.sendMessage(`🚨 User ${data.user.username} has been banned for: ${data.violations.join(', ')}`);
      }
    });
  }
}

// Auto-start if this file is run directly
if (require.main === module) {
  startCompleteBot();
}

module.exports = {
  startCompleteBot,
  fullBotConfig,
};
