export interface KickChannel {
    id: number;
    user_id: number;
    slug: string;
    is_banned: boolean;
    playback_url: string;
    name_updated_at: string | null;
    vod_enabled: boolean;
    subscription_enabled: boolean;
    can_host: boolean;
    user: {
        id: number;
        username: string;
        agreed_to_terms: boolean;
        email_verified_at: string | null;
        bio: string;
        country: string;
        state: string;
        city: string;
        instagram: string;
        twitter: string;
        youtube: string;
        discord: string;
        tiktok: string;
        facebook: string;
    };
    chatroom: {
        id: number;
        chatable_type: string;
        channel_id: number;
        created_at: string;
        updated_at: string;
        chat_mode_old: string;
        chat_mode: string;
        slow_mode: boolean;
        chatable_id: number;
        followers_mode: boolean;
        subscribers_mode: boolean;
        emotes_mode: boolean;
        message_interval: number;
        following_min_duration: number;
    };
}
export interface KickUser {
    id: number;
    username: string;
    slug: string;
    follower_badges: any[];
    subscriber_badges: any[];
    is_staff: boolean;
    is_channel_owner: boolean;
    is_moderator: boolean;
    is_vip: boolean;
    is_subscriber: boolean;
    is_follower: boolean;
    is_banned: boolean;
    profile_pic: string;
}
export interface ChatroomSettings {
    id: number;
    slow_mode: boolean;
    followers_mode: boolean;
    subscribers_mode: boolean;
    emotes_mode: boolean;
    message_interval: number;
    following_min_duration: number;
}
declare class KickAPI {
    private api;
    private authToken?;
    private userId?;
    constructor();
    authenticate(email: string, password: string): Promise<boolean>;
    private authenticateAlternative;
    private authenticateFallback;
    getChannel(channelSlug: string): Promise<KickChannel | null>;
    getUser(username: string): Promise<KickUser | null>;
    sendMessage(chatroomId: number, message: string): Promise<boolean>;
    banUser(chatroomId: number, username: string, permanent?: boolean): Promise<boolean>;
    timeoutUser(chatroomId: number, username: string, duration: number): Promise<boolean>;
    unbanUser(chatroomId: number, username: string): Promise<boolean>;
    getChatroomSettings(chatroomId: number): Promise<ChatroomSettings | null>;
    updateChatroomSettings(chatroomId: number, settings: Partial<ChatroomSettings>): Promise<boolean>;
    get currentUserId(): number | undefined;
    get isAuthenticated(): boolean;
}
export declare const kickAPI: KickAPI;
export {};
//# sourceMappingURL=kickApi.d.ts.map