{"version": 3, "file": "AuthManager.js", "sourceRoot": "", "sources": ["../../src/auth/AuthManager.ts"], "names": [], "mappings": ";;;AAAA,4CAAyC;AACzC,4CAAyC;AAezC,MAAa,WAAW;IAAxB;QACU,oBAAe,GAAG,KAAK,CAAC;IA2KlC,CAAC;IAnKC,KAAK,CAAC,YAAY,CAAC,WAA4B;QAC7C,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;YAEvD,IAAI,WAAW,CAAC,KAAK,EAAE,CAAC;gBAEtB,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAC7D,CAAC;iBAAM,IAAI,WAAW,CAAC,KAAK,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;gBAErD,OAAO,MAAM,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC;YACtF,CAAC;iBAAM,CAAC;gBACN,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,8CAA8C;iBACtD,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC9C,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI,uBAAuB;aAChD,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,qBAAqB,CAAC,KAAa;QAC/C,IAAI,CAAC;YAEH,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YAMvB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAE5B,eAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YACrD,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,IAAI,CAAC,aAAa;gBAC1B,QAAQ,EAAE,IAAI,CAAC,eAAe;aAC/B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACpD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,eAAe;aACvB,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,wBAAwB,CAAC,KAAa,EAAE,QAAgB;QACpE,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,iBAAO,CAAC,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YAE5D,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;gBAC5B,IAAI,CAAC,aAAa,GAAG,iBAAO,CAAC,aAAa,CAAC;gBAE3C,eAAM,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;gBAC9D,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,MAAM,EAAE,IAAI,CAAC,aAAa;oBAC1B,QAAQ,EAAE,IAAI,CAAC,eAAe;iBAC/B,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,2BAA2B;iBACnC,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI,uBAAuB;aAChD,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,MAAM;QACJ,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;QAC/B,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;QACjC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAE3B,eAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;IACzC,CAAC;IAKD,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAKD,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAKD,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAKD,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAKD,KAAK,CAAC,WAAW;QACf,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YAGH,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YAGH,OAAO,iBAAO,CAAC,eAAe,CAAC;QACjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AA5KD,kCA4KC;AAEY,QAAA,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC"}