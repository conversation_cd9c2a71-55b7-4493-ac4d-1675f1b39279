"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomCommandManager = void 0;
const events_1 = require("events");
const types_1 = require("../types");
const logger_1 = require("../utils/logger");
class CustomCommandManager extends events_1.EventEmitter {
    constructor(commandHandler) {
        super();
        this.customCommands = new Map();
        this.variables = new Map();
        this.commandHandler = commandHandler;
        this.setupDefaultVariables();
    }
    setupDefaultVariables() {
        this.registerVariable({
            name: 'user',
            description: 'The username of the person who used the command',
            example: '{user}',
            resolver: (context) => context.user.username,
        });
        this.registerVariable({
            name: 'channel',
            description: 'The current channel name',
            example: '{channel}',
            resolver: (context) => context.bot.currentChannel?.slug || 'unknown',
        });
        this.registerVariable({
            name: 'uptime',
            description: 'Bot uptime',
            example: '{uptime}',
            resolver: (context) => context.bot.getStats().uptimeFormatted,
        });
        this.registerVariable({
            name: 'count',
            description: 'Number of times this command has been used',
            example: '{count}',
            resolver: (context) => {
                const customCmd = this.getCustomCommand(context.command.name);
                return customCmd ? customCmd.useCount.toString() : '0';
            },
        });
        this.registerVariable({
            name: 'random',
            description: 'Random number between 1-100',
            example: '{random}',
            resolver: () => Math.floor(Math.random() * 100 + 1).toString(),
        });
        this.registerVariable({
            name: 'args',
            description: 'All arguments passed to the command',
            example: '{args}',
            resolver: (context) => context.args.join(' '),
        });
        this.registerVariable({
            name: 'arg1',
            description: 'First argument passed to the command',
            example: '{arg1}',
            resolver: (context) => context.args[0] || '',
        });
        this.registerVariable({
            name: 'arg2',
            description: 'Second argument passed to the command',
            example: '{arg2}',
            resolver: (context) => context.args[1] || '',
        });
        this.registerVariable({
            name: 'arg3',
            description: 'Third argument passed to the command',
            example: '{arg3}',
            resolver: (context) => context.args[2] || '',
        });
        this.registerVariable({
            name: 'time',
            description: 'Current time',
            example: '{time}',
            resolver: () => new Date().toLocaleTimeString(),
        });
        this.registerVariable({
            name: 'date',
            description: 'Current date',
            example: '{date}',
            resolver: () => new Date().toLocaleDateString(),
        });
    }
    registerVariable(variable) {
        this.variables.set(variable.name, variable);
        logger_1.logger.debug(`Registered command variable: ${variable.name}`);
    }
    async createCommand(name, response, options) {
        try {
            if (this.commandHandler.getCommand(name) || this.customCommands.has(name)) {
                logger_1.logger.warn(`Command ${name} already exists`);
                return false;
            }
            if (!this.isValidCommandName(name)) {
                logger_1.logger.warn(`Invalid command name: ${name}`);
                return false;
            }
            const customCommand = {
                id: this.generateCommandId(),
                name,
                description: options.description || `Custom command: ${name}`,
                usage: `!${name}`,
                aliases: options.aliases || [],
                cooldown: options.cooldown || 5,
                permission: options.permission || types_1.PermissionLevel.EVERYONE,
                enabled: options.enabled !== false,
                response,
                variables: this.extractVariables(response),
                isCustom: true,
                useCount: 0,
                created_by: options.createdBy,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
            };
            this.customCommands.set(name, customCommand);
            this.commandHandler.registerCommand(customCommand, async (context) => {
                await this.executeCustomCommand(context, customCommand);
            });
            logger_1.logger.info(`Created custom command: ${name}`);
            this.emit('command_created', customCommand);
            return true;
        }
        catch (error) {
            logger_1.logger.error(`Failed to create command ${name}:`, error);
            return false;
        }
    }
    async updateCommand(name, updates) {
        try {
            const customCommand = this.customCommands.get(name);
            if (!customCommand) {
                logger_1.logger.warn(`Custom command ${name} not found`);
                return false;
            }
            if (updates.response !== undefined) {
                customCommand.response = updates.response;
                customCommand.variables = this.extractVariables(updates.response);
            }
            if (updates.description !== undefined)
                customCommand.description = updates.description;
            if (updates.aliases !== undefined)
                customCommand.aliases = updates.aliases;
            if (updates.cooldown !== undefined)
                customCommand.cooldown = updates.cooldown;
            if (updates.permission !== undefined)
                customCommand.permission = updates.permission;
            if (updates.enabled !== undefined)
                customCommand.enabled = updates.enabled;
            customCommand.updated_at = new Date().toISOString();
            this.commandHandler.updateCommand(name, customCommand);
            logger_1.logger.info(`Updated custom command: ${name}`);
            this.emit('command_updated', customCommand);
            return true;
        }
        catch (error) {
            logger_1.logger.error(`Failed to update command ${name}:`, error);
            return false;
        }
    }
    async deleteCommand(name) {
        try {
            const customCommand = this.customCommands.get(name);
            if (!customCommand) {
                logger_1.logger.warn(`Custom command ${name} not found`);
                return false;
            }
            this.customCommands.delete(name);
            this.commandHandler.unregisterCommand(name);
            logger_1.logger.info(`Deleted custom command: ${name}`);
            this.emit('command_deleted', customCommand);
            return true;
        }
        catch (error) {
            logger_1.logger.error(`Failed to delete command ${name}:`, error);
            return false;
        }
    }
    async executeCustomCommand(context, customCommand) {
        try {
            customCommand.useCount++;
            customCommand.lastUsed = new Date().toISOString();
            const processedResponse = await this.processVariables(customCommand.response, context);
            await context.bot.replyToMessage(context.message, processedResponse);
            this.emit('command_executed', {
                command: customCommand,
                user: context.user.username,
                response: processedResponse,
            });
        }
        catch (error) {
            logger_1.logger.error(`Error executing custom command ${customCommand.name}:`, error);
            await context.bot.replyToMessage(context.message, 'An error occurred while executing the command.');
        }
    }
    async processVariables(response, context) {
        let processedResponse = response;
        const variableMatches = response.match(/\{([^}]+)\}/g);
        if (!variableMatches) {
            return processedResponse;
        }
        for (const match of variableMatches) {
            const variableName = match.slice(1, -1);
            const variable = this.variables.get(variableName);
            if (variable) {
                try {
                    const value = await variable.resolver(context);
                    processedResponse = processedResponse.replace(match, value);
                }
                catch (error) {
                    logger_1.logger.error(`Error resolving variable ${variableName}:`, error);
                    processedResponse = processedResponse.replace(match, `{${variableName}}`);
                }
            }
        }
        return processedResponse;
    }
    extractVariables(response) {
        const variableMatches = response.match(/\{([^}]+)\}/g);
        if (!variableMatches) {
            return [];
        }
        return variableMatches.map(match => match.slice(1, -1));
    }
    isValidCommandName(name) {
        return /^[a-zA-Z0-9_]+$/.test(name) && name.length >= 2 && name.length <= 50;
    }
    generateCommandId() {
        return `cmd_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    getCustomCommand(name) {
        return this.customCommands.get(name);
    }
    getAllCustomCommands() {
        return Array.from(this.customCommands.values());
    }
    getCustomCommandsByPermission(permissionLevel) {
        return this.getAllCustomCommands().filter(cmd => cmd.permission <= permissionLevel);
    }
    getAvailableVariables() {
        return Array.from(this.variables.values());
    }
    getCommandStats() {
        const commands = this.getAllCustomCommands();
        const totalUses = commands.reduce((sum, cmd) => sum + cmd.useCount, 0);
        const mostUsed = commands.sort((a, b) => b.useCount - a.useCount).slice(0, 5);
        return {
            totalCommands: commands.length,
            enabledCommands: commands.filter(cmd => cmd.enabled).length,
            disabledCommands: commands.filter(cmd => !cmd.enabled).length,
            totalUses,
            averageUses: commands.length > 0 ? Math.round(totalUses / commands.length) : 0,
            mostUsedCommands: mostUsed.map(cmd => ({
                name: cmd.name,
                uses: cmd.useCount,
                lastUsed: cmd.lastUsed,
            })),
            availableVariables: this.variables.size,
        };
    }
    exportCommands() {
        return this.getAllCustomCommands();
    }
    async importCommands(commands) {
        let importedCount = 0;
        for (const command of commands) {
            try {
                if (this.customCommands.has(command.name)) {
                    logger_1.logger.warn(`Skipping import of existing command: ${command.name}`);
                    continue;
                }
                this.customCommands.set(command.name, command);
                this.commandHandler.registerCommand(command, async (context) => {
                    await this.executeCustomCommand(context, command);
                });
                importedCount++;
            }
            catch (error) {
                logger_1.logger.error(`Failed to import command ${command.name}:`, error);
            }
        }
        logger_1.logger.info(`Imported ${importedCount} custom commands`);
        return importedCount;
    }
}
exports.CustomCommandManager = CustomCommandManager;
//# sourceMappingURL=CustomCommandManager.js.map