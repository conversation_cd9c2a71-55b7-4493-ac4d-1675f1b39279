import sqlite3 from 'sqlite3';
import { promisify } from 'util';
import { logger } from '../utils/logger';
import { databaseConfig } from '../config';
import path from 'path';
import fs from 'fs';

export interface DatabaseUser {
  id: number;
  username: string;
  display_name: string;
  points: number;
  messages_sent: number;
  commands_used: number;
  time_in_chat: number;
  last_seen: string;
  first_seen: string;
  is_follower: boolean;
  is_subscriber: boolean;
  is_vip: boolean;
  is_moderator: boolean;
  violations: number;
  timeouts: number;
  bans: number;
  created_at: string;
  updated_at: string;
}

export interface DatabaseCommand {
  id: number;
  name: string;
  response: string;
  description: string;
  aliases: string;
  cooldown: number;
  permission: number;
  enabled: boolean;
  use_count: number;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface ChatLog {
  id: number;
  username: string;
  message: string;
  timestamp: string;
  message_type: string;
  channel: string;
}

export interface ModerationLog {
  id: number;
  username: string;
  action: string;
  reason: string;
  duration: number | null;
  moderator: string;
  timestamp: string;
}

export class Database {
  private db: sqlite3.Database;
  private dbPath: string;

  constructor() {
    this.dbPath = databaseConfig.path;
    this.ensureDirectoryExists();
    this.db = new sqlite3.Database(this.dbPath);
    
    // Promisify database methods
    this.db.run = promisify(this.db.run.bind(this.db));
    this.db.get = promisify(this.db.get.bind(this.db));
    this.db.all = promisify(this.db.all.bind(this.db));
  }

  /**
   * Ensure database directory exists
   */
  private ensureDirectoryExists(): void {
    const dir = path.dirname(this.dbPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  }

  /**
   * Initialize database tables
   */
  async initialize(): Promise<void> {
    try {
      await this.createTables();
      logger.info('Database initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize database:', error);
      throw error;
    }
  }

  /**
   * Create database tables
   */
  private async createTables(): Promise<void> {
    // Users table
    await (this.db.run as any)(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        display_name TEXT NOT NULL,
        points INTEGER DEFAULT 0,
        messages_sent INTEGER DEFAULT 0,
        commands_used INTEGER DEFAULT 0,
        time_in_chat INTEGER DEFAULT 0,
        last_seen TEXT,
        first_seen TEXT,
        is_follower BOOLEAN DEFAULT FALSE,
        is_subscriber BOOLEAN DEFAULT FALSE,
        is_vip BOOLEAN DEFAULT FALSE,
        is_moderator BOOLEAN DEFAULT FALSE,
        violations INTEGER DEFAULT 0,
        timeouts INTEGER DEFAULT 0,
        bans INTEGER DEFAULT 0,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Custom commands table
    await (this.db.run as any)(`
      CREATE TABLE IF NOT EXISTS commands (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        response TEXT NOT NULL,
        description TEXT,
        aliases TEXT DEFAULT '',
        cooldown INTEGER DEFAULT 5,
        permission INTEGER DEFAULT 0,
        enabled BOOLEAN DEFAULT TRUE,
        use_count INTEGER DEFAULT 0,
        created_by TEXT NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Chat logs table
    await (this.db.run as any)(`
      CREATE TABLE IF NOT EXISTS chat_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT NOT NULL,
        message TEXT NOT NULL,
        timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
        message_type TEXT DEFAULT 'message',
        channel TEXT NOT NULL
      )
    `);

    // Moderation logs table
    await (this.db.run as any)(`
      CREATE TABLE IF NOT EXISTS moderation_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT NOT NULL,
        action TEXT NOT NULL,
        reason TEXT,
        duration INTEGER,
        moderator TEXT NOT NULL,
        timestamp TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Settings table
    await (this.db.run as any)(`
      CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Giveaways table
    await (this.db.run as any)(`
      CREATE TABLE IF NOT EXISTS giveaways (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        description TEXT,
        prize TEXT NOT NULL,
        duration INTEGER NOT NULL,
        max_entries INTEGER DEFAULT -1,
        entry_cost INTEGER DEFAULT 0,
        status TEXT DEFAULT 'active',
        winner TEXT,
        created_by TEXT NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        ended_at TEXT
      )
    `);

    // Giveaway entries table
    await (this.db.run as any)(`
      CREATE TABLE IF NOT EXISTS giveaway_entries (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        giveaway_id INTEGER NOT NULL,
        username TEXT NOT NULL,
        entries INTEGER DEFAULT 1,
        timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (giveaway_id) REFERENCES giveaways (id),
        UNIQUE(giveaway_id, username)
      )
    `);

    // Song requests table
    await (this.db.run as any)(`
      CREATE TABLE IF NOT EXISTS song_requests (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        artist TEXT,
        url TEXT NOT NULL,
        platform TEXT NOT NULL,
        duration INTEGER,
        requested_by TEXT NOT NULL,
        status TEXT DEFAULT 'pending',
        played_at TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);

    logger.info('Database tables created successfully');
  }

  // User operations
  async getUser(username: string): Promise<DatabaseUser | null> {
    try {
      const user = await (this.db.get as any)(
        'SELECT * FROM users WHERE username = ? COLLATE NOCASE',
        [username]
      );
      return user || null;
    } catch (error) {
      logger.error(`Failed to get user ${username}:`, error);
      return null;
    }
  }

  async createUser(userData: Partial<DatabaseUser>): Promise<boolean> {
    try {
      await (this.db.run as any)(`
        INSERT INTO users (username, display_name, first_seen, last_seen)
        VALUES (?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `, [userData.username, userData.display_name || userData.username]);
      
      logger.debug(`Created user: ${userData.username}`);
      return true;
    } catch (error) {
      logger.error(`Failed to create user ${userData.username}:`, error);
      return false;
    }
  }

  async updateUser(username: string, updates: Partial<DatabaseUser>): Promise<boolean> {
    try {
      const setClause = Object.keys(updates)
        .filter(key => key !== 'username')
        .map(key => `${key} = ?`)
        .join(', ');
      
      if (!setClause) return false;

      const values = Object.keys(updates)
        .filter(key => key !== 'username')
        .map(key => updates[key as keyof DatabaseUser]);
      
      values.push(username);

      await (this.db.run as any)(`
        UPDATE users SET ${setClause}, updated_at = CURRENT_TIMESTAMP
        WHERE username = ? COLLATE NOCASE
      `, values);

      return true;
    } catch (error) {
      logger.error(`Failed to update user ${username}:`, error);
      return false;
    }
  }

  // Command operations
  async getCommand(name: string): Promise<DatabaseCommand | null> {
    try {
      const command = await (this.db.get as any)(
        'SELECT * FROM commands WHERE name = ? COLLATE NOCASE',
        [name]
      );
      return command || null;
    } catch (error) {
      logger.error(`Failed to get command ${name}:`, error);
      return null;
    }
  }

  async getAllCommands(): Promise<DatabaseCommand[]> {
    try {
      const commands = await (this.db.all as any)('SELECT * FROM commands ORDER BY name');
      return commands || [];
    } catch (error) {
      logger.error('Failed to get all commands:', error);
      return [];
    }
  }

  async createCommand(commandData: Partial<DatabaseCommand>): Promise<boolean> {
    try {
      await (this.db.run as any)(`
        INSERT INTO commands (name, response, description, aliases, cooldown, permission, enabled, created_by)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        commandData.name,
        commandData.response,
        commandData.description || '',
        commandData.aliases || '',
        commandData.cooldown || 5,
        commandData.permission || 0,
        commandData.enabled !== false,
        commandData.created_by
      ]);
      
      logger.debug(`Created command: ${commandData.name}`);
      return true;
    } catch (error) {
      logger.error(`Failed to create command ${commandData.name}:`, error);
      return false;
    }
  }

  async updateCommand(name: string, updates: Partial<DatabaseCommand>): Promise<boolean> {
    try {
      const setClause = Object.keys(updates)
        .filter(key => key !== 'name' && key !== 'id')
        .map(key => `${key} = ?`)
        .join(', ');
      
      if (!setClause) return false;

      const values = Object.keys(updates)
        .filter(key => key !== 'name' && key !== 'id')
        .map(key => updates[key as keyof DatabaseCommand]);
      
      values.push(name);

      await (this.db.run as any)(`
        UPDATE commands SET ${setClause}, updated_at = CURRENT_TIMESTAMP
        WHERE name = ? COLLATE NOCASE
      `, values);

      return true;
    } catch (error) {
      logger.error(`Failed to update command ${name}:`, error);
      return false;
    }
  }

  async deleteCommand(name: string): Promise<boolean> {
    try {
      await (this.db.run as any)('DELETE FROM commands WHERE name = ? COLLATE NOCASE', [name]);
      logger.debug(`Deleted command: ${name}`);
      return true;
    } catch (error) {
      logger.error(`Failed to delete command ${name}:`, error);
      return false;
    }
  }

  // Chat logging
  async logMessage(username: string, message: string, channel: string, messageType: string = 'message'): Promise<void> {
    try {
      await (this.db.run as any)(`
        INSERT INTO chat_logs (username, message, channel, message_type)
        VALUES (?, ?, ?, ?)
      `, [username, message, channel, messageType]);
    } catch (error) {
      logger.error('Failed to log message:', error);
    }
  }

  // Moderation logging
  async logModerationAction(username: string, action: string, reason: string, duration: number | null, moderator: string): Promise<void> {
    try {
      await (this.db.run as any)(`
        INSERT INTO moderation_logs (username, action, reason, duration, moderator)
        VALUES (?, ?, ?, ?, ?)
      `, [username, action, reason, duration, moderator]);
    } catch (error) {
      logger.error('Failed to log moderation action:', error);
    }
  }

  // Settings
  async getSetting(key: string): Promise<string | null> {
    try {
      const result = await (this.db.get as any)('SELECT value FROM settings WHERE key = ?', [key]);
      return result ? result.value : null;
    } catch (error) {
      logger.error(`Failed to get setting ${key}:`, error);
      return null;
    }
  }

  async setSetting(key: string, value: string): Promise<boolean> {
    try {
      await (this.db.run as any)(`
        INSERT OR REPLACE INTO settings (key, value, updated_at)
        VALUES (?, ?, CURRENT_TIMESTAMP)
      `, [key, value]);
      return true;
    } catch (error) {
      logger.error(`Failed to set setting ${key}:`, error);
      return false;
    }
  }

  // Statistics
  async getStats(): Promise<any> {
    try {
      const userCount = await (this.db.get as any)('SELECT COUNT(*) as count FROM users');
      const commandCount = await (this.db.get as any)('SELECT COUNT(*) as count FROM commands');
      const messageCount = await (this.db.get as any)('SELECT COUNT(*) as count FROM chat_logs');
      const moderationCount = await (this.db.get as any)('SELECT COUNT(*) as count FROM moderation_logs');

      return {
        users: userCount.count,
        commands: commandCount.count,
        messages: messageCount.count,
        moderationActions: moderationCount.count,
      };
    } catch (error) {
      logger.error('Failed to get stats:', error);
      return { users: 0, commands: 0, messages: 0, moderationActions: 0 };
    }
  }

  /**
   * Close database connection
   */
  async close(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.db.close((err) => {
        if (err) {
          logger.error('Error closing database:', err);
          reject(err);
        } else {
          logger.info('Database connection closed');
          resolve();
        }
      });
    });
  }
}

export const database = new Database();
