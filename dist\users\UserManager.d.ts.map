{"version": 3, "file": "UserManager.d.ts", "sourceRoot": "", "sources": ["../../src/users/UserManager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,MAAM,UAAU,CAAC;AAIhE,MAAM,WAAW,SAAS;IACxB,CAAC,QAAQ,EAAE,MAAM,GAAG;QAClB,IAAI,EAAE,QAAQ,CAAC;QACf,QAAQ,EAAE,IAAI,CAAC;QACf,YAAY,EAAE,MAAM,CAAC;QACrB,YAAY,EAAE,MAAM,CAAC;QACrB,UAAU,EAAE,MAAM,CAAC;QACnB,MAAM,EAAE,MAAM,CAAC;KAChB,CAAC;CACH;AAED,qBAAa,WAAY,SAAQ,YAAY;IAC3C,OAAO,CAAC,SAAS,CAAiB;IAClC,OAAO,CAAC,UAAU,CAAqB;IACvC,OAAO,CAAC,IAAI,CAAqB;IACjC,OAAO,CAAC,WAAW,CAAqB;IACxC,OAAO,CAAC,SAAS,CAAqB;IACtC,OAAO,CAAC,WAAW,CAAqB;;IASxC,UAAU,CAAC,IAAI,EAAE,QAAQ,GAAG,IAAI;IA4BhC,OAAO,CAAC,qBAAqB;IA0CvB,OAAO,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;IAyBzD,sBAAsB,CAAC,IAAI,EAAE,QAAQ,GAAG,MAAM,GAAG,eAAe;IA0BhE,aAAa,CAAC,IAAI,EAAE,QAAQ,GAAG,MAAM,EAAE,aAAa,EAAE,eAAe,GAAG,OAAO;IAQ/E,qBAAqB,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;IAW7C,qBAAqB,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;IAU7C,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI;IAWjD,YAAY,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO;IAgBvD,YAAY,CAAC,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI;IAsBhD,YAAY,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO;IAevC,aAAa,IAAI,MAAM,EAAE;IAOzB,OAAO,IAAI,MAAM,EAAE;IAOnB,cAAc,IAAI,MAAM,EAAE;IAO1B,YAAY,IAAI,MAAM,EAAE;IAOxB,cAAc,IAAI,MAAM,EAAE;IAO1B,cAAc,IAAI,MAAM,EAAE;IAgB1B,mBAAmB,CAAC,KAAK,GAAE,MAAW,GAAG,KAAK,CAAC;QAAE,QAAQ,EAAE,MAAM,CAAC;QAAC,MAAM,EAAE,MAAM,CAAA;KAAE,CAAC;IAepF,qBAAqB,CAAC,KAAK,GAAE,MAAW,GAAG,KAAK,CAAC;QAAE,QAAQ,EAAE,MAAM,CAAC;QAAC,QAAQ,EAAE,MAAM,CAAA;KAAE,CAAC;IAexF,kBAAkB,IAAI,MAAM;IAqB5B,aAAa;;;;;;;;;IAkBb,cAAc,IAAI,SAAS;IAO3B,cAAc,CAAC,IAAI,EAAE,SAAS,GAAG,IAAI;CAUtC"}