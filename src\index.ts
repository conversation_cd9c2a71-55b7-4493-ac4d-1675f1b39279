import { KickBot } from './bot/KickBot';
import { CommandHandler } from './commands/CommandHandler';
import { registerDefaultCommands } from './commands/defaultCommands';
import { config } from './config';
import { logger } from './utils/logger';

async function main(): Promise<void> {
  try {
    logger.info('Starting KickBot application...');

    // Validate configuration
    if (!config.username || !config.channel) {
      throw new Error('Bot username and channel must be configured in environment variables');
    }

    if (!config.password && !config.token) {
      throw new Error('Either bot password or token must be configured');
    }

    // Create bot instance
    const bot = new KickBot(config);

    // Create command handler
    const commandHandler = new CommandHandler(bot);

    // Register default commands
    registerDefaultCommands(commandHandler);

    // Setup bot event handlers
    setupBotEventHandlers(bot, commandHandler);

    // Setup graceful shutdown
    setupGracefulShutdown(bot);

    // Start the bot
    await bot.start();

    logger.info('KickBot is now running! Press Ctrl+C to stop.');

  } catch (error) {
    logger.error('Failed to start KickBot:', error);
    process.exit(1);
  }
}

/**
 * Setup bot event handlers
 */
function setupBotEventHandlers(bot: KickBot, commandHandler: CommandHandler): void {
  bot.on('started', () => {
    logger.info('Bot started successfully');
  });

  bot.on('stopped', () => {
    logger.info('Bot stopped');
  });

  bot.on('connected', () => {
    logger.info('Connected to Kick WebSocket');
  });

  bot.on('subscribed', () => {
    logger.info('Subscribed to channel chat');
  });

  bot.on('message', (message) => {
    // Log all messages (already handled by logger in bot)
    // Additional message processing can be added here
  });

  bot.on('new_follower', (data) => {
    // Handle new follower event
    bot.sendMessage(`Welcome to the stream, @${data.username}! Thanks for following! 🎉`);
  });

  bot.on('user_banned', (data) => {
    logger.info(`User banned: ${data.user?.username}`);
  });

  bot.on('user_unbanned', (data) => {
    logger.info(`User unbanned: ${data.user?.username}`);
  });

  bot.on('streamer_live', (data) => {
    logger.info('Streamer went live');
    // Could send a message or perform other actions
  });

  bot.on('streamer_offline', (data) => {
    logger.info('Streamer went offline');
  });

  bot.on('error', (error) => {
    logger.error('Bot error:', error);
    
    // Attempt to restart bot after error
    setTimeout(async () => {
      try {
        logger.info('Attempting to restart bot after error...');
        await bot.stop();
        await bot.start();
      } catch (restartError) {
        logger.error('Failed to restart bot:', restartError);
        process.exit(1);
      }
    }, 5000);
  });

  // Command handler events
  commandHandler.on('command_executed', (data) => {
    logger.info(`Command executed: ${data.command} by ${data.user}`);
  });

  commandHandler.on('command_error', (data) => {
    logger.error(`Command error: ${data.command} by ${data.user}:`, data.error);
  });
}

/**
 * Setup graceful shutdown handlers
 */
function setupGracefulShutdown(bot: KickBot): void {
  const shutdown = async (signal: string) => {
    logger.info(`Received ${signal}, shutting down gracefully...`);
    
    try {
      await bot.stop();
      logger.info('Bot stopped successfully');
      process.exit(0);
    } catch (error) {
      logger.error('Error during shutdown:', error);
      process.exit(1);
    }
  };

  // Handle different shutdown signals
  process.on('SIGINT', () => shutdown('SIGINT'));
  process.on('SIGTERM', () => shutdown('SIGTERM'));
  process.on('SIGQUIT', () => shutdown('SIGQUIT'));

  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    logger.error('Uncaught exception:', error);
    shutdown('uncaughtException');
  });

  // Handle unhandled promise rejections
  process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled promise rejection:', { reason, promise });
    shutdown('unhandledRejection');
  });
}

// Start the application
if (require.main === module) {
  main().catch((error) => {
    logger.error('Application startup failed:', error);
    process.exit(1);
  });
}

export { KickBot, CommandHandler, registerDefaultCommands };
