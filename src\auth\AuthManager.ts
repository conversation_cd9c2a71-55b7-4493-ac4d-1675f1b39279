import { kickAPI } from '../api/kickApi';
import { logger } from '../utils/logger';

export interface AuthCredentials {
  email?: string;
  password?: string;
  token?: string;
}

export interface AuthResult {
  success: boolean;
  userId?: number;
  username?: string;
  error?: string;
}

export class AuthManager {
  private isAuthenticated = false;
  private currentUserId?: number;
  private currentUsername?: string;
  private authToken?: string;

  /**
   * Authenticate with <PERSON> using provided credentials
   */
  async authenticate(credentials: AuthCredentials): Promise<AuthResult> {
    try {
      logger.info('Attempting to authenticate with Kick...');

      if (credentials.token) {
        // Token-based authentication
        return await this.authenticateWithToken(credentials.token);
      } else if (credentials.email && credentials.password) {
        // Email/password authentication
        return await this.authenticateWithPassword(credentials.email, credentials.password);
      } else {
        return {
          success: false,
          error: 'No valid authentication credentials provided',
        };
      }
    } catch (error: any) {
      logger.error('Authentication failed:', error);
      return {
        success: false,
        error: error.message || 'Authentication failed',
      };
    }
  }

  /**
   * Authenticate using API token
   */
  private async authenticateWithToken(token: string): Promise<AuthResult> {
    try {
      // Set the token in the API client
      this.authToken = token;
      
      // Test the token by making an authenticated request
      // This would need to be implemented based on Kick's API
      // For now, we'll assume the token is valid
      
      this.isAuthenticated = true;
      
      logger.info('Successfully authenticated with token');
      return {
        success: true,
        userId: this.currentUserId,
        username: this.currentUsername,
      };
    } catch (error: any) {
      logger.error('Token authentication failed:', error);
      return {
        success: false,
        error: 'Invalid token',
      };
    }
  }

  /**
   * Authenticate using email and password
   */
  private async authenticateWithPassword(email: string, password: string): Promise<AuthResult> {
    try {
      const success = await kickAPI.authenticate(email, password);
      
      if (success) {
        this.isAuthenticated = true;
        this.currentUserId = kickAPI.currentUserId;
        
        logger.info('Successfully authenticated with email/password');
        return {
          success: true,
          userId: this.currentUserId,
          username: this.currentUsername,
        };
      } else {
        return {
          success: false,
          error: 'Invalid email or password',
        };
      }
    } catch (error: any) {
      logger.error('Password authentication failed:', error);
      return {
        success: false,
        error: error.message || 'Authentication failed',
      };
    }
  }

  /**
   * Logout and clear authentication
   */
  logout(): void {
    this.isAuthenticated = false;
    this.currentUserId = undefined;
    this.currentUsername = undefined;
    this.authToken = undefined;
    
    logger.info('Logged out successfully');
  }

  /**
   * Check if currently authenticated
   */
  get authenticated(): boolean {
    return this.isAuthenticated;
  }

  /**
   * Get current user ID
   */
  get userId(): number | undefined {
    return this.currentUserId;
  }

  /**
   * Get current username
   */
  get username(): string | undefined {
    return this.currentUsername;
  }

  /**
   * Get current auth token
   */
  get token(): string | undefined {
    return this.authToken;
  }

  /**
   * Refresh authentication if needed
   */
  async refreshAuth(): Promise<boolean> {
    if (!this.isAuthenticated) {
      return false;
    }

    try {
      // Implement token refresh logic here if Kick supports it
      // For now, we'll just return the current status
      return this.isAuthenticated;
    } catch (error) {
      logger.error('Failed to refresh authentication:', error);
      this.logout();
      return false;
    }
  }

  /**
   * Validate current authentication status
   */
  async validateAuth(): Promise<boolean> {
    if (!this.isAuthenticated) {
      return false;
    }

    try {
      // Make a test API call to validate the authentication
      // This would depend on Kick's API endpoints
      return kickAPI.isAuthenticated;
    } catch (error) {
      logger.error('Authentication validation failed:', error);
      this.logout();
      return false;
    }
  }
}

export const authManager = new AuthManager();
