import { EventEmitter } from 'events';
import { Command, PermissionLevel, ChatMessage } from '../types';
import { KickBot } from '../bot/KickBot';
import { logger } from '../utils/logger';

export interface CommandContext {
  bot: KickBot;
  message: ChatMessage;
  args: string[];
  user: any;
  command: Command;
}

export type CommandFunction = (context: CommandContext) => Promise<void>;

export class CommandHandler extends EventEmitter {
  private commands = new Map<string, Command>();
  private commandFunctions = new Map<string, CommandFunction>();
  private cooldowns = new Map<string, Map<string, number>>();

  constructor(private bot: KickBot) {
    super();
    this.setupBotEvents();
  }

  /**
   * Setup bot event listeners
   */
  private setupBotEvents(): void {
    this.bot.on('command', async (commandData: any) => {
      await this.handleCommand(commandData);
    });
  }

  /**
   * Register a command
   */
  registerCommand(command: Command, handler: CommandFunction): void {
    // Store command metadata
    this.commands.set(command.name, command);
    this.commandFunctions.set(command.name, handler);

    // Register aliases
    for (const alias of command.aliases) {
      this.commands.set(alias, command);
      this.commandFunctions.set(alias, handler);
    }

    logger.info(`Registered command: ${command.name} (aliases: ${command.aliases.join(', ')})`);
  }

  /**
   * Unregister a command
   */
  unregisterCommand(commandName: string): boolean {
    const command = this.commands.get(commandName);
    if (!command) return false;

    // Remove main command
    this.commands.delete(command.name);
    this.commandFunctions.delete(command.name);

    // Remove aliases
    for (const alias of command.aliases) {
      this.commands.delete(alias);
      this.commandFunctions.delete(alias);
    }

    // Clear cooldowns
    this.cooldowns.delete(command.name);

    logger.info(`Unregistered command: ${command.name}`);
    return true;
  }

  /**
   * Handle incoming command
   */
  private async handleCommand(commandData: any): Promise<void> {
    const { name, args, message, user } = commandData;
    
    const command = this.commands.get(name);
    if (!command) {
      logger.debug(`Unknown command: ${name}`);
      return;
    }

    // Check if command is enabled
    if (!command.enabled) {
      logger.debug(`Command disabled: ${name}`);
      return;
    }

    // Check permissions
    if (!this.bot.hasPermission(user, command.permission)) {
      await this.bot.replyToMessage(message, 'You do not have permission to use this command.');
      return;
    }

    // Check cooldown
    if (this.isOnCooldown(command.name, user.username)) {
      const remainingTime = this.getRemainingCooldown(command.name, user.username);
      await this.bot.replyToMessage(message, `Command is on cooldown. Please wait ${remainingTime} seconds.`);
      return;
    }

    // Execute command
    try {
      const context: CommandContext = {
        bot: this.bot,
        message,
        args,
        user,
        command,
      };

      const handler = this.commandFunctions.get(name);
      if (handler) {
        await handler(context);
        
        // Set cooldown
        this.setCooldown(command.name, user.username, command.cooldown);
        
        this.emit('command_executed', {
          command: command.name,
          user: user.username,
          args,
          success: true,
        });
      }
    } catch (error) {
      logger.error(`Error executing command ${name}:`, error);
      await this.bot.replyToMessage(message, 'An error occurred while executing the command.');
      
      this.emit('command_error', {
        command: command.name,
        user: user.username,
        error,
      });
    }
  }

  /**
   * Check if command is on cooldown for user
   */
  private isOnCooldown(commandName: string, username: string): boolean {
    const commandCooldowns = this.cooldowns.get(commandName);
    if (!commandCooldowns) return false;

    const userCooldown = commandCooldowns.get(username);
    if (!userCooldown) return false;

    return Date.now() < userCooldown;
  }

  /**
   * Get remaining cooldown time in seconds
   */
  private getRemainingCooldown(commandName: string, username: string): number {
    const commandCooldowns = this.cooldowns.get(commandName);
    if (!commandCooldowns) return 0;

    const userCooldown = commandCooldowns.get(username);
    if (!userCooldown) return 0;

    const remaining = Math.ceil((userCooldown - Date.now()) / 1000);
    return Math.max(0, remaining);
  }

  /**
   * Set cooldown for user
   */
  private setCooldown(commandName: string, username: string, cooldownSeconds: number): void {
    if (cooldownSeconds <= 0) return;

    if (!this.cooldowns.has(commandName)) {
      this.cooldowns.set(commandName, new Map());
    }

    const commandCooldowns = this.cooldowns.get(commandName)!;
    const cooldownEnd = Date.now() + (cooldownSeconds * 1000);
    commandCooldowns.set(username, cooldownEnd);

    // Clean up expired cooldowns periodically
    setTimeout(() => {
      commandCooldowns.delete(username);
      if (commandCooldowns.size === 0) {
        this.cooldowns.delete(commandName);
      }
    }, cooldownSeconds * 1000);
  }

  /**
   * Get all registered commands
   */
  getCommands(): Command[] {
    const uniqueCommands = new Map<string, Command>();
    
    for (const [name, command] of this.commands) {
      if (name === command.name) { // Only include main command names, not aliases
        uniqueCommands.set(name, command);
      }
    }
    
    return Array.from(uniqueCommands.values());
  }

  /**
   * Get command by name
   */
  getCommand(name: string): Command | undefined {
    return this.commands.get(name);
  }

  /**
   * Get commands by permission level
   */
  getCommandsByPermission(permissionLevel: PermissionLevel): Command[] {
    return this.getCommands().filter(cmd => cmd.permission <= permissionLevel);
  }

  /**
   * Update command properties
   */
  updateCommand(name: string, updates: Partial<Command>): boolean {
    const command = this.commands.get(name);
    if (!command) return false;

    // Update the command object
    Object.assign(command, updates);

    // Update all references (main name + aliases)
    this.commands.set(command.name, command);
    for (const alias of command.aliases) {
      this.commands.set(alias, command);
    }

    logger.info(`Updated command: ${name}`);
    return true;
  }

  /**
   * Clear all cooldowns for a command
   */
  clearCooldowns(commandName: string): void {
    this.cooldowns.delete(commandName);
    logger.info(`Cleared cooldowns for command: ${commandName}`);
  }

  /**
   * Clear all cooldowns for a user
   */
  clearUserCooldowns(username: string): void {
    for (const [commandName, commandCooldowns] of this.cooldowns) {
      commandCooldowns.delete(username);
      if (commandCooldowns.size === 0) {
        this.cooldowns.delete(commandName);
      }
    }
    logger.info(`Cleared all cooldowns for user: ${username}`);
  }

  /**
   * Get command usage statistics
   */
  getCommandStats(): any {
    const commands = this.getCommands();
    const stats = {
      totalCommands: commands.length,
      enabledCommands: commands.filter(cmd => cmd.enabled).length,
      disabledCommands: commands.filter(cmd => !cmd.enabled).length,
      commandsByPermission: {
        [PermissionLevel.EVERYONE]: commands.filter(cmd => cmd.permission === PermissionLevel.EVERYONE).length,
        [PermissionLevel.FOLLOWER]: commands.filter(cmd => cmd.permission === PermissionLevel.FOLLOWER).length,
        [PermissionLevel.SUBSCRIBER]: commands.filter(cmd => cmd.permission === PermissionLevel.SUBSCRIBER).length,
        [PermissionLevel.VIP]: commands.filter(cmd => cmd.permission === PermissionLevel.VIP).length,
        [PermissionLevel.MODERATOR]: commands.filter(cmd => cmd.permission === PermissionLevel.MODERATOR).length,
        [PermissionLevel.OWNER]: commands.filter(cmd => cmd.permission === PermissionLevel.OWNER).length,
      },
      activeCooldowns: this.cooldowns.size,
    };

    return stats;
  }
}
