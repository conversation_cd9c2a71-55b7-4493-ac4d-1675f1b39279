import { EventEmitter } from 'events';
import { Command, PermissionLevel, ChatMessage } from '../types';
import { CommandHandler, CommandContext } from './CommandHandler';
import { logger } from '../utils/logger';

export interface CustomCommand extends Command {
  id: string;
  response: string;
  variables: string[];
  isCustom: true;
  useCount: number;
  lastUsed?: string;
}

export interface CommandVariable {
  name: string;
  description: string;
  example: string;
  resolver: (context: CommandContext) => string | Promise<string>;
}

export class CustomCommandManager extends EventEmitter {
  private customCommands = new Map<string, CustomCommand>();
  private variables = new Map<string, CommandVariable>();
  private commandHandler: CommandHandler;

  constructor(commandHandler: CommandHandler) {
    super();
    this.commandHandler = commandHandler;
    this.setupDefaultVariables();
  }

  /**
   * Setup default command variables
   */
  private setupDefaultVariables(): void {
    this.registerVariable({
      name: 'user',
      description: 'The username of the person who used the command',
      example: '{user}',
      resolver: (context) => context.user.username,
    });

    this.registerVariable({
      name: 'channel',
      description: 'The current channel name',
      example: '{channel}',
      resolver: (context) => context.bot.currentChannel?.slug || 'unknown',
    });

    this.registerVariable({
      name: 'uptime',
      description: 'Bot uptime',
      example: '{uptime}',
      resolver: (context) => context.bot.getStats().uptimeFormatted,
    });

    this.registerVariable({
      name: 'count',
      description: 'Number of times this command has been used',
      example: '{count}',
      resolver: (context) => {
        const customCmd = this.getCustomCommand(context.command.name);
        return customCmd ? customCmd.useCount.toString() : '0';
      },
    });

    this.registerVariable({
      name: 'random',
      description: 'Random number between 1-100',
      example: '{random}',
      resolver: () => Math.floor(Math.random() * 100 + 1).toString(),
    });

    this.registerVariable({
      name: 'args',
      description: 'All arguments passed to the command',
      example: '{args}',
      resolver: (context) => context.args.join(' '),
    });

    this.registerVariable({
      name: 'arg1',
      description: 'First argument passed to the command',
      example: '{arg1}',
      resolver: (context) => context.args[0] || '',
    });

    this.registerVariable({
      name: 'arg2',
      description: 'Second argument passed to the command',
      example: '{arg2}',
      resolver: (context) => context.args[1] || '',
    });

    this.registerVariable({
      name: 'arg3',
      description: 'Third argument passed to the command',
      example: '{arg3}',
      resolver: (context) => context.args[2] || '',
    });

    this.registerVariable({
      name: 'time',
      description: 'Current time',
      example: '{time}',
      resolver: () => new Date().toLocaleTimeString(),
    });

    this.registerVariable({
      name: 'date',
      description: 'Current date',
      example: '{date}',
      resolver: () => new Date().toLocaleDateString(),
    });
  }

  /**
   * Register a new variable
   */
  registerVariable(variable: CommandVariable): void {
    this.variables.set(variable.name, variable);
    logger.debug(`Registered command variable: ${variable.name}`);
  }

  /**
   * Create a new custom command
   */
  async createCommand(
    name: string,
    response: string,
    options: {
      description?: string;
      aliases?: string[];
      cooldown?: number;
      permission?: PermissionLevel;
      enabled?: boolean;
      createdBy: string;
    }
  ): Promise<boolean> {
    try {
      // Check if command already exists
      if (this.commandHandler.getCommand(name) || this.customCommands.has(name)) {
        logger.warn(`Command ${name} already exists`);
        return false;
      }

      // Validate command name
      if (!this.isValidCommandName(name)) {
        logger.warn(`Invalid command name: ${name}`);
        return false;
      }

      // Create custom command
      const customCommand: CustomCommand = {
        id: this.generateCommandId(),
        name,
        description: options.description || `Custom command: ${name}`,
        usage: `!${name}`,
        aliases: options.aliases || [],
        cooldown: options.cooldown || 5,
        permission: options.permission || PermissionLevel.EVERYONE,
        enabled: options.enabled !== false,
        response,
        variables: this.extractVariables(response),
        isCustom: true,
        useCount: 0,
        created_by: options.createdBy,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Store custom command
      this.customCommands.set(name, customCommand);

      // Register with command handler
      this.commandHandler.registerCommand(customCommand, async (context) => {
        await this.executeCustomCommand(context, customCommand);
      });

      logger.info(`Created custom command: ${name}`);
      this.emit('command_created', customCommand);

      return true;
    } catch (error) {
      logger.error(`Failed to create command ${name}:`, error);
      return false;
    }
  }

  /**
   * Update an existing custom command
   */
  async updateCommand(
    name: string,
    updates: {
      response?: string;
      description?: string;
      aliases?: string[];
      cooldown?: number;
      permission?: PermissionLevel;
      enabled?: boolean;
    }
  ): Promise<boolean> {
    try {
      const customCommand = this.customCommands.get(name);
      if (!customCommand) {
        logger.warn(`Custom command ${name} not found`);
        return false;
      }

      // Update command properties
      if (updates.response !== undefined) {
        customCommand.response = updates.response;
        customCommand.variables = this.extractVariables(updates.response);
      }
      if (updates.description !== undefined) customCommand.description = updates.description;
      if (updates.aliases !== undefined) customCommand.aliases = updates.aliases;
      if (updates.cooldown !== undefined) customCommand.cooldown = updates.cooldown;
      if (updates.permission !== undefined) customCommand.permission = updates.permission;
      if (updates.enabled !== undefined) customCommand.enabled = updates.enabled;

      customCommand.updated_at = new Date().toISOString();

      // Update in command handler
      this.commandHandler.updateCommand(name, customCommand);

      logger.info(`Updated custom command: ${name}`);
      this.emit('command_updated', customCommand);

      return true;
    } catch (error) {
      logger.error(`Failed to update command ${name}:`, error);
      return false;
    }
  }

  /**
   * Delete a custom command
   */
  async deleteCommand(name: string): Promise<boolean> {
    try {
      const customCommand = this.customCommands.get(name);
      if (!customCommand) {
        logger.warn(`Custom command ${name} not found`);
        return false;
      }

      // Remove from custom commands
      this.customCommands.delete(name);

      // Unregister from command handler
      this.commandHandler.unregisterCommand(name);

      logger.info(`Deleted custom command: ${name}`);
      this.emit('command_deleted', customCommand);

      return true;
    } catch (error) {
      logger.error(`Failed to delete command ${name}:`, error);
      return false;
    }
  }

  /**
   * Execute a custom command
   */
  private async executeCustomCommand(context: CommandContext, customCommand: CustomCommand): Promise<void> {
    try {
      // Increment use count
      customCommand.useCount++;
      customCommand.lastUsed = new Date().toISOString();

      // Process variables in response
      const processedResponse = await this.processVariables(customCommand.response, context);

      // Send response
      await context.bot.replyToMessage(context.message, processedResponse);

      this.emit('command_executed', {
        command: customCommand,
        user: context.user.username,
        response: processedResponse,
      });
    } catch (error) {
      logger.error(`Error executing custom command ${customCommand.name}:`, error);
      await context.bot.replyToMessage(context.message, 'An error occurred while executing the command.');
    }
  }

  /**
   * Process variables in a response string
   */
  private async processVariables(response: string, context: CommandContext): Promise<string> {
    let processedResponse = response;

    // Find all variables in the response
    const variableMatches = response.match(/\{([^}]+)\}/g);
    if (!variableMatches) {
      return processedResponse;
    }

    // Process each variable
    for (const match of variableMatches) {
      const variableName = match.slice(1, -1); // Remove { and }
      const variable = this.variables.get(variableName);

      if (variable) {
        try {
          const value = await variable.resolver(context);
          processedResponse = processedResponse.replace(match, value);
        } catch (error) {
          logger.error(`Error resolving variable ${variableName}:`, error);
          processedResponse = processedResponse.replace(match, `{${variableName}}`);
        }
      }
    }

    return processedResponse;
  }

  /**
   * Extract variables from a response string
   */
  private extractVariables(response: string): string[] {
    const variableMatches = response.match(/\{([^}]+)\}/g);
    if (!variableMatches) {
      return [];
    }

    return variableMatches.map(match => match.slice(1, -1)); // Remove { and }
  }

  /**
   * Validate command name
   */
  private isValidCommandName(name: string): boolean {
    // Command name should be alphanumeric and underscores only
    return /^[a-zA-Z0-9_]+$/.test(name) && name.length >= 2 && name.length <= 50;
  }

  /**
   * Generate unique command ID
   */
  private generateCommandId(): string {
    return `cmd_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get a custom command
   */
  getCustomCommand(name: string): CustomCommand | undefined {
    return this.customCommands.get(name);
  }

  /**
   * Get all custom commands
   */
  getAllCustomCommands(): CustomCommand[] {
    return Array.from(this.customCommands.values());
  }

  /**
   * Get custom commands by permission level
   */
  getCustomCommandsByPermission(permissionLevel: PermissionLevel): CustomCommand[] {
    return this.getAllCustomCommands().filter(cmd => cmd.permission <= permissionLevel);
  }

  /**
   * Get available variables
   */
  getAvailableVariables(): CommandVariable[] {
    return Array.from(this.variables.values());
  }

  /**
   * Get command statistics
   */
  getCommandStats() {
    const commands = this.getAllCustomCommands();
    const totalUses = commands.reduce((sum, cmd) => sum + cmd.useCount, 0);
    const mostUsed = commands.sort((a, b) => b.useCount - a.useCount).slice(0, 5);

    return {
      totalCommands: commands.length,
      enabledCommands: commands.filter(cmd => cmd.enabled).length,
      disabledCommands: commands.filter(cmd => !cmd.enabled).length,
      totalUses,
      averageUses: commands.length > 0 ? Math.round(totalUses / commands.length) : 0,
      mostUsedCommands: mostUsed.map(cmd => ({
        name: cmd.name,
        uses: cmd.useCount,
        lastUsed: cmd.lastUsed,
      })),
      availableVariables: this.variables.size,
    };
  }

  /**
   * Export custom commands for backup
   */
  exportCommands(): CustomCommand[] {
    return this.getAllCustomCommands();
  }

  /**
   * Import custom commands from backup
   */
  async importCommands(commands: CustomCommand[]): Promise<number> {
    let importedCount = 0;

    for (const command of commands) {
      try {
        // Check if command already exists
        if (this.customCommands.has(command.name)) {
          logger.warn(`Skipping import of existing command: ${command.name}`);
          continue;
        }

        // Store custom command
        this.customCommands.set(command.name, command);

        // Register with command handler
        this.commandHandler.registerCommand(command, async (context) => {
          await this.executeCustomCommand(context, command);
        });

        importedCount++;
      } catch (error) {
        logger.error(`Failed to import command ${command.name}:`, error);
      }
    }

    logger.info(`Imported ${importedCount} custom commands`);
    return importedCount;
  }
}
