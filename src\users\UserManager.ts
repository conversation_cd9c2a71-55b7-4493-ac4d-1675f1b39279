import { EventEmitter } from 'events';
import { KickUser, PermissionLevel, UserStats } from '../types';
import { kickAPI } from '../api/kickApi';
import { logger } from '../utils/logger';

export interface UserCache {
  [username: string]: {
    user: KickUser;
    lastSeen: Date;
    messageCount: number;
    commandCount: number;
    timeInChat: number;
    points: number;
  };
}

export class UserManager extends EventEmitter {
  private userCache: UserCache = {};
  private moderators = new Set<string>();
  private vips = new Set<string>();
  private subscribers = new Set<string>();
  private followers = new Set<string>();
  private bannedUsers = new Set<string>();

  constructor() {
    super();
  }

  /**
   * Add or update user in cache
   */
  updateUser(user: KickUser): void {
    const username = user.username.toLowerCase();
    const now = new Date();

    if (!this.userCache[username]) {
      this.userCache[username] = {
        user,
        lastSeen: now,
        messageCount: 0,
        commandCount: 0,
        timeInChat: 0,
        points: 0,
      };
      
      logger.debug(`Added new user to cache: ${username}`);
    } else {
      // Update user data
      this.userCache[username].user = user;
      this.userCache[username].lastSeen = now;
    }

    // Update permission sets
    this.updateUserPermissions(user);
  }

  /**
   * Update user permission sets
   */
  private updateUserPermissions(user: KickUser): void {
    const username = user.username.toLowerCase();

    // Update moderators
    if (user.is_moderator) {
      this.moderators.add(username);
    } else {
      this.moderators.delete(username);
    }

    // Update VIPs
    if (user.is_vip) {
      this.vips.add(username);
    } else {
      this.vips.delete(username);
    }

    // Update subscribers
    if (user.is_subscriber) {
      this.subscribers.add(username);
    } else {
      this.subscribers.delete(username);
    }

    // Update followers
    if (user.is_follower) {
      this.followers.add(username);
    } else {
      this.followers.delete(username);
    }

    // Update banned users
    if (user.is_banned) {
      this.bannedUsers.add(username);
    } else {
      this.bannedUsers.delete(username);
    }
  }

  /**
   * Get user from cache or fetch from API
   */
  async getUser(username: string): Promise<KickUser | null> {
    const normalizedUsername = username.toLowerCase();
    
    // Check cache first
    if (this.userCache[normalizedUsername]) {
      return this.userCache[normalizedUsername].user;
    }

    // Fetch from API
    try {
      const user = await kickAPI.getUser(username);
      if (user) {
        this.updateUser(user);
        return user;
      }
    } catch (error) {
      logger.error(`Failed to fetch user ${username}:`, error);
    }

    return null;
  }

  /**
   * Get user permission level
   */
  getUserPermissionLevel(user: KickUser | string): PermissionLevel {
    let userData: KickUser | null = null;

    if (typeof user === 'string') {
      const normalizedUsername = user.toLowerCase();
      userData = this.userCache[normalizedUsername]?.user || null;
    } else {
      userData = user;
    }

    if (!userData) {
      return PermissionLevel.EVERYONE;
    }

    if (userData.is_channel_owner) return PermissionLevel.OWNER;
    if (userData.is_moderator) return PermissionLevel.MODERATOR;
    if (userData.is_vip) return PermissionLevel.VIP;
    if (userData.is_subscriber) return PermissionLevel.SUBSCRIBER;
    if (userData.is_follower) return PermissionLevel.FOLLOWER;
    
    return PermissionLevel.EVERYONE;
  }

  /**
   * Check if user has required permission level
   */
  hasPermission(user: KickUser | string, requiredLevel: PermissionLevel): boolean {
    const userLevel = this.getUserPermissionLevel(user);
    return userLevel >= requiredLevel;
  }

  /**
   * Increment message count for user
   */
  incrementMessageCount(username: string): void {
    const normalizedUsername = username.toLowerCase();
    if (this.userCache[normalizedUsername]) {
      this.userCache[normalizedUsername].messageCount++;
      this.userCache[normalizedUsername].lastSeen = new Date();
    }
  }

  /**
   * Increment command count for user
   */
  incrementCommandCount(username: string): void {
    const normalizedUsername = username.toLowerCase();
    if (this.userCache[normalizedUsername]) {
      this.userCache[normalizedUsername].commandCount++;
    }
  }

  /**
   * Add points to user
   */
  addPoints(username: string, points: number): void {
    const normalizedUsername = username.toLowerCase();
    if (this.userCache[normalizedUsername]) {
      this.userCache[normalizedUsername].points += points;
      this.emit('points_added', { username, points, total: this.userCache[normalizedUsername].points });
    }
  }

  /**
   * Remove points from user
   */
  removePoints(username: string, points: number): boolean {
    const normalizedUsername = username.toLowerCase();
    if (this.userCache[normalizedUsername]) {
      const currentPoints = this.userCache[normalizedUsername].points;
      if (currentPoints >= points) {
        this.userCache[normalizedUsername].points -= points;
        this.emit('points_removed', { username, points, total: this.userCache[normalizedUsername].points });
        return true;
      }
    }
    return false;
  }

  /**
   * Get user statistics
   */
  getUserStats(username: string): UserStats | null {
    const normalizedUsername = username.toLowerCase();
    const userData = this.userCache[normalizedUsername];
    
    if (!userData) {
      return null;
    }

    return {
      username: userData.user.username,
      messages_sent: userData.messageCount,
      commands_used: userData.commandCount,
      time_in_chat: userData.timeInChat,
      points: userData.points,
      last_seen: userData.lastSeen.toISOString(),
      is_active: this.isUserActive(username),
    };
  }

  /**
   * Check if user is active (seen in last 10 minutes)
   */
  isUserActive(username: string): boolean {
    const normalizedUsername = username.toLowerCase();
    const userData = this.userCache[normalizedUsername];
    
    if (!userData) {
      return false;
    }

    const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000);
    return userData.lastSeen > tenMinutesAgo;
  }

  /**
   * Get all moderators
   */
  getModerators(): string[] {
    return Array.from(this.moderators);
  }

  /**
   * Get all VIPs
   */
  getVips(): string[] {
    return Array.from(this.vips);
  }

  /**
   * Get all subscribers
   */
  getSubscribers(): string[] {
    return Array.from(this.subscribers);
  }

  /**
   * Get all followers
   */
  getFollowers(): string[] {
    return Array.from(this.followers);
  }

  /**
   * Get all banned users
   */
  getBannedUsers(): string[] {
    return Array.from(this.bannedUsers);
  }

  /**
   * Get active users (seen in last 10 minutes)
   */
  getActiveUsers(): string[] {
    const activeUsers: string[] = [];
    const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000);

    for (const [username, userData] of Object.entries(this.userCache)) {
      if (userData.lastSeen > tenMinutesAgo) {
        activeUsers.push(userData.user.username);
      }
    }

    return activeUsers;
  }

  /**
   * Get top users by points
   */
  getTopUsersByPoints(limit: number = 10): Array<{ username: string; points: number }> {
    const users = Object.values(this.userCache)
      .map(userData => ({
        username: userData.user.username,
        points: userData.points,
      }))
      .sort((a, b) => b.points - a.points)
      .slice(0, limit);

    return users;
  }

  /**
   * Get top users by message count
   */
  getTopUsersByMessages(limit: number = 10): Array<{ username: string; messages: number }> {
    const users = Object.values(this.userCache)
      .map(userData => ({
        username: userData.user.username,
        messages: userData.messageCount,
      }))
      .sort((a, b) => b.messages - a.messages)
      .slice(0, limit);

    return users;
  }

  /**
   * Clear inactive users from cache (not seen in 24 hours)
   */
  clearInactiveUsers(): number {
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    let clearedCount = 0;

    for (const [username, userData] of Object.entries(this.userCache)) {
      if (userData.lastSeen < oneDayAgo) {
        delete this.userCache[username];
        clearedCount++;
      }
    }

    if (clearedCount > 0) {
      logger.info(`Cleared ${clearedCount} inactive users from cache`);
    }

    return clearedCount;
  }

  /**
   * Get user cache statistics
   */
  getCacheStats() {
    const totalUsers = Object.keys(this.userCache).length;
    const activeUsers = this.getActiveUsers().length;

    return {
      totalUsers,
      activeUsers,
      moderators: this.moderators.size,
      vips: this.vips.size,
      subscribers: this.subscribers.size,
      followers: this.followers.size,
      bannedUsers: this.bannedUsers.size,
    };
  }

  /**
   * Export user data for backup
   */
  exportUserData(): UserCache {
    return { ...this.userCache };
  }

  /**
   * Import user data from backup
   */
  importUserData(data: UserCache): void {
    this.userCache = { ...data };
    
    // Rebuild permission sets
    for (const userData of Object.values(this.userCache)) {
      this.updateUserPermissions(userData.user);
    }
    
    logger.info(`Imported ${Object.keys(data).length} users from backup`);
  }
}
