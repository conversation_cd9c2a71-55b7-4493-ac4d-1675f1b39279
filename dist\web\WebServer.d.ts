import { Request } from 'express';
import { FullKickBot } from '../FullKickBot';
export interface AuthRequest extends Request {
    user?: {
        username: string;
        isAdmin: boolean;
    };
}
export declare class WebServer {
    private app;
    private server?;
    private bot;
    constructor(bot: FullKickBot);
    private setupMiddleware;
    private setupRoutes;
    private authenticateToken;
    private handleLogin;
    private handleLogout;
    private handleMe;
    private getBotStatus;
    private getBotStats;
    private restartBot;
    start(): Promise<void>;
    private getCommands;
    private createCommand;
    private updateCommand;
    private deleteCommand;
    private getModerationStats;
    private getModerationLogs;
    private addBannedWord;
    private removeBannedWord;
    private getCurrencyStats;
    private getCurrencyLeaderboard;
    private givePoints;
    private getGiveaways;
    private createGiveaway;
    private endGiveaway;
    private cancelGiveaway;
    private getUsers;
    private getUser;
    private getChatLogs;
    stop(): Promise<void>;
}
//# sourceMappingURL=WebServer.d.ts.map