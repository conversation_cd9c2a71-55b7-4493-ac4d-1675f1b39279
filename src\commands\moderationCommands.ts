import { <PERSON><PERSON><PERSON><PERSON>, CommandContext } from './CommandHandler';
import { ModerationManager } from '../moderation/ModerationManager';
import { PermissionLevel } from '../types';
import { logger } from '../utils/logger';

/**
 * Register moderation commands
 */
export function registerModerationCommands(commandHandler: CommandHandler, moderationManager: ModerationManager): void {
  // Add banned word
  commandHandler.registerCommand({
    name: 'addword',
    description: 'Add a banned word',
    usage: '!addword <word>',
    aliases: ['banword'],
    cooldown: 0,
    permission: PermissionLevel.MODERATOR,
    enabled: true,
    created_by: 'system',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }, async (context) => await addBannedWordCommand(context, moderationManager));

  // Remove banned word
  commandHandler.registerCommand({
    name: 'removeword',
    description: 'Remove a banned word',
    usage: '!removeword <word>',
    aliases: ['unbanword'],
    cooldown: 0,
    permission: PermissionLevel.MODERATOR,
    enabled: true,
    created_by: 'system',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }, async (context) => await removeBannedWordCommand(context, moderationManager));

  // Toggle moderation
  commandHandler.registerCommand({
    name: 'moderation',
    description: 'Toggle moderation on/off',
    usage: '!moderation <on|off>',
    aliases: ['mod'],
    cooldown: 0,
    permission: PermissionLevel.MODERATOR,
    enabled: true,
    created_by: 'system',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }, async (context) => await toggleModerationCommand(context, moderationManager));

  // Moderation stats
  commandHandler.registerCommand({
    name: 'modstats',
    description: 'Show moderation statistics',
    usage: '!modstats',
    aliases: ['moderationstats'],
    cooldown: 10,
    permission: PermissionLevel.MODERATOR,
    enabled: true,
    created_by: 'system',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }, async (context) => await moderationStatsCommand(context, moderationManager));

  // User moderation info
  commandHandler.registerCommand({
    name: 'usermod',
    description: 'Show user moderation information',
    usage: '!usermod <username>',
    aliases: ['userinfo'],
    cooldown: 5,
    permission: PermissionLevel.MODERATOR,
    enabled: true,
    created_by: 'system',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }, async (context) => await userModerationCommand(context, moderationManager));

  // Clear user moderation data
  commandHandler.registerCommand({
    name: 'clearuser',
    description: 'Clear user moderation data',
    usage: '!clearuser <username>',
    aliases: ['cleanusermod'],
    cooldown: 0,
    permission: PermissionLevel.MODERATOR,
    enabled: true,
    created_by: 'system',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }, async (context) => await clearUserModerationCommand(context, moderationManager));

  // Slow mode
  commandHandler.registerCommand({
    name: 'slow',
    description: 'Enable/disable slow mode',
    usage: '!slow <on|off> [interval]',
    aliases: ['slowmode'],
    cooldown: 0,
    permission: PermissionLevel.MODERATOR,
    enabled: true,
    created_by: 'system',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }, async (context) => await slowModeCommand(context));

  // Followers only
  commandHandler.registerCommand({
    name: 'followers',
    description: 'Enable/disable followers only mode',
    usage: '!followers <on|off> [duration]',
    aliases: ['followersonly'],
    cooldown: 0,
    permission: PermissionLevel.MODERATOR,
    enabled: true,
    created_by: 'system',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }, async (context) => await followersOnlyCommand(context));

  // Subscribers only
  commandHandler.registerCommand({
    name: 'subs',
    description: 'Enable/disable subscribers only mode',
    usage: '!subs <on|off>',
    aliases: ['subsonly', 'subscribers'],
    cooldown: 0,
    permission: PermissionLevel.MODERATOR,
    enabled: true,
    created_by: 'system',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }, async (context) => await subscribersOnlyCommand(context));

  logger.info('Registered moderation commands');
}

/**
 * Add banned word command
 */
async function addBannedWordCommand(context: CommandContext, moderationManager: ModerationManager): Promise<void> {
  const { bot, message, args } = context;
  
  if (args.length === 0) {
    await bot.replyToMessage(message, 'Usage: !addword <word>');
    return;
  }
  
  const word = args[0];
  moderationManager.addBannedWord(word);
  await bot.replyToMessage(message, `✅ Added "${word}" to banned words list.`);
}

/**
 * Remove banned word command
 */
async function removeBannedWordCommand(context: CommandContext, moderationManager: ModerationManager): Promise<void> {
  const { bot, message, args } = context;
  
  if (args.length === 0) {
    await bot.replyToMessage(message, 'Usage: !removeword <word>');
    return;
  }
  
  const word = args[0];
  const success = moderationManager.removeBannedWord(word);
  
  if (success) {
    await bot.replyToMessage(message, `✅ Removed "${word}" from banned words list.`);
  } else {
    await bot.replyToMessage(message, `❌ "${word}" was not found in banned words list.`);
  }
}

/**
 * Toggle moderation command
 */
async function toggleModerationCommand(context: CommandContext, moderationManager: ModerationManager): Promise<void> {
  const { bot, message, args } = context;
  
  if (args.length === 0) {
    await bot.replyToMessage(message, 'Usage: !moderation <on|off>');
    return;
  }
  
  const action = args[0].toLowerCase();
  
  if (action === 'on' || action === 'enable') {
    moderationManager.updateConfig({ enabled: true });
    await bot.replyToMessage(message, '✅ Auto-moderation enabled.');
  } else if (action === 'off' || action === 'disable') {
    moderationManager.updateConfig({ enabled: false });
    await bot.replyToMessage(message, '❌ Auto-moderation disabled.');
  } else {
    await bot.replyToMessage(message, 'Usage: !moderation <on|off>');
  }
}

/**
 * Moderation stats command
 */
async function moderationStatsCommand(context: CommandContext, moderationManager: ModerationManager): Promise<void> {
  const { bot, message } = context;
  
  const stats = moderationManager.getModerationStats();
  
  const response = `📊 Moderation Stats: ${stats.totalUsers} users tracked | ${stats.totalViolations} violations | ${stats.totalTimeouts} timeouts | ${stats.totalBans} bans | Status: ${stats.config.enabled ? 'Enabled' : 'Disabled'}`;
  
  await bot.replyToMessage(message, response);
}

/**
 * User moderation command
 */
async function userModerationCommand(context: CommandContext, moderationManager: ModerationManager): Promise<void> {
  const { bot, message, args } = context;
  
  if (args.length === 0) {
    await bot.replyToMessage(message, 'Usage: !usermod <username>');
    return;
  }
  
  const username = args[0];
  const userData = moderationManager.getUserModerationData(username);
  
  if (!userData) {
    await bot.replyToMessage(message, `No moderation data found for ${username}.`);
    return;
  }
  
  const response = `👤 ${userData.username}: ${userData.violations.length} violations | ${userData.timeoutCount} timeouts | ${userData.banCount} bans | ${userData.messageCount} messages`;
  
  await bot.replyToMessage(message, response);
}

/**
 * Clear user moderation command
 */
async function clearUserModerationCommand(context: CommandContext, moderationManager: ModerationManager): Promise<void> {
  const { bot, message, args } = context;
  
  if (args.length === 0) {
    await bot.replyToMessage(message, 'Usage: !clearuser <username>');
    return;
  }
  
  const username = args[0];
  const success = moderationManager.clearUserData(username);
  
  if (success) {
    await bot.replyToMessage(message, `✅ Cleared moderation data for ${username}.`);
  } else {
    await bot.replyToMessage(message, `❌ No moderation data found for ${username}.`);
  }
}

/**
 * Slow mode command
 */
async function slowModeCommand(context: CommandContext): Promise<void> {
  const { bot, message, args } = context;
  
  if (args.length === 0) {
    await bot.replyToMessage(message, 'Usage: !slow <on|off> [interval]');
    return;
  }
  
  const action = args[0].toLowerCase();
  
  if (action === 'on' || action === 'enable') {
    const interval = args.length > 1 ? parseInt(args[1]) : 30;
    
    if (isNaN(interval) || interval < 1 || interval > 300) {
      await bot.replyToMessage(message, 'Invalid interval. Must be between 1-300 seconds.');
      return;
    }
    
    // This would need to be implemented in the channel manager
    await bot.replyToMessage(message, `✅ Slow mode enabled (${interval}s interval).`);
  } else if (action === 'off' || action === 'disable') {
    // This would need to be implemented in the channel manager
    await bot.replyToMessage(message, '❌ Slow mode disabled.');
  } else {
    await bot.replyToMessage(message, 'Usage: !slow <on|off> [interval]');
  }
}

/**
 * Followers only command
 */
async function followersOnlyCommand(context: CommandContext): Promise<void> {
  const { bot, message, args } = context;
  
  if (args.length === 0) {
    await bot.replyToMessage(message, 'Usage: !followers <on|off> [duration]');
    return;
  }
  
  const action = args[0].toLowerCase();
  
  if (action === 'on' || action === 'enable') {
    const duration = args.length > 1 ? parseInt(args[1]) : 0;
    await bot.replyToMessage(message, `✅ Followers only mode enabled${duration > 0 ? ` (${duration}m minimum follow time)` : ''}.`);
  } else if (action === 'off' || action === 'disable') {
    await bot.replyToMessage(message, '❌ Followers only mode disabled.');
  } else {
    await bot.replyToMessage(message, 'Usage: !followers <on|off> [duration]');
  }
}

/**
 * Subscribers only command
 */
async function subscribersOnlyCommand(context: CommandContext): Promise<void> {
  const { bot, message, args } = context;
  
  if (args.length === 0) {
    await bot.replyToMessage(message, 'Usage: !subs <on|off>');
    return;
  }
  
  const action = args[0].toLowerCase();
  
  if (action === 'on' || action === 'enable') {
    await bot.replyToMessage(message, '✅ Subscribers only mode enabled.');
  } else if (action === 'off' || action === 'disable') {
    await bot.replyToMessage(message, '❌ Subscribers only mode disabled.');
  } else {
    await bot.replyToMessage(message, 'Usage: !subs <on|off>');
  }
}
