{"version": 3, "file": "CommandHandler.js", "sourceRoot": "", "sources": ["../../src/commands/CommandHandler.ts"], "names": [], "mappings": ";;;AAAA,mCAAsC;AACtC,oCAAiE;AAEjE,4CAAyC;AAYzC,MAAa,cAAe,SAAQ,qBAAY;IAK9C,YAAoB,GAAY;QAC9B,KAAK,EAAE,CAAC;QADU,QAAG,GAAH,GAAG,CAAS;QAJxB,aAAQ,GAAG,IAAI,GAAG,EAAmB,CAAC;QACtC,qBAAgB,GAAG,IAAI,GAAG,EAA2B,CAAC;QACtD,cAAS,GAAG,IAAI,GAAG,EAA+B,CAAC;QAIzD,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAKO,cAAc;QACpB,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,EAAE,KAAK,EAAE,WAAgB,EAAE,EAAE;YAChD,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC;IAKD,eAAe,CAAC,OAAgB,EAAE,OAAwB;QAExD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACzC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAGjD,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAClC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAC5C,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,uBAAuB,OAAO,CAAC,IAAI,cAAc,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC9F,CAAC;IAKD,iBAAiB,CAAC,WAAmB;QACnC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAC/C,IAAI,CAAC,OAAO;YAAE,OAAO,KAAK,CAAC;QAG3B,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACnC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAG3C,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5B,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC;QAGD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEpC,eAAM,CAAC,IAAI,CAAC,yBAAyB,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;QACrD,OAAO,IAAI,CAAC;IACd,CAAC;IAKO,KAAK,CAAC,aAAa,CAAC,WAAgB;QAC1C,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,WAAW,CAAC;QAElD,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,oBAAoB,IAAI,EAAE,CAAC,CAAC;YACzC,OAAO;QACT,CAAC;QAGD,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACrB,eAAM,CAAC,KAAK,CAAC,qBAAqB,IAAI,EAAE,CAAC,CAAC;YAC1C,OAAO;QACT,CAAC;QAGD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YACtD,MAAM,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,OAAO,EAAE,iDAAiD,CAAC,CAAC;YAC1F,OAAO;QACT,CAAC;QAGD,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnD,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC7E,MAAM,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,OAAO,EAAE,uCAAuC,aAAa,WAAW,CAAC,CAAC;YACxG,OAAO;QACT,CAAC;QAGD,IAAI,CAAC;YACH,MAAM,OAAO,GAAmB;gBAC9B,GAAG,EAAE,IAAI,CAAC,GAAG;gBACb,OAAO;gBACP,IAAI;gBACJ,IAAI;gBACJ,OAAO;aACR,CAAC;YAEF,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAChD,IAAI,OAAO,EAAE,CAAC;gBACZ,MAAM,OAAO,CAAC,OAAO,CAAC,CAAC;gBAGvB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;gBAEhE,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;oBAC5B,OAAO,EAAE,OAAO,CAAC,IAAI;oBACrB,IAAI,EAAE,IAAI,CAAC,QAAQ;oBACnB,IAAI;oBACJ,OAAO,EAAE,IAAI;iBACd,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YACxD,MAAM,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,OAAO,EAAE,gDAAgD,CAAC,CAAC;YAEzF,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;gBACzB,OAAO,EAAE,OAAO,CAAC,IAAI;gBACrB,IAAI,EAAE,IAAI,CAAC,QAAQ;gBACnB,KAAK;aACN,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAKO,YAAY,CAAC,WAAmB,EAAE,QAAgB;QACxD,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACzD,IAAI,CAAC,gBAAgB;YAAE,OAAO,KAAK,CAAC;QAEpC,MAAM,YAAY,GAAG,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,YAAY;YAAE,OAAO,KAAK,CAAC;QAEhC,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,YAAY,CAAC;IACnC,CAAC;IAKO,oBAAoB,CAAC,WAAmB,EAAE,QAAgB;QAChE,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACzD,IAAI,CAAC,gBAAgB;YAAE,OAAO,CAAC,CAAC;QAEhC,MAAM,YAAY,GAAG,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,YAAY;YAAE,OAAO,CAAC,CAAC;QAE5B,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;QAChE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;IAChC,CAAC;IAKO,WAAW,CAAC,WAAmB,EAAE,QAAgB,EAAE,eAAuB;QAChF,IAAI,eAAe,IAAI,CAAC;YAAE,OAAO;QAEjC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;YACrC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;QAC7C,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAE,CAAC;QAC1D,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,eAAe,GAAG,IAAI,CAAC,CAAC;QAC1D,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QAG5C,UAAU,CAAC,GAAG,EAAE;YACd,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAClC,IAAI,gBAAgB,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gBAChC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YACrC,CAAC;QACH,CAAC,EAAE,eAAe,GAAG,IAAI,CAAC,CAAC;IAC7B,CAAC;IAKD,WAAW;QACT,MAAM,cAAc,GAAG,IAAI,GAAG,EAAmB,CAAC;QAElD,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5C,IAAI,IAAI,KAAK,OAAO,CAAC,IAAI,EAAE,CAAC;gBAC1B,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACpC,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;IAC7C,CAAC;IAKD,UAAU,CAAC,IAAY;QACrB,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAKD,uBAAuB,CAAC,eAAgC;QACtD,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,IAAI,eAAe,CAAC,CAAC;IAC7E,CAAC;IAKD,aAAa,CAAC,IAAY,EAAE,OAAyB;QACnD,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,CAAC,OAAO;YAAE,OAAO,KAAK,CAAC;QAG3B,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAGhC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACzC,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACpC,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,oBAAoB,IAAI,EAAE,CAAC,CAAC;QACxC,OAAO,IAAI,CAAC;IACd,CAAC;IAKD,cAAc,CAAC,WAAmB;QAChC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QACnC,eAAM,CAAC,IAAI,CAAC,kCAAkC,WAAW,EAAE,CAAC,CAAC;IAC/D,CAAC;IAKD,kBAAkB,CAAC,QAAgB;QACjC,KAAK,MAAM,CAAC,WAAW,EAAE,gBAAgB,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAC7D,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAClC,IAAI,gBAAgB,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gBAChC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;QACD,eAAM,CAAC,IAAI,CAAC,mCAAmC,QAAQ,EAAE,CAAC,CAAC;IAC7D,CAAC;IAKD,eAAe;QACb,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACpC,MAAM,KAAK,GAAG;YACZ,aAAa,EAAE,QAAQ,CAAC,MAAM;YAC9B,eAAe,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,MAAM;YAC3D,gBAAgB,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,MAAM;YAC7D,oBAAoB,EAAE;gBACpB,CAAC,uBAAe,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,KAAK,uBAAe,CAAC,QAAQ,CAAC,CAAC,MAAM;gBACtG,CAAC,uBAAe,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,KAAK,uBAAe,CAAC,QAAQ,CAAC,CAAC,MAAM;gBACtG,CAAC,uBAAe,CAAC,UAAU,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,KAAK,uBAAe,CAAC,UAAU,CAAC,CAAC,MAAM;gBAC1G,CAAC,uBAAe,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,KAAK,uBAAe,CAAC,GAAG,CAAC,CAAC,MAAM;gBAC5F,CAAC,uBAAe,CAAC,SAAS,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,KAAK,uBAAe,CAAC,SAAS,CAAC,CAAC,MAAM;gBACxG,CAAC,uBAAe,CAAC,KAAK,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,KAAK,uBAAe,CAAC,KAAK,CAAC,CAAC,MAAM;aACjG;YACD,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;SACrC,CAAC;QAEF,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AA7QD,wCA6QC"}