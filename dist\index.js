"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerDefaultCommands = exports.CommandHandler = exports.KickBot = void 0;
const KickBot_1 = require("./bot/KickBot");
Object.defineProperty(exports, "KickBot", { enumerable: true, get: function () { return KickBot_1.KickBot; } });
const CommandHandler_1 = require("./commands/CommandHandler");
Object.defineProperty(exports, "CommandHandler", { enumerable: true, get: function () { return CommandHandler_1.CommandHandler; } });
const defaultCommands_1 = require("./commands/defaultCommands");
Object.defineProperty(exports, "registerDefaultCommands", { enumerable: true, get: function () { return defaultCommands_1.registerDefaultCommands; } });
const config_1 = require("./config");
const logger_1 = require("./utils/logger");
async function main() {
    try {
        logger_1.logger.info('Starting KickBot application...');
        if (!config_1.config.username || !config_1.config.channel) {
            throw new Error('Bot username and channel must be configured in environment variables');
        }
        if (!config_1.config.password && !config_1.config.token) {
            throw new Error('Either bot password or token must be configured');
        }
        const bot = new KickBot_1.KickBot(config_1.config);
        const commandHandler = new CommandHandler_1.CommandHandler(bot);
        (0, defaultCommands_1.registerDefaultCommands)(commandHandler);
        setupBotEventHandlers(bot, commandHandler);
        setupGracefulShutdown(bot);
        await bot.start();
        logger_1.logger.info('KickBot is now running! Press Ctrl+C to stop.');
    }
    catch (error) {
        logger_1.logger.error('Failed to start KickBot:', error);
        process.exit(1);
    }
}
function setupBotEventHandlers(bot, commandHandler) {
    bot.on('started', () => {
        logger_1.logger.info('Bot started successfully');
    });
    bot.on('stopped', () => {
        logger_1.logger.info('Bot stopped');
    });
    bot.on('connected', () => {
        logger_1.logger.info('Connected to Kick WebSocket');
    });
    bot.on('subscribed', () => {
        logger_1.logger.info('Subscribed to channel chat');
    });
    bot.on('message', (message) => {
    });
    bot.on('new_follower', (data) => {
        bot.sendMessage(`Welcome to the stream, @${data.username}! Thanks for following! 🎉`);
    });
    bot.on('user_banned', (data) => {
        logger_1.logger.info(`User banned: ${data.user?.username}`);
    });
    bot.on('user_unbanned', (data) => {
        logger_1.logger.info(`User unbanned: ${data.user?.username}`);
    });
    bot.on('streamer_live', (data) => {
        logger_1.logger.info('Streamer went live');
    });
    bot.on('streamer_offline', (data) => {
        logger_1.logger.info('Streamer went offline');
    });
    bot.on('error', (error) => {
        logger_1.logger.error('Bot error:', error);
        setTimeout(async () => {
            try {
                logger_1.logger.info('Attempting to restart bot after error...');
                await bot.stop();
                await bot.start();
            }
            catch (restartError) {
                logger_1.logger.error('Failed to restart bot:', restartError);
                process.exit(1);
            }
        }, 5000);
    });
    commandHandler.on('command_executed', (data) => {
        logger_1.logger.info(`Command executed: ${data.command} by ${data.user}`);
    });
    commandHandler.on('command_error', (data) => {
        logger_1.logger.error(`Command error: ${data.command} by ${data.user}:`, data.error);
    });
}
function setupGracefulShutdown(bot) {
    const shutdown = async (signal) => {
        logger_1.logger.info(`Received ${signal}, shutting down gracefully...`);
        try {
            await bot.stop();
            logger_1.logger.info('Bot stopped successfully');
            process.exit(0);
        }
        catch (error) {
            logger_1.logger.error('Error during shutdown:', error);
            process.exit(1);
        }
    };
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGQUIT', () => shutdown('SIGQUIT'));
    process.on('uncaughtException', (error) => {
        logger_1.logger.error('Uncaught exception:', error);
        shutdown('uncaughtException');
    });
    process.on('unhandledRejection', (reason, promise) => {
        logger_1.logger.error('Unhandled promise rejection:', { reason, promise });
        shutdown('unhandledRejection');
    });
}
if (require.main === module) {
    main().catch((error) => {
        logger_1.logger.error('Application startup failed:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=index.js.map