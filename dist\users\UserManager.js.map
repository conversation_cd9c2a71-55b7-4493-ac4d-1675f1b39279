{"version": 3, "file": "UserManager.js", "sourceRoot": "", "sources": ["../../src/users/UserManager.ts"], "names": [], "mappings": ";;;AAAA,mCAAsC;AACtC,oCAAgE;AAChE,4CAAyC;AACzC,4CAAyC;AAazC,MAAa,WAAY,SAAQ,qBAAY;IAQ3C;QACE,KAAK,EAAE,CAAC;QARF,cAAS,GAAc,EAAE,CAAC;QAC1B,eAAU,GAAG,IAAI,GAAG,EAAU,CAAC;QAC/B,SAAI,GAAG,IAAI,GAAG,EAAU,CAAC;QACzB,gBAAW,GAAG,IAAI,GAAG,EAAU,CAAC;QAChC,cAAS,GAAG,IAAI,GAAG,EAAU,CAAC;QAC9B,gBAAW,GAAG,IAAI,GAAG,EAAU,CAAC;IAIxC,CAAC;IAKD,UAAU,CAAC,IAAc;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;QAC7C,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QAEvB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG;gBACzB,IAAI;gBACJ,QAAQ,EAAE,GAAG;gBACb,YAAY,EAAE,CAAC;gBACf,YAAY,EAAE,CAAC;gBACf,UAAU,EAAE,CAAC;gBACb,MAAM,EAAE,CAAC;aACV,CAAC;YAEF,eAAM,CAAC,KAAK,CAAC,4BAA4B,QAAQ,EAAE,CAAC,CAAC;QACvD,CAAC;aAAM,CAAC;YAEN,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC;YACrC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,QAAQ,GAAG,GAAG,CAAC;QAC1C,CAAC;QAGD,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAKO,qBAAqB,CAAC,IAAc;QAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;QAG7C,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAChC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACnC,CAAC;QAGD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC1B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC7B,CAAC;QAGD,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACpC,CAAC;QAGD,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC/B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC;QAGD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,OAAO,CAAC,QAAgB;QAC5B,MAAM,kBAAkB,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;QAGlD,IAAI,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACvC,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC;QACjD,CAAC;QAGD,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,iBAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC7C,IAAI,IAAI,EAAE,CAAC;gBACT,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBACtB,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;QAC3D,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAKD,sBAAsB,CAAC,IAAuB;QAC5C,IAAI,QAAQ,GAAoB,IAAI,CAAC;QAErC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,MAAM,kBAAkB,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YAC9C,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAAE,IAAI,IAAI,IAAI,CAAC;QAC9D,CAAC;aAAM,CAAC;YACN,QAAQ,GAAG,IAAI,CAAC;QAClB,CAAC;QAED,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,uBAAe,CAAC,QAAQ,CAAC;QAClC,CAAC;QAED,IAAI,QAAQ,CAAC,gBAAgB;YAAE,OAAO,uBAAe,CAAC,KAAK,CAAC;QAC5D,IAAI,QAAQ,CAAC,YAAY;YAAE,OAAO,uBAAe,CAAC,SAAS,CAAC;QAC5D,IAAI,QAAQ,CAAC,MAAM;YAAE,OAAO,uBAAe,CAAC,GAAG,CAAC;QAChD,IAAI,QAAQ,CAAC,aAAa;YAAE,OAAO,uBAAe,CAAC,UAAU,CAAC;QAC9D,IAAI,QAAQ,CAAC,WAAW;YAAE,OAAO,uBAAe,CAAC,QAAQ,CAAC;QAE1D,OAAO,uBAAe,CAAC,QAAQ,CAAC;IAClC,CAAC;IAKD,aAAa,CAAC,IAAuB,EAAE,aAA8B;QACnE,MAAM,SAAS,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QACpD,OAAO,SAAS,IAAI,aAAa,CAAC;IACpC,CAAC;IAKD,qBAAqB,CAAC,QAAgB;QACpC,MAAM,kBAAkB,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;QAClD,IAAI,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACvC,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,YAAY,EAAE,CAAC;YAClD,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;QAC3D,CAAC;IACH,CAAC;IAKD,qBAAqB,CAAC,QAAgB;QACpC,MAAM,kBAAkB,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;QAClD,IAAI,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACvC,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,YAAY,EAAE,CAAC;QACpD,CAAC;IACH,CAAC;IAKD,SAAS,CAAC,QAAgB,EAAE,MAAc;QACxC,MAAM,kBAAkB,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;QAClD,IAAI,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACvC,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC;YACpD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;QACpG,CAAC;IACH,CAAC;IAKD,YAAY,CAAC,QAAgB,EAAE,MAAc;QAC3C,MAAM,kBAAkB,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;QAClD,IAAI,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACvC,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAAC;YAChE,IAAI,aAAa,IAAI,MAAM,EAAE,CAAC;gBAC5B,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC;gBACpD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;gBACpG,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAKD,YAAY,CAAC,QAAgB;QAC3B,MAAM,kBAAkB,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;QAClD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;QAEpD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO;YACL,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;YAChC,aAAa,EAAE,QAAQ,CAAC,YAAY;YACpC,aAAa,EAAE,QAAQ,CAAC,YAAY;YACpC,YAAY,EAAE,QAAQ,CAAC,UAAU;YACjC,MAAM,EAAE,QAAQ,CAAC,MAAM;YACvB,SAAS,EAAE,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE;YAC1C,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;SACvC,CAAC;IACJ,CAAC;IAKD,YAAY,CAAC,QAAgB;QAC3B,MAAM,kBAAkB,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;QAClD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;QAEpD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAC5D,OAAO,QAAQ,CAAC,QAAQ,GAAG,aAAa,CAAC;IAC3C,CAAC;IAKD,aAAa;QACX,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACrC,CAAC;IAKD,OAAO;QACL,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAKD,cAAc;QACZ,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACtC,CAAC;IAKD,YAAY;QACV,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAKD,cAAc;QACZ,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACtC,CAAC;IAKD,cAAc;QACZ,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAE5D,KAAK,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAClE,IAAI,QAAQ,CAAC,QAAQ,GAAG,aAAa,EAAE,CAAC;gBACtC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAKD,mBAAmB,CAAC,QAAgB,EAAE;QACpC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;aACxC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAChB,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;YAChC,MAAM,EAAE,QAAQ,CAAC,MAAM;SACxB,CAAC,CAAC;aACF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;aACnC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAEnB,OAAO,KAAK,CAAC;IACf,CAAC;IAKD,qBAAqB,CAAC,QAAgB,EAAE;QACtC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;aACxC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAChB,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;YAChC,QAAQ,EAAE,QAAQ,CAAC,YAAY;SAChC,CAAC,CAAC;aACF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC;aACvC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAEnB,OAAO,KAAK,CAAC;IACf,CAAC;IAKD,kBAAkB;QAChB,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAC7D,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,KAAK,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAClE,IAAI,QAAQ,CAAC,QAAQ,GAAG,SAAS,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;gBAChC,YAAY,EAAE,CAAC;YACjB,CAAC;QACH,CAAC;QAED,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YACrB,eAAM,CAAC,IAAI,CAAC,WAAW,YAAY,4BAA4B,CAAC,CAAC;QACnE,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAKD,aAAa;QACX,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;QACtD,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC;QAEjD,OAAO;YACL,UAAU;YACV,WAAW;YACX,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI;YAChC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;YACpB,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI;YAClC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;YAC9B,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI;SACnC,CAAC;IACJ,CAAC;IAKD,cAAc;QACZ,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;IAC/B,CAAC;IAKD,cAAc,CAAC,IAAe;QAC5B,IAAI,CAAC,SAAS,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;QAG7B,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YACrD,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC5C,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,YAAY,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,oBAAoB,CAAC,CAAC;IACxE,CAAC;CACF;AA9WD,kCA8WC"}