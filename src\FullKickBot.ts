import { KickBot } from './bot/KickBot';
import { CommandHandler } from './commands/CommandHandler';
import { CustomCommandManager } from './commands/CustomCommandManager';
import { ModerationManager } from './moderation/ModerationManager';
import { CurrencySystem } from './features/CurrencySystem';
import { GiveawaySystem } from './features/GiveawaySystem';
import { UserManager } from './users/UserManager';
import { ChannelManager } from './channels/ChannelManager';
import { database } from './database/Database';
import { registerDefaultCommands } from './commands/defaultCommands';
import { registerModerationCommands } from './commands/moderationCommands';
import { registerAdvancedCommands } from './commands/advancedCommands';
import { config } from './config';
import { logger } from './utils/logger';

export interface FullKickBotConfig {
  // Bot configuration
  bot: {
    username: string;
    password?: string;
    token?: string;
    channel: string;
    prefix: string;
  };
  
  // Feature toggles
  features: {
    moderation: boolean;
    customCommands: boolean;
    currency: boolean;
    giveaways: boolean;
    analytics: boolean;
    webDashboard: boolean;
  };
  
  // Currency system configuration
  currency: {
    enabled: boolean;
    currencyName: string;
    currencySymbol: string;
    passiveEarning: {
      enabled: boolean;
      amount: number;
      interval: number;
      requireActive: boolean;
    };
    messageRewards: {
      enabled: boolean;
      amount: number;
      cooldown: number;
    };
    followRewards: {
      enabled: boolean;
      amount: number;
    };
    subscribeRewards: {
      enabled: boolean;
      amount: number;
    };
    dailyBonus: {
      enabled: boolean;
      amount: number;
      streakMultiplier: number;
    };
  };
  
  // Moderation configuration
  moderation: {
    enabled: boolean;
    spamProtection: {
      enabled: boolean;
      maxMessages: number;
      timeWindow: number;
      punishment: 'timeout' | 'ban';
      duration: number;
    };
    capsFilter: {
      enabled: boolean;
      threshold: number;
      minLength: number;
      punishment: 'timeout' | 'delete' | 'warn';
      duration: number;
    };
    bannedWords: {
      enabled: boolean;
      words: string[];
      punishment: 'timeout' | 'ban' | 'delete';
      duration: number;
    };
    linkProtection: {
      enabled: boolean;
      allowedDomains: string[];
      punishment: 'timeout' | 'delete' | 'warn';
      duration: number;
    };
    repetitiveMessages: {
      enabled: boolean;
      maxRepeats: number;
      timeWindow: number;
      punishment: 'timeout' | 'delete';
      duration: number;
    };
    autoTimeout: {
      enabled: boolean;
      escalation: boolean;
    };
  };
}

export class FullKickBot {
  private bot: KickBot;
  private commandHandler: CommandHandler;
  private customCommandManager?: CustomCommandManager;
  private moderationManager?: ModerationManager;
  private currencySystem?: CurrencySystem;
  private giveawaySystem?: GiveawaySystem;
  private userManager: UserManager;
  private channelManager: ChannelManager;
  private config: FullKickBotConfig;
  private isRunning = false;

  constructor(config: FullKickBotConfig) {
    this.config = config;
    
    // Initialize core components
    this.bot = new KickBot({
      username: config.bot.username,
      password: config.bot.password,
      token: config.bot.token,
      channel: config.bot.channel,
      prefix: config.bot.prefix,
      features: {
        moderation: config.features.moderation,
        commands: config.features.customCommands,
        songRequests: false, // TODO: Implement
        games: true,
        analytics: config.features.analytics,
      },
      moderation: {
        maxMessageLength: 500,
        spamThreshold: config.moderation.spamProtection.maxMessages,
        capsThreshold: config.moderation.capsFilter.threshold,
        linkProtection: config.moderation.linkProtection.enabled,
        bannedWords: config.moderation.bannedWords.words,
        autoTimeout: config.moderation.autoTimeout.enabled,
        timeoutDuration: config.moderation.spamProtection.duration,
      },
    });

    this.commandHandler = new CommandHandler(this.bot);
    this.userManager = new UserManager();
    this.channelManager = new ChannelManager();

    this.initializeFeatures();
    this.setupEventHandlers();
  }

  /**
   * Initialize optional features based on configuration
   */
  private initializeFeatures(): void {
    // Custom commands
    if (this.config.features.customCommands) {
      this.customCommandManager = new CustomCommandManager(this.commandHandler);
    }

    // Moderation system
    if (this.config.features.moderation) {
      this.moderationManager = new ModerationManager(this.bot, this.config.moderation);
    }

    // Currency system
    if (this.config.features.currency) {
      this.currencySystem = new CurrencySystem(this.bot, this.config.currency);
    }

    // Giveaway system
    if (this.config.features.giveaways) {
      this.giveawaySystem = new GiveawaySystem(this.bot);
    }
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    // Bot events
    this.bot.on('started', () => {
      logger.info('🤖 FullKickBot started successfully!');
      this.isRunning = true;
    });

    this.bot.on('stopped', () => {
      logger.info('🤖 FullKickBot stopped');
      this.isRunning = false;
    });

    this.bot.on('message', (message) => {
      // Update user data
      this.userManager.updateUser(message.sender);
      this.userManager.incrementMessageCount(message.sender.username);
      
      // Log message to database
      if (this.config.features.analytics) {
        database.logMessage(
          message.sender.username,
          message.content,
          this.config.bot.channel,
          'message'
        );
      }
    });

    this.bot.on('command', (commandData) => {
      this.userManager.incrementCommandCount(commandData.user.username);
    });

    this.bot.on('new_follower', (data) => {
      logger.info(`🎉 New follower: ${data.username}`);
      this.bot.sendMessage(`Welcome to the stream, @${data.username}! Thanks for following! 🎉`);
    });

    // Currency system events
    if (this.currencySystem) {
      this.currencySystem.on('points_added', (data) => {
        logger.debug(`💰 ${data.username} earned ${data.amount} points: ${data.reason}`);
      });

      this.currencySystem.on('points_transferred', (data) => {
        logger.info(`💸 ${data.from} transferred ${data.amount} points to ${data.to}`);
      });
    }

    // Giveaway system events
    if (this.giveawaySystem) {
      this.giveawaySystem.on('giveaway_created', (giveaway) => {
        logger.info(`🎁 Giveaway created: ${giveaway.title}`);
      });

      this.giveawaySystem.on('giveaway_ended', (data) => {
        logger.info(`🏆 Giveaway "${data.giveaway.title}" won by ${data.winner}`);
      });
    }

    // Moderation events
    if (this.moderationManager) {
      this.moderationManager.on('violations_detected', (data) => {
        logger.warn(`⚠️ Violations detected for ${data.user.username}: ${data.violations.join(', ')}`);
      });

      this.moderationManager.on('punishment_executed', (data) => {
        logger.info(`🔨 Punishment: ${data.punishment.type} for ${data.user.username}`);
        
        // Log to database
        if (this.config.features.analytics) {
          database.logModerationAction(
            data.user.username,
            data.punishment.type,
            data.violations.join(', '),
            data.punishment.duration || null,
            'auto-mod'
          );
        }
      });
    }

    // Error handling
    this.bot.on('error', (error) => {
      logger.error('🚨 Bot error:', error);
      
      // Attempt to restart after error
      setTimeout(async () => {
        try {
          logger.info('🔄 Attempting to restart bot after error...');
          await this.stop();
          await this.start();
        } catch (restartError) {
          logger.error('❌ Failed to restart bot:', restartError);
        }
      }, 5000);
    });
  }

  /**
   * Start the bot
   */
  async start(): Promise<void> {
    try {
      logger.info('🚀 Starting FullKickBot...');

      // Initialize database
      await database.initialize();

      // Register commands
      this.registerCommands();

      // Start the bot
      await this.bot.start();

      logger.info('✅ FullKickBot is now fully operational!');
      this.logFeatureStatus();

    } catch (error) {
      logger.error('❌ Failed to start FullKickBot:', error);
      throw error;
    }
  }

  /**
   * Stop the bot
   */
  async stop(): Promise<void> {
    try {
      logger.info('🛑 Stopping FullKickBot...');

      // Stop bot
      await this.bot.stop();

      // Cleanup features
      this.currencySystem?.cleanup();
      this.giveawaySystem?.cleanup();
      this.channelManager.cleanup();

      // Close database
      await database.close();

      logger.info('✅ FullKickBot stopped successfully');
    } catch (error) {
      logger.error('❌ Error stopping FullKickBot:', error);
      throw error;
    }
  }

  /**
   * Register all commands
   */
  private registerCommands(): void {
    // Register default commands
    registerDefaultCommands(this.commandHandler, this.customCommandManager);

    // Register moderation commands
    if (this.moderationManager) {
      registerModerationCommands(this.commandHandler, this.moderationManager);
    }

    // Register advanced feature commands
    registerAdvancedCommands(
      this.commandHandler,
      this.currencySystem,
      this.giveawaySystem
    );

    logger.info('📝 All commands registered successfully');
  }

  /**
   * Log feature status
   */
  private logFeatureStatus(): void {
    logger.info('🔧 Feature Status:');
    logger.info(`   • Custom Commands: ${this.config.features.customCommands ? '✅' : '❌'}`);
    logger.info(`   • Moderation: ${this.config.features.moderation ? '✅' : '❌'}`);
    logger.info(`   • Currency System: ${this.config.features.currency ? '✅' : '❌'}`);
    logger.info(`   • Giveaways: ${this.config.features.giveaways ? '✅' : '❌'}`);
    logger.info(`   • Analytics: ${this.config.features.analytics ? '✅' : '❌'}`);
  }

  /**
   * Get bot statistics
   */
  async getStats(): Promise<any> {
    const botStats = this.bot.getStats();
    const dbStats = await database.getStats();
    const userStats = this.userManager.getCacheStats();
    const channelStats = this.channelManager.getCacheStats();

    const stats = {
      bot: botStats,
      database: dbStats,
      users: userStats,
      channels: channelStats,
      features: {
        customCommands: this.customCommandManager?.getCommandStats(),
        moderation: this.moderationManager?.getModerationStats(),
        currency: this.currencySystem ? await this.currencySystem.getStats() : null,
        giveaways: this.giveawaySystem ? await this.giveawaySystem.getStats() : null,
      },
    };

    return stats;
  }

  /**
   * Get bot instance
   */
  get botInstance(): KickBot {
    return this.bot;
  }

  /**
   * Check if bot is running
   */
  get running(): boolean {
    return this.isRunning;
  }

  /**
   * Get feature managers
   */
  get features() {
    return {
      commands: this.commandHandler,
      customCommands: this.customCommandManager,
      moderation: this.moderationManager,
      currency: this.currencySystem,
      giveaways: this.giveawaySystem,
      users: this.userManager,
      channels: this.channelManager,
    };
  }
}
