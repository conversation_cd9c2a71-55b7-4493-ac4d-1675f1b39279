import { EventEmitter } from 'events';
import { KickBot } from '../bot/KickBot';
export interface ModerationConfig {
    enabled: boolean;
    spamProtection: {
        enabled: boolean;
        maxMessages: number;
        timeWindow: number;
        punishment: 'timeout' | 'ban';
        duration: number;
    };
    capsFilter: {
        enabled: boolean;
        threshold: number;
        minLength: number;
        punishment: 'timeout' | 'delete' | 'warn';
        duration: number;
    };
    bannedWords: {
        enabled: boolean;
        words: string[];
        punishment: 'timeout' | 'ban' | 'delete';
        duration: number;
    };
    linkProtection: {
        enabled: boolean;
        allowedDomains: string[];
        punishment: 'timeout' | 'delete' | 'warn';
        duration: number;
    };
    repetitiveMessages: {
        enabled: boolean;
        maxRepeats: number;
        timeWindow: number;
        punishment: 'timeout' | 'delete';
        duration: number;
    };
    autoTimeout: {
        enabled: boolean;
        escalation: boolean;
    };
}
export interface UserViolation {
    username: string;
    type: string;
    message: string;
    timestamp: Date;
    punishment: string;
    duration?: number;
}
export interface UserModerationData {
    username: string;
    violations: UserViolation[];
    messageHistory: Array<{
        content: string;
        timestamp: Date;
    }>;
    lastMessageTime: Date;
    messageCount: number;
    timeoutCount: number;
    banCount: number;
}
export declare class ModerationManager extends EventEmitter {
    private config;
    private bot;
    private userModerationData;
    private messageHistory;
    constructor(bot: KickBot, config: ModerationConfig);
    private setupBotEvents;
    private processMessage;
    private isPrivilegedUser;
    private updateUserModerationData;
    private checkSpam;
    private checkCapsLock;
    private checkBannedWords;
    private checkLinks;
    private checkRepetitiveMessages;
    private handleViolations;
    private determinePunishment;
    private executePunishment;
    updateConfig(newConfig: Partial<ModerationConfig>): void;
    addBannedWord(word: string): void;
    removeBannedWord(word: string): boolean;
    getUserModerationData(username: string): UserModerationData | undefined;
    getModerationStats(): {
        totalUsers: number;
        totalViolations: number;
        totalTimeouts: number;
        totalBans: number;
        config: ModerationConfig;
        recentMessages: number;
    };
    clearUserData(username: string): boolean;
    exportModerationData(): {
        config: ModerationConfig;
        userData: [string, UserModerationData][];
        messageHistory: {
            username: string;
            content: string;
            timestamp: Date;
        }[];
    };
}
//# sourceMappingURL=ModerationManager.d.ts.map