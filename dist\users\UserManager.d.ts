import { EventEmitter } from 'events';
import { KickUser, PermissionLevel, UserStats } from '../types';
export interface UserCache {
    [username: string]: {
        user: KickUser;
        lastSeen: Date;
        messageCount: number;
        commandCount: number;
        timeInChat: number;
        points: number;
    };
}
export declare class UserManager extends EventEmitter {
    private userCache;
    private moderators;
    private vips;
    private subscribers;
    private followers;
    private bannedUsers;
    constructor();
    updateUser(user: KickUser): void;
    private updateUserPermissions;
    getUser(username: string): Promise<KickUser | null>;
    getUserPermissionLevel(user: KickUser | string): PermissionLevel;
    hasPermission(user: KickUser | string, requiredLevel: PermissionLevel): boolean;
    incrementMessageCount(username: string): void;
    incrementCommandCount(username: string): void;
    addPoints(username: string, points: number): void;
    removePoints(username: string, points: number): boolean;
    getUserStats(username: string): UserStats | null;
    isUserActive(username: string): boolean;
    getModerators(): string[];
    getVips(): string[];
    getSubscribers(): string[];
    getFollowers(): string[];
    getBannedUsers(): string[];
    getActiveUsers(): string[];
    getTopUsersByPoints(limit?: number): Array<{
        username: string;
        points: number;
    }>;
    getTopUsersByMessages(limit?: number): Array<{
        username: string;
        messages: number;
    }>;
    clearInactiveUsers(): number;
    getCacheStats(): {
        totalUsers: number;
        activeUsers: number;
        moderators: number;
        vips: number;
        subscribers: number;
        followers: number;
        bannedUsers: number;
    };
    exportUserData(): UserCache;
    importUserData(data: UserCache): void;
}
//# sourceMappingURL=UserManager.d.ts.map