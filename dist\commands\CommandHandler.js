"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommandHandler = void 0;
const events_1 = require("events");
const types_1 = require("../types");
const logger_1 = require("../utils/logger");
class CommandHandler extends events_1.EventEmitter {
    constructor(bot) {
        super();
        this.bot = bot;
        this.commands = new Map();
        this.commandFunctions = new Map();
        this.cooldowns = new Map();
        this.setupBotEvents();
    }
    setupBotEvents() {
        this.bot.on('command', async (commandData) => {
            await this.handleCommand(commandData);
        });
    }
    registerCommand(command, handler) {
        this.commands.set(command.name, command);
        this.commandFunctions.set(command.name, handler);
        for (const alias of command.aliases) {
            this.commands.set(alias, command);
            this.commandFunctions.set(alias, handler);
        }
        logger_1.logger.info(`Registered command: ${command.name} (aliases: ${command.aliases.join(', ')})`);
    }
    unregisterCommand(commandName) {
        const command = this.commands.get(commandName);
        if (!command)
            return false;
        this.commands.delete(command.name);
        this.commandFunctions.delete(command.name);
        for (const alias of command.aliases) {
            this.commands.delete(alias);
            this.commandFunctions.delete(alias);
        }
        this.cooldowns.delete(command.name);
        logger_1.logger.info(`Unregistered command: ${command.name}`);
        return true;
    }
    async handleCommand(commandData) {
        const { name, args, message, user } = commandData;
        const command = this.commands.get(name);
        if (!command) {
            logger_1.logger.debug(`Unknown command: ${name}`);
            return;
        }
        if (!command.enabled) {
            logger_1.logger.debug(`Command disabled: ${name}`);
            return;
        }
        if (!this.bot.hasPermission(user, command.permission)) {
            await this.bot.replyToMessage(message, 'You do not have permission to use this command.');
            return;
        }
        if (this.isOnCooldown(command.name, user.username)) {
            const remainingTime = this.getRemainingCooldown(command.name, user.username);
            await this.bot.replyToMessage(message, `Command is on cooldown. Please wait ${remainingTime} seconds.`);
            return;
        }
        try {
            const context = {
                bot: this.bot,
                message,
                args,
                user,
                command,
            };
            const handler = this.commandFunctions.get(name);
            if (handler) {
                await handler(context);
                this.setCooldown(command.name, user.username, command.cooldown);
                this.emit('command_executed', {
                    command: command.name,
                    user: user.username,
                    args,
                    success: true,
                });
            }
        }
        catch (error) {
            logger_1.logger.error(`Error executing command ${name}:`, error);
            await this.bot.replyToMessage(message, 'An error occurred while executing the command.');
            this.emit('command_error', {
                command: command.name,
                user: user.username,
                error,
            });
        }
    }
    isOnCooldown(commandName, username) {
        const commandCooldowns = this.cooldowns.get(commandName);
        if (!commandCooldowns)
            return false;
        const userCooldown = commandCooldowns.get(username);
        if (!userCooldown)
            return false;
        return Date.now() < userCooldown;
    }
    getRemainingCooldown(commandName, username) {
        const commandCooldowns = this.cooldowns.get(commandName);
        if (!commandCooldowns)
            return 0;
        const userCooldown = commandCooldowns.get(username);
        if (!userCooldown)
            return 0;
        const remaining = Math.ceil((userCooldown - Date.now()) / 1000);
        return Math.max(0, remaining);
    }
    setCooldown(commandName, username, cooldownSeconds) {
        if (cooldownSeconds <= 0)
            return;
        if (!this.cooldowns.has(commandName)) {
            this.cooldowns.set(commandName, new Map());
        }
        const commandCooldowns = this.cooldowns.get(commandName);
        const cooldownEnd = Date.now() + (cooldownSeconds * 1000);
        commandCooldowns.set(username, cooldownEnd);
        setTimeout(() => {
            commandCooldowns.delete(username);
            if (commandCooldowns.size === 0) {
                this.cooldowns.delete(commandName);
            }
        }, cooldownSeconds * 1000);
    }
    getCommands() {
        const uniqueCommands = new Map();
        for (const [name, command] of this.commands) {
            if (name === command.name) {
                uniqueCommands.set(name, command);
            }
        }
        return Array.from(uniqueCommands.values());
    }
    getCommand(name) {
        return this.commands.get(name);
    }
    getCommandsByPermission(permissionLevel) {
        return this.getCommands().filter(cmd => cmd.permission <= permissionLevel);
    }
    updateCommand(name, updates) {
        const command = this.commands.get(name);
        if (!command)
            return false;
        Object.assign(command, updates);
        this.commands.set(command.name, command);
        for (const alias of command.aliases) {
            this.commands.set(alias, command);
        }
        logger_1.logger.info(`Updated command: ${name}`);
        return true;
    }
    clearCooldowns(commandName) {
        this.cooldowns.delete(commandName);
        logger_1.logger.info(`Cleared cooldowns for command: ${commandName}`);
    }
    clearUserCooldowns(username) {
        for (const [commandName, commandCooldowns] of this.cooldowns) {
            commandCooldowns.delete(username);
            if (commandCooldowns.size === 0) {
                this.cooldowns.delete(commandName);
            }
        }
        logger_1.logger.info(`Cleared all cooldowns for user: ${username}`);
    }
    getCommandStats() {
        const commands = this.getCommands();
        const stats = {
            totalCommands: commands.length,
            enabledCommands: commands.filter(cmd => cmd.enabled).length,
            disabledCommands: commands.filter(cmd => !cmd.enabled).length,
            commandsByPermission: {
                [types_1.PermissionLevel.EVERYONE]: commands.filter(cmd => cmd.permission === types_1.PermissionLevel.EVERYONE).length,
                [types_1.PermissionLevel.FOLLOWER]: commands.filter(cmd => cmd.permission === types_1.PermissionLevel.FOLLOWER).length,
                [types_1.PermissionLevel.SUBSCRIBER]: commands.filter(cmd => cmd.permission === types_1.PermissionLevel.SUBSCRIBER).length,
                [types_1.PermissionLevel.VIP]: commands.filter(cmd => cmd.permission === types_1.PermissionLevel.VIP).length,
                [types_1.PermissionLevel.MODERATOR]: commands.filter(cmd => cmd.permission === types_1.PermissionLevel.MODERATOR).length,
                [types_1.PermissionLevel.OWNER]: commands.filter(cmd => cmd.permission === types_1.PermissionLevel.OWNER).length,
            },
            activeCooldowns: this.cooldowns.size,
        };
        return stats;
    }
}
exports.CommandHandler = CommandHandler;
//# sourceMappingURL=CommandHandler.js.map