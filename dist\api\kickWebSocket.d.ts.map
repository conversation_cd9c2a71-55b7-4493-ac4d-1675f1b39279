{"version": 3, "file": "kickWebSocket.d.ts", "sourceRoot": "", "sources": ["../../src/api/kickWebSocket.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAGtC,OAAO,EAAe,QAAQ,EAAoB,MAAM,UAAU,CAAC;AAEnE,MAAM,WAAW,aAAa;IAC5B,KAAK,EAAE,MAAM,CAAC;IACd,IAAI,EAAE,MAAM,CAAC;IACb,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAED,MAAM,WAAW,eAAe;IAC9B,EAAE,EAAE,MAAM,CAAC;IACX,WAAW,EAAE,MAAM,CAAC;IACpB,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,EAAE,MAAM,CAAC;IACb,UAAU,EAAE,MAAM,CAAC;IACnB,MAAM,EAAE,QAAQ,CAAC;IACjB,QAAQ,CAAC,EAAE,GAAG,CAAC;CAChB;AAED,qBAAa,aAAc,SAAQ,YAAY;IAC7C,OAAO,CAAC,EAAE,CAAC,CAAY;IACvB,OAAO,CAAC,SAAS,CAAC,CAAS;IAC3B,OAAO,CAAC,UAAU,CAAC,CAAS;IAC5B,OAAO,CAAC,WAAW,CAAS;IAC5B,OAAO,CAAC,iBAAiB,CAAK;IAC9B,OAAO,CAAC,oBAAoB,CAAK;IACjC,OAAO,CAAC,cAAc,CAAQ;IAC9B,OAAO,CAAC,iBAAiB,CAAC,CAAiB;IAC3C,OAAO,CAAC,iBAAiB,CAAC,CAAiB;;IASrC,OAAO,CAAC,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAoFnE,OAAO,CAAC,qBAAqB;IAgB7B,OAAO,CAAC,kBAAkB;IAqB1B,OAAO,CAAC,aAAa;IAoDrB,OAAO,CAAC,iBAAiB;IAwBzB,OAAO,CAAC,gBAAgB;IAaxB,OAAO,CAAC,kBAAkB;IAa1B,OAAO,CAAC,sBAAsB;IAa9B,OAAO,CAAC,kBAAkB;IAa1B,OAAO,CAAC,qBAAqB;IAa7B,OAAO,CAAC,IAAI;IAWZ,OAAO,CAAC,cAAc;IAWtB,OAAO,CAAC,iBAAiB;IAkBzB,OAAO,CAAC,OAAO;IAef,UAAU,IAAI,IAAI;IAclB,IAAI,SAAS,IAAI,OAAO,CAEvB;CACF"}