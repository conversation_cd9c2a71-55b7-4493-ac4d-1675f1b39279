import { EventEmitter } from 'events';
import { KickUser } from '../types';
export interface PusherMessage {
    event: string;
    data: string;
    channel?: string;
}
export interface ChatMessageData {
    id: string;
    chatroom_id: number;
    content: string;
    type: string;
    created_at: string;
    sender: KickUser;
    metadata?: any;
}
export declare class KickWebSocket extends EventEmitter {
    private ws?;
    private channelId?;
    private chatroomId?;
    private isConnected;
    private reconnectAttempts;
    private maxReconnectAttempts;
    private reconnectDelay;
    private heartbeatInterval?;
    private connectionTimeout?;
    constructor();
    connect(channelId: number, chatroomId: number): Promise<void>;
    private sendConnectionMessage;
    private subscribeToChannel;
    private handleMessage;
    private handleChatMessage;
    private handleUserBanned;
    private handleUserUnbanned;
    private handleFollowersUpdated;
    private handleStreamerLive;
    private handleStreamerOffline;
    private send;
    private startHeartbeat;
    private scheduleReconnect;
    private cleanup;
    disconnect(): void;
    get connected(): boolean;
}
//# sourceMappingURL=kickWebSocket.d.ts.map