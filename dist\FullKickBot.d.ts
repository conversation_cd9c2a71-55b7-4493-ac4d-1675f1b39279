import { KickBot } from './bot/KickBot';
import { CommandHandler } from './commands/CommandHandler';
import { CustomCommandManager } from './commands/CustomCommandManager';
import { ModerationManager } from './moderation/ModerationManager';
import { CurrencySystem } from './features/CurrencySystem';
import { GiveawaySystem } from './features/GiveawaySystem';
import { UserManager } from './users/UserManager';
import { ChannelManager } from './channels/ChannelManager';
export interface FullKickBotConfig {
    bot: {
        username: string;
        password?: string;
        token?: string;
        channel: string;
        prefix: string;
    };
    features: {
        moderation: boolean;
        customCommands: boolean;
        currency: boolean;
        giveaways: boolean;
        analytics: boolean;
        webDashboard: boolean;
    };
    currency: {
        enabled: boolean;
        currencyName: string;
        currencySymbol: string;
        passiveEarning: {
            enabled: boolean;
            amount: number;
            interval: number;
            requireActive: boolean;
        };
        messageRewards: {
            enabled: boolean;
            amount: number;
            cooldown: number;
        };
        followRewards: {
            enabled: boolean;
            amount: number;
        };
        subscribeRewards: {
            enabled: boolean;
            amount: number;
        };
        dailyBonus: {
            enabled: boolean;
            amount: number;
            streakMultiplier: number;
        };
    };
    moderation: {
        enabled: boolean;
        spamProtection: {
            enabled: boolean;
            maxMessages: number;
            timeWindow: number;
            punishment: 'timeout' | 'ban';
            duration: number;
        };
        capsFilter: {
            enabled: boolean;
            threshold: number;
            minLength: number;
            punishment: 'timeout' | 'delete' | 'warn';
            duration: number;
        };
        bannedWords: {
            enabled: boolean;
            words: string[];
            punishment: 'timeout' | 'ban' | 'delete';
            duration: number;
        };
        linkProtection: {
            enabled: boolean;
            allowedDomains: string[];
            punishment: 'timeout' | 'delete' | 'warn';
            duration: number;
        };
        repetitiveMessages: {
            enabled: boolean;
            maxRepeats: number;
            timeWindow: number;
            punishment: 'timeout' | 'delete';
            duration: number;
        };
        autoTimeout: {
            enabled: boolean;
            escalation: boolean;
        };
    };
}
export declare class FullKickBot {
    private bot;
    private commandHandler;
    private customCommandManager?;
    private moderationManager?;
    private currencySystem?;
    private giveawaySystem?;
    private userManager;
    private channelManager;
    private webServer?;
    private config;
    private isRunning;
    constructor(config: FullKickBotConfig);
    private initializeFeatures;
    private setupEventHandlers;
    start(): Promise<void>;
    stop(): Promise<void>;
    private registerCommands;
    private logFeatureStatus;
    getStats(): Promise<any>;
    get botInstance(): KickBot;
    get running(): boolean;
    get features(): {
        commands: CommandHandler;
        customCommands: CustomCommandManager | undefined;
        moderation: ModerationManager | undefined;
        currency: CurrencySystem | undefined;
        giveaways: GiveawaySystem | undefined;
        users: UserManager;
        channels: ChannelManager;
    };
}
//# sourceMappingURL=FullKickBot.d.ts.map