"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = exports.LogLevel = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const config_1 = require("../config");
var LogLevel;
(function (LogLevel) {
    LogLevel[LogLevel["ERROR"] = 0] = "ERROR";
    LogLevel[LogLevel["WARN"] = 1] = "WARN";
    LogLevel[LogLevel["INFO"] = 2] = "INFO";
    LogLevel[LogLevel["DEBUG"] = 3] = "DEBUG";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
class Logger {
    constructor() {
        this.logLevel = this.getLogLevel(config_1.logConfig.level);
        this.logFile = config_1.logConfig.file;
        this.ensureLogDirectory();
    }
    getLogLevel(level) {
        switch (level.toLowerCase()) {
            case 'error': return LogLevel.ERROR;
            case 'warn': return LogLevel.WARN;
            case 'info': return LogLevel.INFO;
            case 'debug': return LogLevel.DEBUG;
            default: return LogLevel.INFO;
        }
    }
    ensureLogDirectory() {
        const logDir = path_1.default.dirname(this.logFile);
        if (!fs_1.default.existsSync(logDir)) {
            fs_1.default.mkdirSync(logDir, { recursive: true });
        }
    }
    formatMessage(level, message, data) {
        const timestamp = new Date().toISOString();
        const dataStr = data ? ` ${JSON.stringify(data)}` : '';
        return `[${timestamp}] [${level}] ${message}${dataStr}`;
    }
    writeToFile(message) {
        try {
            fs_1.default.appendFileSync(this.logFile, message + '\n');
        }
        catch (error) {
            console.error('Failed to write to log file:', error);
        }
    }
    log(level, levelName, message, data) {
        if (level <= this.logLevel) {
            const formattedMessage = this.formatMessage(levelName, message, data);
            const colors = {
                ERROR: '\x1b[31m',
                WARN: '\x1b[33m',
                INFO: '\x1b[36m',
                DEBUG: '\x1b[37m',
                RESET: '\x1b[0m'
            };
            console.log(`${colors[levelName]}${formattedMessage}${colors.RESET}`);
            this.writeToFile(formattedMessage);
        }
    }
    error(message, data) {
        this.log(LogLevel.ERROR, 'ERROR', message, data);
    }
    warn(message, data) {
        this.log(LogLevel.WARN, 'WARN', message, data);
    }
    info(message, data) {
        this.log(LogLevel.INFO, 'INFO', message, data);
    }
    debug(message, data) {
        this.log(LogLevel.DEBUG, 'DEBUG', message, data);
    }
    chatMessage(username, message, channel) {
        this.info(`[${channel}] ${username}: ${message}`);
    }
    commandUsed(username, command, args, channel) {
        this.info(`[${channel}] ${username} used command: ${command} ${args.join(' ')}`);
    }
    moderationAction(action, target, moderator, reason) {
        const reasonStr = reason ? ` (${reason})` : '';
        this.info(`Moderation: ${moderator} ${action} ${target}${reasonStr}`);
    }
    botEvent(event, data) {
        this.info(`Bot Event: ${event}`, data);
    }
}
exports.logger = new Logger();
//# sourceMappingURL=logger.js.map